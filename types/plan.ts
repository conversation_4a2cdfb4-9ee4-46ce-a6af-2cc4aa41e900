

// types/plan.ts

// export interface Plan {
//   _id: string; // Add this line
//   title: string;
//   priceRange: string;
//   percentage: string;
//   profit: string;
//   capitalReturn: string;
//   totalPercentage: string;
//   // Add any other properties your Plan has
// }



// export interface Plan {
//   _id: string;
//   title: string;
//   priceRange: string;
//   percentage: string;
//   profit: string;
//   capitalReturn: string;
//   totalPercentage: string;
//   duration: number; // Duration in months
//   // amount: number;
//   status: "active" | "inactive" | "archived";
//   // iconUrl?: ImageSourcePropType; // Optional: URL for plan icon
//   // planImageUrl?: ImageSourcePropType; // Optional: Image URL for the plan representation
// }



export interface Plan {
  _id: string;
  title: string;
  priceRange: string;
  percentage: string;
  profit: string;
  capitalReturn: string;
  totalPercentage: string;
  duration: number; // Duration in months
  status: "active" | "inactive" | "archived";
  planType?: "normal" | "promotional"; // New optional property with default value "normal"
}

