#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const mode = process.argv[2];

if (!mode || !['strict', 'relaxed', 'production'].includes(mode)) {
  console.log('Usage: node scripts/typescript-mode.js [strict|relaxed|production]');
  console.log('');
  console.log('strict     - Enable strict TypeScript checking (development)');
  console.log('relaxed    - Disable strict TypeScript checking');
  console.log('production - Use production TypeScript config');
  process.exit(1);
}

const configMap = {
  strict: 'tsconfig.dev.json',
  relaxed: 'tsconfig.prod.json',
  production: 'tsconfig.prod.json'
};

const sourceConfig = configMap[mode];
const targetConfig = 'tsconfig.json';

try {
  // Read the source config
  const sourceContent = fs.readFileSync(sourceConfig, 'utf8');
  const sourceData = JSON.parse(sourceContent);

  // Read the current config (base config)
  const currentContent = fs.readFileSync(targetConfig, 'utf8');
  const currentData = JSON.parse(currentContent);

  // Create a backup of the original base config
  const backupPath = 'tsconfig.backup.json';
  if (!fs.existsSync(backupPath)) {
    fs.writeFileSync(backupPath, currentContent);
  }

  // Apply the compiler options from the source config
  currentData.compilerOptions = {
    ...currentData.compilerOptions,
    ...sourceData.compilerOptions
  };

  // Write back to tsconfig.json
  fs.writeFileSync(targetConfig, JSON.stringify(currentData, null, 2) + '\n');
  
  console.log(`✅ TypeScript mode switched to: ${mode}`);
  console.log(`📝 Updated ${targetConfig} with settings from ${sourceConfig}`);
  
  if (mode === 'strict') {
    console.log('🔍 Strict TypeScript checking is now enabled for development');
  } else {
    console.log('🚀 Relaxed TypeScript checking is now enabled for easier deployment');
  }
  
} catch (error) {
  console.error('❌ Error switching TypeScript mode:', error.message);
  process.exit(1);
}
