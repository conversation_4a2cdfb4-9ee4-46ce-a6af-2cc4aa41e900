import { connectToDatabase } from '../../lib/dbconnect';
import { User } from '../../models/User';

async function migrateUsers() {
  try {
    await connectToDatabase();
    console.log('Connected to database. Starting migration...');

    const users = await User.find({});
    console.log(`Found ${users.length} users to migrate.`);

    let migratedCount = 0;

    for (const user of users) {
      let updated = false;

      // Add rememberMe field if it doesn't exist
      if (user.rememberMe === undefined) {
        user.rememberMe = false;
        updated = true;
      }

      // Convert refreshTokenHash to refreshToken if necessary
      if (user.refreshTokenHash && !user.refreshToken) {
        user.refreshToken = user.refreshTokenHash;
        user.refreshTokenHash = undefined;
        updated = true;
      }

      if (updated) {
        await user.save();
        migratedCount++;
      }
    }

    console.log(`Migration completed. ${migratedCount} users were updated.`);
  } catch (error) {
    console.error('Error during user migration:', error);
  } finally {
    process.exit();
  }
}

migrateUsers();

