// app/layout.tsx

'use client';

import { usePathname } from 'next/navigation';
import localFont from 'next/font/local';
import './globals.css';
import { AuthProvider } from '@/context/AuthContext';
import { ThemeProvider } from '@/context/ThemeContext';
import { LoadingProvider } from '@/context/LoadingContext';
import Footer from '@/components/home/<USER>';

const geistSans = localFont({
  src: './fonts/GeistVF.woff',
  variable: '--font-geist-sans',
  weight: '100 900',
});
const geistMono = localFont({
  src: './fonts/GeistMonoVF.woff',
  variable: '--font-geist-mono',
  weight: '100 900',
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine if the Footer should be shown
  const showFooter =
    pathname !== '/register' &&
    !pathname.startsWith('/dashboard') &&
    !pathname.startsWith('/admin');

  return (
    <html lang="en">
      <head>
        <title>TGM Finance - Premium Investment & Wealth Management Platform</title>
        <meta name="description" content="TGM Finance is a cutting-edge financial technology platform offering sophisticated investment solutions, wealth management services, and premium financial advisory for modern investors." />
        <meta name="keywords" content="TGM Finance, investment platform, wealth management, financial advisory, fintech, premium investments, portfolio management, financial planning" />
        <meta name="author" content="TGM Finance Team" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta property="og:title" content="TGM Finance - Premium Investment & Wealth Management" />
        <meta property="og:description" content="Transform your financial future with TGM Finance's sophisticated investment platform and expert wealth management services." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://tgmfinance.com" />
        <meta property="og:site_name" content="TGM Finance" />
        <meta property="og:image" content="/og-image.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="TGM Finance - Premium Investment Platform" />
        <meta name="twitter:description" content="Sophisticated investment solutions for modern investors" />
        <meta name="twitter:image" content="/twitter-image.jpg" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider>
          <LoadingProvider>
            <AuthProvider>{children}</AuthProvider>
            {showFooter && <Footer />}
          </LoadingProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
