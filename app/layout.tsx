

// // app/layout.tsx

// 'use client';

// import { usePathname } from 'next/navigation';
// import localFont from 'next/font/local';
// import './globals.css';
// import { AuthProvider } from '@/context/AuthContext';
// import { ThemeProvider } from '@/context/ThemeContext';
// import Footer from '@/components/home/<USER>';

// const geistSans = localFont({
//   src: './fonts/GeistVF.woff',
//   variable: '--font-geist-sans',
//   weight: '100 900',
// });
// const geistMono = localFont({
//   src: './fonts/GeistMonoVF.woff',
//   variable: '--font-geist-mono',
//   weight: '100 900',
// });

// export default function RootLayout({
//   children,
// }: {
//   children: React.ReactNode;
// }) {
//   const pathname = usePathname();
//   const showFooter = pathname !== '/register';

//   return (
//     <html lang="en">
//       <body
//         className={`${geistSans.variable} ${geistMono.variable} antialiased`}
//       >
//         <ThemeProvider>
//           <AuthProvider>{children}</AuthProvider>
//           {showFooter && <Footer />}
//         </ThemeProvider>
//       </body>
//     </html>
//   );
// }





// app/layout.tsx

'use client';

import { usePathname } from 'next/navigation';
import localFont from 'next/font/local';
import './globals.css';
import { AuthProvider } from '@/context/AuthContext';
import { ThemeProvider } from '@/context/ThemeContext';
import { LoadingProvider } from '@/context/LoadingContext';
import Footer from '@/components/home/<USER>';

const geistSans = localFont({
  src: './fonts/GeistVF.woff',
  variable: '--font-geist-sans',
  weight: '100 900',
});
const geistMono = localFont({
  src: './fonts/GeistMonoVF.woff',
  variable: '--font-geist-mono',
  weight: '100 900',
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine if the Footer should be shown
  const showFooter =
    pathname !== '/register' &&
    !pathname.startsWith('/dashboard') &&
    !pathname.startsWith('/admin');

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider>
          <LoadingProvider>
            <AuthProvider>{children}</AuthProvider>
            {showFooter && <Footer />}
          </LoadingProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
