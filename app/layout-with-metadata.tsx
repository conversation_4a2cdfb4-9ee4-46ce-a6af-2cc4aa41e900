import type { Metadata } from 'next';
import RootLayout from './layout';

export const metadata: Metadata = {
  title: 'TGM Finance - Premium Investment & Wealth Management Platform',
  description: 'TGM Finance is a cutting-edge financial technology platform offering sophisticated investment solutions, wealth management services, and premium financial advisory for modern investors.',
  keywords: 'TGM Finance, investment platform, wealth management, financial advisory, fintech, premium investments, portfolio management, financial planning',
  authors: [{ name: 'TGM Finance Team' }],
  creator: 'TGM Finance',
  publisher: 'TGM Finance',
  robots: 'index, follow',
  openGraph: {
    title: 'TGM Finance - Premium Investment & Wealth Management',
    description: 'Transform your financial future with TGM Finance\'s sophisticated investment platform and expert wealth management services.',
    url: 'https://tgmfinance.com',
    siteName: 'TGM Finance',
    type: 'website',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'TGM Finance - Premium Investment Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TGM Finance - Premium Investment Platform',
    description: 'Sophisticated investment solutions for modern investors',
    images: ['/twitter-image.jpg'],
  },
};

export default function LayoutWithMetadata({
  children,
}: {
  children: React.ReactNode;
}) {
  return <RootLayout>{children}</RootLayout>;
}

