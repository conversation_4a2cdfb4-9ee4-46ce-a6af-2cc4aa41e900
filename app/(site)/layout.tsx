
'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import StickyNav from '@/components/navigation/sticky-nav';

export default function SiteLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const showNav = pathname !== '/register';

  return (
    <>
      {showNav && <StickyNav />}
      <main className={showNav ? "pt-16" : ""}>{children}</main>
    </>
  );
}

