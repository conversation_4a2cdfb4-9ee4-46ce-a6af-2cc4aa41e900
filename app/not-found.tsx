// import Link from 'next/link'
// import { Home } from 'lucide-react'

// export default function NotFound() {
//   return (
//     <div className="min-h-screen bg-gray-100 flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8">
//       <div className="max-w-md w-full space-y-8 text-center">
//         <div className="space-y-4">
//           <h1 className="text-6xl font-extrabold text-amber-600">404</h1>
//           <h2 className="text-3xl font-bold text-gray-900">Page Not Found</h2>
//           <p className="text-xl text-gray-600">Oops! The page you're looking for doesn't exist.</p>
//         </div>
//         <div className="mt-8">
//           <svg
//             className="mx-auto h-32 w-32 text-amber-500"
//             fill="none"
//             viewBox="0 0 24 24"
//             stroke="currentColor"
//             aria-hidden="true"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               strokeWidth={2}
//               d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
//             />
//           </svg>
//         </div>
//         <div className="mt-8">
//           <Link
//             href="/"
//             className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
//           >
//             <Home className="mr-2 -ml-1 h-5 w-5" aria-hidden="true" />
//             Back to Home
//           </Link>
//         </div>
//       </div>
//     </div>
//   )
// }




import Link from 'next/link'
import Image from 'next/image'
import { Home, Download } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-100 flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        {/* App Download Banner */}
        <div className="bg-amber-100 border-l-4 border-amber-500 text-amber-700 p-4 mb-8" role="alert">
          <p className="font-bold">Have you downloaded our app?</p>
          <p>Get the TGM App for the best investment experience!</p>
          <Link
            href="/appdownload"
            className="inline-flex items-center px-4 py-2 mt-3 border border-transparent text-sm font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
          >
            <Download className="mr-2 -ml-1 h-4 w-4" aria-hidden="true" />
            Download Now
          </Link>
        </div>

        <div className="space-y-4">
          <h1 className="text-6xl font-extrabold text-amber-600">404</h1>
          <h2 className="text-3xl font-bold text-gray-900">Page Not Found</h2>
          <p className="text-xl text-gray-600">Oops! The page you&apos;re looking for doesn&apos;t exist.</p>
        </div>

        <div className="mt-8">
          <svg
            className="mx-auto h-24 w-24 text-amber-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <div className="mt-8">
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
          >
            <Home className="mr-2 -ml-1 h-5 w-5" aria-hidden="true" />
            Back to Home
          </Link>
        </div>
        {/* App Screenshot */}
        <div className="mt-8">
          <Image
            src="/appscreens/ourapp.png"
            alt="TGM App Screenshot"
            width={400}
            height={500}
            className="mx-auto rounded-lg "
          />
        </div>

        

       
      </div>
    </div>
  )
}

