// 'use client';

// import React from 'react';

// export default function DashboardHomePage() {
//   return (
    // <div className="bg-white p-6 rounded-lg shadow-md">
    //   <h1 className="text-2xl font-bold mb-4">Welcome to your dashboard!</h1>
    //   <p>Here is an overview of your account.</p>
    // </div>
//   );
// }
// //

'use client';

import PaymentsTable from '@/components/dashboard/PaymentsTable';
import { PayoutSkeleton } from '@/components/ui/DashboardSkeleton';
import React, { useState, useEffect } from 'react';

export default function UserPayments() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadPayoutData = async () => {
      // Simulate loading payout data
      await new Promise(resolve => setTimeout(resolve, 1400));
      setIsLoading(false);
    };

    loadPayoutData();
  }, []);

  if (isLoading) {
    return <PayoutSkeleton />;
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-4">Payouts</h1>
        <p>Track your investment payouts and earnings.</p>
      </div>

      <PaymentsTable />
    </div>
  );
}
