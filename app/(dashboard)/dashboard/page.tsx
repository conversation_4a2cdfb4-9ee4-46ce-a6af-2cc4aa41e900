// app/(dashboard)/dashboard/page.tsx
'use client';

import InvestmentTable from '@/components/dashboard/UserInvestmentTable';
import DashboardSkeleton from '@/components/ui/DashboardSkeleton';
import React, { useEffect, useState } from 'react';

export default function DashboardHomePage() {
  const [isLoading, setIsLoading] = useState(true);
  const [, setInvestmentData] = useState(null);

  useEffect(() => {
    const loadDashboardData = async () => {
      // Simulate loading dashboard data
      await new Promise(resolve => setTimeout(resolve, 1500));
      setInvestmentData({}); // Set actual data here
      setIsLoading(false);
    };

    loadDashboardData();
  }, []);

  if (isLoading) {
    return <DashboardSkeleton />;
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-4">Welcome to your dashboard!</h1>
        <p>Here is an overview of your account.</p>
      </div>

      <InvestmentTable />
    </div>
  );
}


