'use server'

import { redirect } from 'next/navigation'

type InvestmentPackage = {
  id: string
  name: string
  price: number
}

const investmentPackages: InvestmentPackage[] = [
  { id: 'basic', name: 'Basic Plan', price: 100 },
  { id: 'standard', name: 'Standard Plan', price: 250 },
  { id: 'premium', name: 'Premium Plan', price: 500 },
]

export async function selectInvestmentPackage(formData: FormData) {
  const packageId = formData.get('packageId') as string
  const selectedPackage = investmentPackages.find(pkg => pkg.id === packageId)

  if (!selectedPackage) {
    throw new Error('Invalid package selected')
  }

  // Here you would typically store this in a database
  // For this example, we'll just simulate storage and redirect
  console.log('Storing package:', selectedPackage)

  // Redirect to the confirmation page with the package ID
  redirect(`/onboarding/confirm/${packageId}`)
}