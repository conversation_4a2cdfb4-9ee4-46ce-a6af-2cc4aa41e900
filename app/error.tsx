'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import { AlertOctagon, Home } from 'lucide-react'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error(error)
  }, [error])

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        <div className="space-y-4">
          <AlertOctagon className="mx-auto h-24 w-24 text-amber-600" />
          <h1 className="text-3xl font-bold text-gray-900">Oops! Something went wrong</h1>
          <p className="text-xl text-gray-600">We&apos;re sorry, but we encountered an unexpected error.</p>
        </div>
        <div className="mt-8 space-y-4">
          <button
            onClick={() => reset()}
            className="w-full flex justify-center items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
          >
            Try again
          </button>
          <Link
            href="/"
            className="w-full flex justify-center items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-amber-700 bg-amber-100 hover:bg-amber-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
          >
            <Home className="mr-2 -ml-1 h-5 w-5" aria-hidden="true" />
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  )
}

