// // app/(admin)/all-referrals/page.tsx
// 'use client';

// import AdminReferrals from '@/components/admin/referrals/AdminReferrals';
// // import { ReferralLink } from '@/components/admin/referrals/ReferralDashboard';
// // import ReferralDashboard from '@/components/admin/referrals/ReferralDashboard';
// import React from 'react';

// export default function AllReferrals() {
//   return (
//     <> 
//       <div className="bg-white p-6 rounded-lg shadow-md">
//         <h1 className="text-2xl font-bold mb-4">All Client Referrals</h1>
//         <p>Manage All user Referrals.</p>
//     </div>
//      {/* <ReferralDashboard/> */}
//      <AdminReferrals/>
//      {/* <ReferralLink/> */}
//     </>
    
//   );
// }



'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, Plus, BarChart3, TrendingUp, Users, DollarSign, Activity } from 'lucide-react';
import ReferralStats from '@/components/admin/ReferralStats';
import ModernReferralTable from '@/components/admin/referrals/ModernReferralTable';
import { LoadingWrapper } from '@/components/ui/loading-wrapper';
import { ReferralManagementSkeleton } from '@/components/ui/skeletons';

export default function AllReferrals() {
  const [isLoading, setIsLoading] = useState(true);
  const [, setReferralData] = useState<any>(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    const loadReferralData = async () => {
      // Simulate loading referral data
      await new Promise(resolve => setTimeout(resolve, 2200));
      setReferralData({}); // Set actual data here
      setIsLoading(false);
    };

    loadReferralData();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  if (isLoading) {
    return <ReferralManagementSkeleton />;
  }

  return (
    <div className="space-y-8">
      {/* Modern Page Header */}
      <div className="relative">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 via-blue-600/5 to-green-600/5 rounded-3xl"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(147,51,234,0.1),transparent_50%)]"></div>

        <Card className="relative border-0 shadow-xl bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm">
          <CardHeader className="pb-8">
            <div className="flex items-start justify-between">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl shadow-lg">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                      Referral Management
                    </CardTitle>
                    <CardDescription className="text-lg mt-1">
                      Comprehensive referral tracking and analytics dashboard
                    </CardDescription>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    disabled={refreshing}
                    className="bg-white/50 hover:bg-white/80 border-slate-200 dark:bg-slate-800/50 dark:hover:bg-slate-800/80 dark:border-slate-700"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-white/50 hover:bg-white/80 border-slate-200 dark:bg-slate-800/50 dark:hover:bg-slate-800/80 dark:border-slate-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    New Referral
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-white/50 hover:bg-white/80 border-slate-200 dark:bg-slate-800/50 dark:hover:bg-slate-800/80 dark:border-slate-700"
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Analytics
                  </Button>
                </div>
              </div>

              {/* Live Status Indicator */}
              <div className="flex items-center space-x-2 bg-purple-50 dark:bg-purple-900/20 px-4 py-2 rounded-full border border-purple-200 dark:border-purple-800">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-purple-700 dark:text-purple-400">
                  Live Data
                </span>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      {/* Enhanced Statistics Overview */}
      <LoadingWrapper
        isLoading={false}
        loadingText="Loading referral statistics..."
        loadingVariant="skeleton"
      >
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-blue-500/5 to-green-500/5 rounded-2xl"></div>
          <ReferralStats />
        </div>
      </LoadingWrapper>

      {/* Modern Referral Table */}
      <LoadingWrapper
        isLoading={false}
        loadingText="Loading referral data..."
        loadingVariant="skeleton"
      >
        <Card className="border-0 shadow-xl bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm">
          <CardHeader className="border-b border-slate-200 dark:border-slate-700 bg-gradient-to-r from-slate-50 to-white dark:from-slate-800 dark:to-slate-900">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl font-semibold flex items-center">
                  <Users className="h-5 w-5 mr-2 text-purple-600" />
                  Referral Database
                </CardTitle>
                <CardDescription>
                  Complete overview of all referrals and their performance
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1 text-sm text-slate-600 dark:text-slate-400">
                  <Activity className="h-4 w-4" />
                  <span>Real-time updates</span>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <ModernReferralTable />
          </CardContent>
        </Card>
      </LoadingWrapper>
    </div>
  );
}

