// // app/(admin)/all-investments/page.tsx
// 'use client';

// import SubscriptionTable from '@/components/admin/SubscriptionTable';
// import React from 'react';

// export default function AllInvestments() {
//   return (
    
//     <> 
//     {/* <div className="bg-white p-6 rounded-lg shadow-md">
//       <h1 className="text-2xl font-bold mb-4">All Investments</h1>
     
//     </div> */}

//      <SubscriptionTable/>
//     </>
//   );
// }



'use client';

import InvestmentStats from '@/components/admin/InvestmentStats';
import SubscriptionTable from '@/components/admin/SubscriptionTable';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import { InvestmentManagementSkeleton } from '@/components/ui/AdminSkeleton';
import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  Activity,
  DollarSign,
  Search,
  Filter,
  Download,
  Plus,
  RefreshCw,
  BarChart3
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function AllInvestments() {
  const [isLoading, setIsLoading] = useState(true);
  const [, setInvestmentData] = useState<any>(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    const loadInvestmentData = async () => {
      // Simulate loading investment data
      await new Promise(resolve => setTimeout(resolve, 2200));
      setInvestmentData({}); // Set actual data here
      setIsLoading(false);
    };

    loadInvestmentData();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  if (isLoading) {
    return <InvestmentManagementSkeleton />;
  }

  return (
    <div className="space-y-8">
      {/* Modern Page Header */}
      <div className="relative">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-green-600/5 via-blue-600/5 to-purple-600/5 rounded-3xl"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(34,197,94,0.1),transparent_50%)]"></div>

        <Card className="relative border-0 shadow-xl bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm">
          <CardHeader className="pb-8">
            <div className="flex items-start justify-between">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl shadow-lg">
                    <TrendingUp className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                      Investment Management
                    </CardTitle>
                    <CardDescription className="text-lg mt-1">
                      Comprehensive investment tracking and analytics dashboard
                    </CardDescription>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    disabled={refreshing}
                    className="bg-white/50 hover:bg-white/80 border-slate-200 dark:bg-slate-800/50 dark:hover:bg-slate-800/80 dark:border-slate-700"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-white/50 hover:bg-white/80 border-slate-200 dark:bg-slate-800/50 dark:hover:bg-slate-800/80 dark:border-slate-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    New Investment
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-white/50 hover:bg-white/80 border-slate-200 dark:bg-slate-800/50 dark:hover:bg-slate-800/80 dark:border-slate-700"
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Analytics
                  </Button>
                </div>
              </div>

              {/* Live Status Indicator */}
              <div className="flex items-center space-x-2 bg-green-50 dark:bg-green-900/20 px-4 py-2 rounded-full border border-green-200 dark:border-green-800">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-700 dark:text-green-400">
                  Live Data
                </span>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      {/* Enhanced Statistics Overview */}
      <LoadingWrapper
        isLoading={false}
        loadingText="Loading investment statistics..."
        loadingVariant="skeleton"
      >
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 via-blue-500/5 to-purple-500/5 rounded-2xl"></div>
          <InvestmentStats />
        </div>
      </LoadingWrapper>

      {/* Modern Investment Table */}
      <LoadingWrapper
        isLoading={false}
        loadingText="Loading investment data..."
        loadingVariant="skeleton"
      >
        <Card className="border-0 shadow-xl bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm">
          <CardHeader className="border-b border-slate-200 dark:border-slate-700 bg-gradient-to-r from-slate-50 to-white dark:from-slate-800 dark:to-slate-900">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl font-semibold flex items-center">
                  <DollarSign className="h-5 w-5 mr-2 text-green-600" />
                  Investment Database
                </CardTitle>
                <CardDescription>
                  Complete overview of all investments and their performance
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1 text-sm text-slate-600 dark:text-slate-400">
                  <Activity className="h-4 w-4" />
                  <span>Real-time updates</span>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <SubscriptionTable />
          </CardContent>
        </Card>
      </LoadingWrapper>
    </div>
  );
}

