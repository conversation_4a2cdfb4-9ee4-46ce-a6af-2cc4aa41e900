// app/(admin)/admin/all-investors/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import UserTable from '@/components/admin/UserTable';
import InvestorStats from '@/components/InvestorStats';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import { UserManagementSkeleton } from '@/components/ui/AdminSkeleton';
import {
  Users,
  TrendingUp,
  Activity,
  UserCheck,
  DollarSign,
  Search,
  Filter,
  Download,
  Plus,
  RefreshCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const AllInvestorsPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [, setUserData] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    const loadUserData = async () => {
      // Simulate loading user management data
      await new Promise(resolve => setTimeout(resolve, 1800));
      setUserData({}); // Set actual data here
      setIsLoading(false);
    };

    loadUserData();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  if (isLoading) {
    return <UserManagementSkeleton />;
  }

  return (
    <div className="space-y-8">
      {/* Modern Page Header */}
      <div className="relative">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-amber-600/5 via-yellow-600/5 to-orange-600/5 rounded-3xl"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(245,158,11,0.1),transparent_50%)]"></div>

        <Card className="relative shadow-xl bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border border-amber-200/50 dark:border-amber-800/30">
          <CardHeader className="pb-8">
            <div className="flex items-start justify-between">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-gradient-to-br from-amber-500 to-yellow-500 rounded-2xl shadow-lg shadow-amber-500/25">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                      Members & Investors
                    </CardTitle>
                    <CardDescription className="text-lg mt-1">
                      Comprehensive member management and analytics dashboard
                    </CardDescription>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    disabled={refreshing}
                    className="bg-amber-50/50 hover:bg-amber-100/80 border-amber-200 dark:bg-amber-900/20 dark:hover:bg-amber-800/50 dark:border-amber-700"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-amber-50/50 hover:bg-amber-100/80 border-amber-200 dark:bg-amber-900/20 dark:hover:bg-amber-800/50 dark:border-amber-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Member
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-amber-50/50 hover:bg-amber-100/80 border-amber-200 dark:bg-amber-900/20 dark:hover:bg-amber-800/50 dark:border-amber-700"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Advanced Filters
                  </Button>
                </div>
              </div>

              {/* Live Status Indicator */}
              <div className="flex items-center space-x-2 bg-green-50 dark:bg-green-900/20 px-4 py-2 rounded-full border border-green-200 dark:border-green-800">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-700 dark:text-green-400">
                  Live Data
                </span>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      {/* Enhanced Statistics Overview */}
      <LoadingWrapper
        isLoading={false}
        loadingText="Loading member statistics..."
        loadingVariant="skeleton"
      >
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/5 via-yellow-500/5 to-orange-500/5 rounded-2xl"></div>
          <InvestorStats />
        </div>
      </LoadingWrapper>

      {/* Modern User Management Table */}
      <LoadingWrapper
        isLoading={false}
        loadingText="Loading member data..."
        loadingVariant="skeleton"
      >
        <Card className="shadow-xl bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm border border-amber-200/50 dark:border-amber-800/30">
          <CardHeader className="border-b border-amber-200/50 dark:border-amber-700/50 bg-gradient-to-r from-amber-50/50 to-yellow-50/30 dark:from-amber-900/10 dark:to-yellow-900/5">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl font-semibold flex items-center">
                  <UserCheck className="h-5 w-5 mr-2 text-amber-600" />
                  Member Database
                </CardTitle>
                <CardDescription>
                  Complete overview of all registered members and their activities
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1 text-sm text-slate-600 dark:text-slate-400">
                  <TrendingUp className="h-4 w-4" />
                  <span>Real-time updates</span>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <UserTable />
          </CardContent>
        </Card>
      </LoadingWrapper>
    </div>
  );
};

export default AllInvestorsPage;

