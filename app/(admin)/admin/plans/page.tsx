// // app/(admin)/admin/page.tsx
// 'use client';

// import AdminPurchasePlans from '@/components/admin/AdminPurchasePlans';
// import React from 'react';

// export default function PlansPage() {
//   return (
//     <>
//     <div className="bg-white p-6 rounded-lg shadow-md">
//       <h1 className="text-2xl font-bold mb-4">All Plans Page</h1>
//       <p>All Investment plans available.</p>
//     </div>
//     <AdminPurchasePlans/>
//     </>
//   );
// }


'use client';

import AdminPurchasePlans from '@/components/admin/AdminPurchasePlans';
import PlanStats from '@/components/admin/PlanStats';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import { PlanManagementSkeleton } from '@/components/ui/AdminSkeleton';
import React, { useState, useEffect } from 'react';

export default function PlansPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [, setPlanData] = useState(null);

  useEffect(() => {
    const loadPlanData = async () => {
      // Simulate loading plan data
      await new Promise(resolve => setTimeout(resolve, 1600));
      setPlanData({}); // Set actual data here
      setIsLoading(false);
    };

    loadPlanData();
  }, []);

  if (isLoading) {
    return <PlanManagementSkeleton />;
  }

  return (
    <div className="space-y-6">
      <LoadingWrapper
        isLoading={false}
        className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md"
      >
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h1 className="text-2xl font-bold mb-4">Investment Plans Management</h1>
          <p className="text-gray-600 dark:text-gray-300">
            Manage and analyze all investment plans available on the platform.
          </p>
        </div>
      </LoadingWrapper>

      <LoadingWrapper
        isLoading={false}
        loadingText="Loading plan statistics..."
        loadingVariant="skeleton"
      >
        <PlanStats />
      </LoadingWrapper>

      <LoadingWrapper
        isLoading={false}
        loadingText="Loading plan management..."
        loadingVariant="skeleton"
        className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md"
      >
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <AdminPurchasePlans />
        </div>
      </LoadingWrapper>
    </div>
  );
}

