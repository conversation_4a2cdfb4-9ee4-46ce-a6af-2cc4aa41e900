// // app/(admin)/admin/page.tsx
// 'use client';

// import AdminPurchasePlans from '@/components/admin/AdminPurchasePlans';
// import React from 'react';

// export default function PlansPage() {
//   return (
//     <>
//     <div className="bg-white p-6 rounded-lg shadow-md">
//       <h1 className="text-2xl font-bold mb-4">All Plans Page</h1>
//       <p>All Investment plans available.</p>
//     </div>
//     <AdminPurchasePlans/>
//     </>
//   );
// }


'use client';

import AdminPurchasePlans from '@/components/admin/AdminPurchasePlans';
import PlanStats from '@/components/admin/PlanStats';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import { PlanManagementSkeleton } from '@/components/ui/AdminSkeleton';
import React, { useState, useEffect } from 'react';

export default function PlansPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [, setPlanData] = useState(null);

  useEffect(() => {
    const loadPlanData = async () => {
      // Simulate loading plan data
      await new Promise(resolve => setTimeout(resolve, 1600));
      setPlanData({}); // Set actual data here
      setIsLoading(false);
    };

    loadPlanData();
  }, []);

  if (isLoading) {
    return <PlanManagementSkeleton />;
  }

  return (
    <div className="space-y-8">
      {/* Modern Page Header */}
      <div className="bg-gradient-to-r from-amber-500 via-yellow-500 to-orange-500 rounded-2xl p-8 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Investment Plans Management</h1>
            <p className="text-amber-100 text-lg">Create, manage, and analyze all investment plans on the platform</p>
          </div>
          <div className="hidden md:block">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <LoadingWrapper
        isLoading={false}
        loadingText="Loading plan statistics..."
        loadingVariant="skeleton"
      >
        <PlanStats />
      </LoadingWrapper>

      <LoadingWrapper
        isLoading={false}
        loadingText="Loading plan management..."
        loadingVariant="skeleton"
        className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-2xl shadow-xl border border-amber-200/50 dark:border-amber-800/30"
      >
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-2xl shadow-xl border border-amber-200/50 dark:border-amber-800/30">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-slate-900 dark:text-white">Plan Management Dashboard</h2>
              <p className="text-slate-600 dark:text-slate-400">Create, edit, and monitor investment plans</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-slate-600 dark:text-slate-400">Live</span>
            </div>
          </div>
          <AdminPurchasePlans />
        </div>
      </LoadingWrapper>
    </div>
  );
}

