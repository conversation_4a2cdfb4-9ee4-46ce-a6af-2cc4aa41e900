// // app/(admin)/payouts/page.tsx
// 'use client';

// import PaymentsTable from '@/components/admin/PaymentsTable';
// import React from 'react';

// export default function Allpayouts() {
//   return (
    
//     <>
//     <div className="bg-white p-6 rounded-lg shadow-md">
//       <h1 className="text-2xl font-bold mb-4">All Payouts Management</h1>
//       <p>All Payouts page.</p>
//     </div>

//      <PaymentsTable/>
//     </>
//   );
// }



'use client';

import PayoutStats from '@/components/admin/PayoutStats';
import PaymentsTable from '@/components/admin/PaymentsTable';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from 'lucide-react';
import React, { useState } from 'react';

export default function Allpayouts() {
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement search functionality here
    console.log('Searching for:', searchTerm);
  };

  return (
    <div className="space-y-4">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-4">All Payouts Management</h1>
        <p className="text-gray-600 dark:text-gray-300">
          Manage and analyze all payouts on the platform.
        </p>
      </div>

      <PayoutStats />

      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Payout Details</h2>
          <form onSubmit={handleSearch} className="flex gap-2">
            <Input
              type="text"
              placeholder="Search payouts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
            <Button type="submit" variant="outline" size="icon">
              <Search className="h-4 w-4" />
            </Button>
          </form>
        </div>
        <PaymentsTable />
      </div>
    </div>
  );
}

