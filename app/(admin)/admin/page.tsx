// // app/(admin)/admin/page.tsx
// 'use client';

// import InvestmentTable from '@/components/admin/InvestmentTable';
// import React from 'react';

// export default function AdminHomePage() {
//   return (
//     <> 
//     {/* <div className="bg-white p-6 rounded-lg shadow-md">
//       <h1 className="text-2xl font-bold mb-4">All Membership payments</h1>
//       <p>Membership investments.</p>
//     </div> */}

//      <InvestmentTable/>
//     </>
    
//   );
// }

// app/(admin)/admin/page.tsx
'use client';

import DashboardStats from '@/components/admin/DashboardStats';
import InvestmentTable from '@/components/admin/InvestmentTable';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import AdminDashboardSkeleton from '@/components/ui/AdminSkeleton';
import React, { useState, useEffect } from 'react';

export default function AdminHomePage() {
  const [isLoading, setIsLoading] = useState(true);
  const [, setDashboardData] = useState(null);

  useEffect(() => {
    const loadAdminDashboard = async () => {
      // Simulate loading admin dashboard data
      await new Promise(resolve => setTimeout(resolve, 2000));
      setDashboardData({}); // Set actual data here
      setIsLoading(false);
    };

    loadAdminDashboard();
  }, []);

  if (isLoading) {
    return <AdminDashboardSkeleton />;
  }

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-amber-500 via-yellow-500 to-orange-500 rounded-2xl p-8 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Welcome back, Admin!</h1>
            <p className="text-amber-100 text-lg">Here's what's happening with TGM Finance today.</p>
          </div>
          <div className="hidden md:block">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <LoadingWrapper
        isLoading={false}
        loadingText="Loading statistics..."
        loadingVariant="skeleton"
      >
        <DashboardStats />
      </LoadingWrapper>

      <LoadingWrapper
        isLoading={false}
        loadingText="Loading recent investments..."
        loadingVariant="skeleton"
        className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-2xl shadow-xl border border-amber-200/50 dark:border-amber-800/30"
      >
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-2xl shadow-xl border border-amber-200/50 dark:border-amber-800/30">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-slate-900 dark:text-white">Recent Investments</h2>
              <p className="text-slate-600 dark:text-slate-400">Monitor the latest investment activities</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-slate-600 dark:text-slate-400">Live</span>
            </div>
          </div>
          <InvestmentTable />
        </div>
      </LoadingWrapper>
    </div>
  );
}

