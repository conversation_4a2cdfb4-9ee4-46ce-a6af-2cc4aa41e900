// // app/(admin)/admin/page.tsx
// 'use client';

// import InvestmentTable from '@/components/admin/InvestmentTable';
// import React from 'react';

// export default function AdminHomePage() {
//   return (
//     <> 
//     {/* <div className="bg-white p-6 rounded-lg shadow-md">
//       <h1 className="text-2xl font-bold mb-4">All Membership payments</h1>
//       <p>Membership investments.</p>
//     </div> */}

//      <InvestmentTable/>
//     </>
    
//   );
// }

// app/(admin)/admin/page.tsx
'use client';

import DashboardStats from '@/components/admin/DashboardStats';
import InvestmentTable from '@/components/admin/InvestmentTable';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import AdminDashboardSkeleton from '@/components/ui/AdminSkeleton';
import React, { useState, useEffect } from 'react';

export default function AdminHomePage() {
  const [isLoading, setIsLoading] = useState(true);
  const [, setDashboardData] = useState(null);

  useEffect(() => {
    const loadAdminDashboard = async () => {
      // Simulate loading admin dashboard data
      await new Promise(resolve => setTimeout(resolve, 2000));
      setDashboardData({}); // Set actual data here
      setIsLoading(false);
    };

    loadAdminDashboard();
  }, []);

  if (isLoading) {
    return <AdminDashboardSkeleton />;
  }

  return (
    <div className="space-y-6">
      <LoadingWrapper
        isLoading={false}
        className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md"
      >
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h1 className="text-2xl font-bold mb-4">Admin Dashboard</h1>
          <p className="text-gray-600 dark:text-gray-300">
            Monitor your platform&apos;s performance and investment activities.
          </p>
        </div>
      </LoadingWrapper>

      <LoadingWrapper
        isLoading={false}
        loadingText="Loading statistics..."
        loadingVariant="skeleton"
      >
        <DashboardStats />
      </LoadingWrapper>

      <LoadingWrapper
        isLoading={false}
        loadingText="Loading recent investments..."
        loadingVariant="skeleton"
        className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md"
      >
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-bold mb-4">Recent Investments</h2>
          <InvestmentTable />
        </div>
      </LoadingWrapper>
    </div>
  );
}

