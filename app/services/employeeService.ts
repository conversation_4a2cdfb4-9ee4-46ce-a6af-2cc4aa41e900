// app/services/employeeService.ts

// export const fetchEmployees = async () => {
//     const response = await fetch('/api/employees/getAllEmployees');
//     if (!response.ok) {
//         throw new Error('Failed to fetch employees');
//     }
//     return response.json();
// };

// export const fetchDepartments = async () => {
//     const response = await fetch('/api/departments/getAllDepartments');
//     if (!response.ok) {
//         throw new Error('Failed to fetch departments');
//     }
//     return response.json();
// };


// // app/services/employeeService.ts
// import { Employee } from '@/app/types/employee';

// // Fetch employees function
// export const fetchEmployees = async () => {
//     const response = await fetch('/api/employees/getAllEmployees');
//     if (!response.ok) {
//         throw new Error('Failed to fetch employees');
//     }
//     return response.json();
// };

// // Fetch departments function
// export const fetchDepartments = async () => {
//     const response = await fetch('/api/departments/getAllDepartments');
//     if (!response.ok) {
//         throw new Error('Failed to fetch departments');
//     }
//     return response.json();
// };
