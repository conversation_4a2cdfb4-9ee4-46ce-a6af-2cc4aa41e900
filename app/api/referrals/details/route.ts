// app/api/referrals/details/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getReferralDetails } from '@/lib/liferrals/referralPayments';
import { verifyAccessToken } from '@/lib/auth';
import '@/models'; // Import all models

const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Adjust in production
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function GET(req: NextRequest) {
  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  try {
    await connectToDatabase();

    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401, headers: corsHeaders }
      );
    }

    const token = authHeader.split(' ')[1];

    try {
      const decodedToken = await verifyAccessToken(token);
      const userId = (decodedToken as { id: string }).id;
      
      if (!userId) {
        throw new Error('Invalid token structure');
      }

      const referralDetails = await getReferralDetails(userId);

      return NextResponse.json({ referralDetails }, { headers: corsHeaders });
    } catch (tokenError) {
      console.error('Token verification failed:', tokenError);
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401, headers: corsHeaders }
      );
    }
  } catch (error) {
    console.error('Error fetching referral details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    );
  }
}

