// app/api/referrals/payments/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { calculateReferralPayments } from '@/lib/liferrals/referralPayments'; 
import { verifyAccessToken } from '@/lib/auth';
import '@/models'; // Import all models

const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Adjust in production
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function GET(req: NextRequest) {
  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  try {
    await connectToDatabase();

    // Get the token from the Authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401, headers: corsHeaders }
      );
    }

    const token = authHeader.split(' ')[1];

    try {
      // Verify the token
      const decodedToken = await verifyAccessToken(token);
      
      // TypeScript doesn't know the structure of decodedToken, so we'll assert its type
      const userId = (decodedToken as { id: string }).id;
      
      if (!userId) {
        throw new Error('Invalid token structure');
      }

      const referralPayments = await calculateReferralPayments(userId);

      return NextResponse.json(referralPayments, { headers: corsHeaders });
    } catch (tokenError) {
      console.error('Token verification failed:', tokenError);
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401, headers: corsHeaders }
      );
    }
  } catch (error) {
    console.error('Error calculating referral payments:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    );
  }
}

