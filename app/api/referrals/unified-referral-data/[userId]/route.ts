
// // app/api/unified-referral-data/[userId]/route.ts

// import { NextRequest, NextResponse } from 'next/server';
// import { connectToDatabase } from '@/lib/dbconnect';
// import NodeCache from 'node-cache';
// import '@/models';
// import { User } from '@/models/User';
// import mongoose from 'mongoose';
// import {
//   getReferralPyramidData,
//   getCompleteReferralStructure,
//   getInvestmentData,
//   UnifiedReferralData,
//   ReferralPyramid,
//   ReferralLevel,
//   ReferralNode,
//   ExtendedIReferral,
// } from '@/lib/liferrals/referralUtils';

// export const dynamic = 'force-dynamic';

// const CACHE_EXPIRATION = 60 * 5; // 5 minutes
// const cache = new NodeCache({ stdTTL: CACHE_EXPIRATION, checkperiod: 120 });

// interface IntroducerData {
//   _id: mongoose.Types.ObjectId;
//   name: string;
//   email: string;
// }

// interface UserData {
//   _id: mongoose.Types.ObjectId;
//   referrerId?: mongoose.Types.ObjectId | null;
//   name: string;
//   email: string;
//   referralCode: string;
//   allReferrals: UserData[];
// }


// async function getReferralData(userId: string): Promise<UnifiedReferralData> {
//   const [pyramidData, completeStructureData] = await Promise.all([
//     getReferralPyramidData(userId),
//     getCompleteReferralStructure(userId),
//   ]);

//   console.log('Fetched Pyramid Data:', JSON.stringify(pyramidData, null, 2));
//   console.log('Fetched Complete Structure Data:', JSON.stringify(completeStructureData, null, 2));

//   if (!pyramidData || !Array.isArray(pyramidData)) {
//     throw new Error('Invalid pyramid data received from database.');
//   }

//   if (!completeStructureData || completeStructureData.length === 0) {
//     throw new Error('Complete structure data is empty or invalid.');
//   }

//   const referralIds = pyramidData.map(referral => referral._id);
//   const investmentData = await getInvestmentData(userId, referralIds);

//   const investmentMap = new Map<string, ExtendedIReferral>(
//     investmentData.map(data => [data.referredUserId?.toString() ?? '', data])
//   );

//   // Fetch all introducers in one query
//   const introducerIds = new Set(pyramidData.map(referral => referral.referrerId).filter(Boolean));
//   const introducers = await User.find({ _id: { $in: Array.from(introducerIds) } })
//     .lean<IntroducerData[]>();

//   // Create a map for introducers
//   const introducerMap = new Map(
//     introducers.map(introducer => [introducer._id.toString(), introducer])
//   );

//   const pyramid: ReferralPyramid = {
//     level1: [],
//     level2: [],
//     level3: [],
//     level4: [],
//   };

//   for (const referral of pyramidData) {
//     const investment = investmentMap.get(referral._id.toString());
//     const introducer = introducerMap.get(referral.referrerId?.toString() ?? '');
//     const referralWithInvestment: ReferralLevel = {
//       ...referral,
//       investmentTotal: investment?.investmentAmount ?? 0,
//       status: investment?.status ?? 'pending',
//       introducerName: introducer?.name ?? 'Unknown',
//       introducerEmail: introducer?.email ?? 'Unknown',
//     };

//     if (referral.level <= 4) {
//       pyramid[`level${referral.level}` as keyof ReferralPyramid].push(referralWithInvestment);
//     }
//   }

//   function buildReferralTree(user: UserData, level: number): ReferralNode {
//     const investment = investmentMap.get(user._id.toString());
//     const introducer = user.referrerId ? introducerMap.get(user.referrerId.toString()) : null;

//     return {
//       userId: user._id.toString(),
//       name: user.name,
//       email: user.email,
//       referralCode: user.referralCode,
//       introducerId: user.referrerId?.toString() || null,
//       introducerName: introducer?.name || null,
//       introducerEmail: introducer?.email || null,
//       level,
//       children: user.allReferrals?.map((child: UserData) => buildReferralTree(child, level + 1)) || [],
//       investmentTotal: investment?.investmentAmount || 0,
//       status: investment?.status || 'pending',
//     };
//   }

//   const completeStructure = buildReferralTree(completeStructureData[0] as UserData, 0);

//   return {
//     pyramid,
//     completeStructure,
//   };
// }





// export async function GET(req: NextRequest, { params }: { params: { userId: string } }) {
//   const { userId } = params;

//   console.log('Received request for unified referral data:', { userId });

//   try {
//     await connectToDatabase();

//     // Check in-memory cache
//     const cachedData = cache.get<UnifiedReferralData>(userId);
//     if (cachedData) {
//       console.log('Returning cached data for userId:', userId);
//       return NextResponse.json(cachedData, { status: 200 });
//     }

//     const unifiedData = await getReferralData(userId);

//     // Cache the result in-memory
//     cache.set(userId, unifiedData);

//     console.log('Unified referral data:', JSON.stringify(unifiedData, null, 2));

//     return NextResponse.json(unifiedData, { status: 200 });
//   } catch (error) {
//     console.error('Error fetching unified referral data:', error);

//     const errorMessage =
//       error instanceof Error
//         ? error.message
//         : 'Unknown error occurred while fetching unified referral data.';

//     console.log('Error details:', error);

//     return NextResponse.json(
//       { error: 'Internal server error', message: errorMessage },
//       { status: 500 }
//     );
//   }
// }

// export async function POST(req: NextRequest, { params }: { params: { userId: string } }) {
//   const { userId } = params;

//   try {
//     cache.del(userId);
//     return NextResponse.json({ message: 'Referral cache cleared successfully' }, { status: 200 });
//   } catch (error) {
//     console.error('Error clearing referral cache:', error);
//     return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
//   }
// }



// app/api/unified-referral-data/[userId]/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import NodeCache from 'node-cache';
import '@/models';
import { User } from '@/models/User';
import mongoose from 'mongoose';
import {
  getReferralPyramidData,
  getCompleteReferralStructure,
  getInvestmentData,
  UnifiedReferralData,
  ReferralPyramid,
  ReferralLevel,
  ReferralNode,
  ExtendedIReferral,
} from '@/lib/liferrals/referralUtils';

export const dynamic = 'force-dynamic';

const CACHE_EXPIRATION = 60 * 5; // 5 minutes
const cache = new NodeCache({ stdTTL: CACHE_EXPIRATION, checkperiod: 120 });

interface IntroducerData {
  _id: mongoose.Types.ObjectId;
  name: string;
  email: string;
}

interface UserData {
  _id: mongoose.Types.ObjectId;
  referrerId?: mongoose.Types.ObjectId | null;
  name: string;
  email: string;
  referralCode: string;
  allReferrals: UserData[];
}

async function getReferralData(userId: string): Promise<UnifiedReferralData> {
  const [pyramidData, completeStructureData] = await Promise.all([
    getReferralPyramidData(userId),
    getCompleteReferralStructure(userId),
  ]);

  if (!pyramidData || !Array.isArray(pyramidData)) {
    throw new Error('Invalid pyramid data received from database.');
  }

  if (!completeStructureData || completeStructureData.length === 0) {
    throw new Error('Complete structure data is empty or invalid.');
  }

  const referralIds = pyramidData.map((referral) => referral._id);
  const investmentData = await getInvestmentData(userId, referralIds);

  const investmentMap = new Map<string, ExtendedIReferral>(
    investmentData.map((data) => [data.referredUserId?.toString() ?? '', data])
  );

  const introducerIds = new Set(pyramidData.map((referral) => referral.referrerId).filter(Boolean));
  const introducers = await User.find({ _id: { $in: Array.from(introducerIds) } }).lean<IntroducerData[]>();

  const introducerMap = new Map<string, IntroducerData>(
    introducers.map((introducer) => [introducer._id.toString(), introducer])
  );

  const pyramid: ReferralPyramid = {
    level1: [],
    level2: [],
    level3: [],
    level4: [],
  };

  for (const referral of pyramidData) {
    const investment = investmentMap.get(referral._id.toString());
    const introducer = introducerMap.get(referral.referrerId?.toString() ?? '');
    const referralWithInvestment: ReferralLevel = {
      ...referral,
      investmentTotal: investment?.investmentAmount ?? 0,
      status: investment?.status ?? 'pending',
      introducerName: introducer?.name || 'Unknown',
      introducerEmail: introducer?.email || 'Unknown',
    };

    if (referral.level <= 4) {
      pyramid[`level${referral.level}` as keyof ReferralPyramid].push(referralWithInvestment);
    }
  }

  function buildReferralTree(user: UserData, level: number): ReferralNode {
    const investment = investmentMap.get(user._id.toString());
    const introducer = user.referrerId ? introducerMap.get(user.referrerId.toString()) : null;

    return {
      userId: user._id.toString(),
      name: user.name,
      email: user.email,
      referralCode: user.referralCode,
      // introducerId: user.referrerId?.toString() || undefined,
      // introducerName: introducer?.name || undefined,
      // introducerEmail: introducer?.email || undefined,
      introducerId: user.referrerId?.toString() || null,
      introducerName: introducer?.name || null,
      introducerEmail: introducer?.email || null,

      level,
      children: user.allReferrals?.map((child: UserData) => buildReferralTree(child, level + 1)) || [],
      investmentTotal: investment?.investmentAmount || 0,
      status: investment?.status || 'pending',
    };
  }

  const completeStructure = buildReferralTree(completeStructureData[0] as UserData, 0);

  return {
    pyramid,
    completeStructure,
  };
}

export async function GET(req: NextRequest, { params }: { params: { userId: string } }) {
  const { userId } = params;

  console.log('Received request for unified referral data:', { userId });

  try {
    await connectToDatabase();

    // Check in-memory cache
    const cachedData = cache.get<UnifiedReferralData>(userId);
    if (cachedData) {
      console.log('Returning cached data for userId:', userId);
      return NextResponse.json(cachedData, { status: 200 });
    }

    const unifiedData = await getReferralData(userId);

    // Cache the result in-memory
    cache.set(userId, unifiedData);

    console.log('Unified referral data:', JSON.stringify(unifiedData, null, 2));

    return NextResponse.json(unifiedData, { status: 200 });
  } catch (error) {
    console.error('Error fetching unified referral data:', error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : 'Unknown error occurred while fetching unified referral data.';

    return NextResponse.json(
      { error: 'Internal server error', message: errorMessage },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest, { params }: { params: { userId: string } }) {
  const { userId } = params;

  try {
    cache.del(userId);
    return NextResponse.json({ message: 'Referral cache cleared successfully' }, { status: 200 });
  } catch (error) {
    console.error('Error clearing referral cache:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}





// import { NextRequest, NextResponse } from 'next/server';
// import { connectToDatabase } from '@/lib/dbconnect';
// import NodeCache from 'node-cache';
// import '@/models';
// import { User } from '@/models/User';
// import mongoose from 'mongoose';
// import {
//   getReferralPyramidData,
//   getCompleteReferralStructure,
//   getInvestmentData,
//   UnifiedReferralData,
//   ReferralPyramid,
//   ReferralLevel,
//   ReferralNode,
//   ExtendedIReferral,
// } from '@/lib/liferrals/referralUtils';

// export const dynamic = 'force-dynamic';

// const CACHE_EXPIRATION = 60 * 5; // 5 minutes
// const cache = new NodeCache({ stdTTL: CACHE_EXPIRATION, checkperiod: 120 });

// interface IntroducerData {
//   _id: mongoose.Types.ObjectId;
//   name: string;
//   email: string;
// }

// interface UserData {
//   _id: mongoose.Types.ObjectId;
//   referrerId?: mongoose.Types.ObjectId | null;
//   name: string;
//   email: string;
//   referralCode: string;
//   allReferrals: UserData[];
// }

// async function getReferralData(userId: string): Promise<UnifiedReferralData> {
//   const [pyramidData, completeStructureData] = await Promise.all([
//     getReferralPyramidData(userId),
//     getCompleteReferralStructure(userId),
//   ]);

//   console.log('Fetched Pyramid Data:', JSON.stringify(pyramidData, null, 2));
//   console.log('Fetched Complete Structure Data:', JSON.stringify(completeStructureData, null, 2));

//   if (!pyramidData || !Array.isArray(pyramidData)) {
//     throw new Error('Invalid pyramid data received from database.');
//   }

//   if (!completeStructureData || completeStructureData.length === 0) {
//     throw new Error('Complete structure data is empty or invalid.');
//   }

//   const referralIds = pyramidData.map(referral => referral._id);
//   const investmentData = await getInvestmentData(userId, referralIds);

//   const investmentMap = new Map<string, ExtendedIReferral>(
//     investmentData.map(data => [data.referredUserId?.toString() ?? '', data])
//   );

//   // Fetch all introducers in one query
//   const introducerIds = new Set(pyramidData.map(referral => referral.referrerId).filter(Boolean));
//   const introducers = await User.find({ _id: { $in: Array.from(introducerIds) } })
//     .lean<IntroducerData[]>();

//   // Create a map for introducers
//   const introducerMap = new Map(
//     introducers.map(introducer => [introducer._id.toString(), introducer])
//   );

//   const pyramid: ReferralPyramid = {
//     level1: [],
//     level2: [],
//     level3: [],
//     level4: [],
//   };

//   for (const referral of pyramidData) {
//     const investment = investmentMap.get(referral._id.toString());
//     const introducer = introducerMap.get(referral.referrerId?.toString() ?? '');
//     const referralWithInvestment: ReferralLevel = {
//       ...referral,
//       investmentTotal: investment?.investmentAmount ?? 0,
//       status: investment?.status ?? 'pending',
//       introducerName: introducer?.name ?? 'Unknown',
//       introducerEmail: introducer?.email ?? 'Unknown',
//     };

//     if (referral.level <= 4) {
//       pyramid[`level${referral.level}` as keyof ReferralPyramid].push(referralWithInvestment);
//     }
//   }

//   function buildReferralTree(user: UserData, level: number): ReferralNode {
//     const investment = investmentMap.get(user._id.toString());
//     const introducer = user.referrerId ? introducerMap.get(user.referrerId.toString()) : null;

//     return {
//       userId: user._id.toString(),
//       name: user.name,
//       email: user.email,
//       referralCode: user.referralCode,
//       introducerId: user.referrerId?.toString() || null,
//       introducerName: introducer?.name || null,
//       introducerEmail: introducer?.email || null,
//       level,
//       children: user.allReferrals?.map((child: UserData) => buildReferralTree(child, level + 1)) || [],
//       investmentTotal: investment?.investmentAmount || 0,
//       status: investment?.status || 'pending',
//     };
//   }

//   const completeStructure = buildReferralTree(completeStructureData[0] as UserData, 0);

//   return {
//     pyramid,
//     completeStructure,
//   };
// }

// export async function GET(req: NextRequest, { params }: { params: { userId: string } }) {
//   const { userId } = params;

//   console.log('Received request for unified referral data:', { userId });

//   try {
//     await connectToDatabase();

//     // Check in-memory cache
//     const cachedData = cache.get<UnifiedReferralData>(userId);
//     if (cachedData) {
//       console.log('Returning cached data for userId:', userId);
//       return NextResponse.json(cachedData, { status: 200 });
//     }

//     const unifiedData = await getReferralData(userId);

//     // Cache the result in-memory
//     cache.set(userId, unifiedData);

//     console.log('Unified referral data:', JSON.stringify(unifiedData, null, 2));

//     return NextResponse.json(unifiedData, { status: 200 });
//   } catch (error) {
//     console.error('Error fetching unified referral data:', error);

//     const errorMessage =
//       error instanceof Error
//         ? error.message
//         : 'Unknown error occurred while fetching unified referral data.';

//     console.log('Error details:', error);

//     return NextResponse.json(
//       { error: 'Internal server error', message: errorMessage },
//       { status: 500 }
//     );
//   }
// }

// export async function POST(req: NextRequest, { params }: { params: { userId: string } }) {
//   const { userId } = params;

//   try {
//     cache.del(userId);
//     return NextResponse.json({ message: 'Referral cache cleared successfully' }, { status: 200 });
//   } catch (error) {
//     console.error('Error clearing referral cache:', error);
//     return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
//   }
// }

