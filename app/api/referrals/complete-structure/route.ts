

// app/api/referrals/complete-structure/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCompleteReferralStructure } from '@/lib/liferrals/referralIntroductions'; 
import { verifyAccessToken } from '@/lib/auth';
import '@/models'; // Import all models
import { JwtPayload } from 'jsonwebtoken';

import { getCorsHeaders } from '@/lib/cors'; // Centralized CORS handling

interface DecodedToken extends JwtPayload {
  id: string;
  name: string;
  email: string;
  role: string; // e.g., 'admin', 'user'
}

/**
 * **GET Handler**
 
 */
export async function GET(req: NextRequest) {
  console.log('Received request for complete referral structure');

  // Extract the Origin header from the request
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // **Connect to Database**
    await connectToDatabase();

    // **Extract Authorization Header**
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('Unauthorized: No valid authorization header');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401, headers: corsHeaders }
      );
    }

    // **Extract Token from Authorization Header**
    const token = authHeader.split(' ')[1];
    console.log('Received token:', token);

    try {
      // **Verify the Access Token**
      const decodedToken = await verifyAccessToken(token) as DecodedToken;
      console.log('Decoded token:', decodedToken);

      const userId = decodedToken.id;
      // const userRole = decodedToken.role;

      if (!userId) {
        console.log('Invalid token structure: No user ID found');
        throw new Error('Invalid token structure');
      }

      // **Fetch Complete Referral Structure**
      console.log(`Fetching complete referral structure for user: ${userId}`);
      const referralStructure = await getCompleteReferralStructure(userId);
      console.log('Fetched complete referral structure:', JSON.stringify(referralStructure, null, 2));

      // **Respond with Referral Structure**
      return NextResponse.json(
        { referralStructure },
        { headers: corsHeaders, status: 200 }
      );
    } catch (tokenError) {
      console.error('Token verification failed:', tokenError);
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401, headers: corsHeaders }
      );
    }
  } catch (error) {
    console.error('Error fetching complete referral structure:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    );
  }
}

/**
 * **OPTIONS Handler**
 * Handles preflight CORS requests by returning appropriate headers.
 */
export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}
