import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { connectToDatabase } from '@/lib/dbconnect';
import { Referral } from '@/models/Referral';
import { getCorsHeaders } from '@/lib/cors'; // Use your predefined CORS utility
import '@/models'; // Ensure all models are loaded for Mongoose

export async function GET(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  // Handle OPTIONS requests for CORS preflight
  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  const authHeader = req.headers.get('authorization');
  if (!authHeader) {
    return NextResponse.json(
      { error: 'Authorization header missing' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    return NextResponse.json(
      { error: 'Authorization token missing' },
      { status: 401, headers: corsHeaders }
    );
  }

  try {
    // Verify the JWT token and extract user ID
    const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { id: string };
    const userId = decoded.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid token or user not found' },
        { status: 401, headers: corsHeaders }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Count the referrals for the user
    const referralCount = await Referral.countDocuments({ referrerId: userId });

    // Return the count in the response
    return NextResponse.json(
      { count: referralCount },
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error fetching referral count:', error);

    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401, headers: corsHeaders }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    );
  }
}
