// app/api/admin/referrals/debug/route.ts
import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { Referral } from '@/models/Referral';
import { Investment } from '@/models/Investment';
import '@/models'; // Import all models

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function GET(req: NextRequest) {
  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  // Verify admin authentication
  const authHeader = req.headers.get('authorization');
  if (!authHeader) {
    return NextResponse.json(
      { error: 'Unauthorized: Missing Authorization header' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    return NextResponse.json(
      { error: 'Unauthorized: Missing token' },
      { status: 401, headers: corsHeaders }
    );
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { role: string };
    if (decoded.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden: Admin role required' },
        { status: 403, headers: corsHeaders }
      );
    }

    await connectToDatabase();

    console.log('🔍 DEBUG: Starting comprehensive referral system analysis...');

    // 1. Check Referral collection
    const referralCount = await Referral.countDocuments();
    console.log(`📊 Total documents in Referral collection: ${referralCount}`);

    const sampleReferrals = await Referral.find()
      .populate('referrerId', 'name email referralCode')
      .populate('referredUserId', 'name email referralCode')
      .limit(5)
      .lean();
    
    console.log('📋 Sample referrals from Referral collection:', JSON.stringify(sampleReferrals, null, 2));

    // 2. Check User collection for referral relationships
    const usersWithReferrers = await User.countDocuments({ referrerId: { $ne: null } });
    console.log(`👥 Users with referrerId (referred by someone): ${usersWithReferrers}`);

    const sampleUsersWithReferrers = await User.find({ referrerId: { $ne: null } })
      .populate('referrerId', 'name email referralCode')
      .select('name email referralCode referrerId createdAt')
      .limit(5)
      .lean();
    
    console.log('👤 Sample users with referrers:', JSON.stringify(sampleUsersWithReferrers, null, 2));

    // 3. Check total users
    const totalUsers = await User.countDocuments();
    console.log(`👥 Total users in system: ${totalUsers}`);

    // 4. Check investments
    const totalInvestments = await Investment.countDocuments();
    const approvedInvestments = await Investment.countDocuments({ paymentStatus: 'approved' });
    console.log(`💰 Total investments: ${totalInvestments}, Approved: ${approvedInvestments}`);

    // 5. Check for users who have referred others
    const usersWhoReferred = await User.aggregate([
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: 'referrerId',
          as: 'referredUsers'
        }
      },
      {
        $match: {
          'referredUsers.0': { $exists: true }
        }
      },
      {
        $project: {
          name: 1,
          email: 1,
          referralCode: 1,
          referredCount: { $size: '$referredUsers' },
          referredUsers: {
            $map: {
              input: '$referredUsers',
              as: 'user',
              in: {
                name: '$$user.name',
                email: '$$user.email',
                createdAt: '$$user.createdAt'
              }
            }
          }
        }
      },
      { $limit: 5 }
    ]);

    console.log('🎯 Users who have referred others:', JSON.stringify(usersWhoReferred, null, 2));

    // 6. Check referral codes
    const usersWithReferralCodes = await User.countDocuments({ 
      referralCode: { $exists: true, $ne: null, $ne: '' } 
    });
    console.log(`🔗 Users with referral codes: ${usersWithReferralCodes}`);

    // 7. Try to find referral patterns
    const referralPatterns = await User.aggregate([
      {
        $match: {
          referrerId: { $ne: null }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'referrerId',
          foreignField: '_id',
          as: 'referrer'
        }
      },
      {
        $unwind: '$referrer'
      },
      {
        $lookup: {
          from: 'investments',
          localField: '_id',
          foreignField: 'userId',
          as: 'investments'
        }
      },
      {
        $addFields: {
          hasInvestments: { $gt: [{ $size: '$investments' }, 0] },
          totalInvestmentAmount: { $sum: '$investments.amount' },
          approvedInvestments: {
            $filter: {
              input: '$investments',
              cond: { $eq: ['$$this.paymentStatus', 'approved'] }
            }
          }
        }
      },
      {
        $addFields: {
          approvedInvestmentAmount: { $sum: '$approvedInvestments.amount' },
          potentialEarnings: { $multiply: [{ $sum: '$approvedInvestments.amount' }, 0.1] }
        }
      },
      {
        $project: {
          referredUser: {
            name: '$name',
            email: '$email',
            createdAt: '$createdAt'
          },
          referrer: {
            name: '$referrer.name',
            email: '$referrer.email',
            referralCode: '$referrer.referralCode'
          },
          hasInvestments: 1,
          totalInvestmentAmount: 1,
          approvedInvestmentAmount: 1,
          potentialEarnings: 1
        }
      },
      { $limit: 10 }
    ]);

    console.log('🔄 Referral patterns with investments:', JSON.stringify(referralPatterns, null, 2));

    // 8. Summary statistics
    const stats = {
      collections: {
        totalReferralDocuments: referralCount,
        totalUsers: totalUsers,
        usersWithReferrers: usersWithReferrers,
        usersWithReferralCodes: usersWithReferralCodes,
        usersWhoReferredOthers: usersWhoReferred.length
      },
      investments: {
        total: totalInvestments,
        approved: approvedInvestments
      },
      samples: {
        referralDocuments: sampleReferrals.length,
        usersWithReferrers: sampleUsersWithReferrers.length,
        usersWhoReferred: usersWhoReferred.length,
        referralPatterns: referralPatterns.length
      }
    };

    return NextResponse.json(
      {
        success: true,
        message: 'Referral system debug analysis complete',
        stats,
        samples: {
          referralDocuments: sampleReferrals,
          usersWithReferrers: sampleUsersWithReferrers,
          usersWhoReferred: usersWhoReferred,
          referralPatterns: referralPatterns
        },
        recommendations: {
          primaryDataSource: referralCount > 0 ? 'referral_collection' : 'user_relationships',
          hasReferralData: referralCount > 0 || usersWithReferrers > 0,
          dataQuality: {
            referralCollection: referralCount > 0 ? 'available' : 'empty',
            userRelationships: usersWithReferrers > 0 ? 'available' : 'empty',
            investmentData: approvedInvestments > 0 ? 'available' : 'empty'
          }
        }
      },
      { headers: corsHeaders }
    );

  } catch (error) {
    console.error('❌ Error in referral debug API:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Debug analysis failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500, headers: corsHeaders }
    );
  }
}
