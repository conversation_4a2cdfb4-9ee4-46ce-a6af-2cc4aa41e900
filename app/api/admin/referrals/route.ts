// import { NextRequest, NextResponse } from 'next/server';
// import jwt from 'jsonwebtoken';
// import { connectToDatabase } from '@/lib/dbconnect';
// import { Referral } from '@/models/Referral';
// // import { User } from '@/models/User';

// const corsHeaders = {
//   'Access-Control-Allow-Origin': '*', // Adjust for production
//   'Access-Control-Allow-Methods': 'GET, OPTIONS',
//   'Access-Control-Allow-Headers': 'Content-Type, Authorization',
// };

// export async function GET(req: NextRequest) {
//   // Handle preflight OPTIONS request
//   if (req.method === 'OPTIONS') {
//     return NextResponse.json(null, { headers: corsHeaders });
//   }

//   // Extract Authorization header
//   const authHeader = req.headers.get('authorization');
//   if (!authHeader) {
//     return NextResponse.json(
//       { error: 'Authorization header missing' },
//       { status: 401, headers: corsHeaders }
//     );
//   }

//   const token = authHeader.split(' ')[1];
//   if (!token) {
//     return NextResponse.json(
//       { error: 'Authorization token missing' },
//       { status: 401, headers: corsHeaders }
//     );
//   }

//   try {
//     // Verify JWT token
//     const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { id: string; role: string };

//     if (!decoded || decoded.role !== 'admin') {
//       return NextResponse.json(
//         { error: 'Unauthorized access. Admin role required.' },
//         { status: 403, headers: corsHeaders }
//       );
//     }

//     // Connect to database
//     await connectToDatabase();

//     // Fetch all referrals with related referrer and referred user information
//     const allReferrals = await Referral.find({})
//       .populate('referrerId', 'name email') // Include referrer details
//       .populate('referredUserId', 'name email') // Include referred user details
//       .exec();

//     // Format response
//     const formattedReferrals = allReferrals.map((referral) => ({
//       id: referral._id,
//       referrer: referral.referrerId
//         ? {
//             id: referral.referrerId._id,
//             name: referral.referrerId.name,
//             email: referral.referrerId.email,
//           }
//         : null,
//       referredUser: referral.referredUserId
//         ? {
//             id: referral.referredUserId._id,
//             name: referral.referredUserId.name,
//             email: referral.referredUserId.email,
//           }
//         : null,
//       referralId: referral.referralId,
//       createdAt: referral.createdAt.toISOString(),
//     }));

//     return NextResponse.json(
//       { referrals: formattedReferrals },
//       { status: 200, headers: corsHeaders }
//     );
//   } catch (error) {
//     console.error('Error fetching all referrals:', error);

//     // Handle specific errors
//     if (error instanceof jwt.JsonWebTokenError) {
//       return NextResponse.json(
//         { error: 'Invalid token' },
//         { status: 401, headers: corsHeaders }
//       );
//     }

//     // Handle other errors
//     return NextResponse.json(
//       { error: 'Internal server error' },
//       { status: 500, headers: corsHeaders }
//     );
//   }
// }



// import { NextRequest, NextResponse } from 'next/server';
// import jwt from 'jsonwebtoken';
// import { connectToDatabase } from '@/lib/dbconnect';
// import { Referral } from '@/models/Referral';
// import { Investment } from '@/models/Investment';

// const corsHeaders = {
//   'Access-Control-Allow-Origin': '*',
//   'Access-Control-Allow-Methods': 'GET, OPTIONS',
//   'Access-Control-Allow-Headers': 'Content-Type, Authorization',
// };

// export async function GET(req: NextRequest) {
//   // Handle preflight OPTIONS request
//   if (req.method === 'OPTIONS') {
//     return NextResponse.json(null, { headers: corsHeaders });
//   }

//   // Verify JWT token and ensure admin role
//   const authHeader = req.headers.get('authorization');
//   const token = authHeader?.split(' ')[1];
//   if (!token) {
//     return NextResponse.json(
//       { error: 'Unauthorized' },
//       { status: 401, headers: corsHeaders }
//     );
//   }

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { role: string };
//     if (decoded.role !== 'admin') {
//       return NextResponse.json(
//         { error: 'Forbidden: Admin role required' },
//         { status: 403, headers: corsHeaders }
//       );
//     }

//     await connectToDatabase();

//     // Fetch all referrals
//     const referrals = await Referral.find({})
//       .populate('referrerId', 'name email')
//       .populate('referredUserId', 'name email')
//       .exec();

//     // Determine "active" status for each referral
//     const referralsWithStatus = await Promise.all(
//       referrals.map(async (referral) => {
//         const activeInvestments = await Investment.find({
//           userId: referral.referredUserId,
//           paymentStatus: 'approved',
//         }).exec();

//         return {
//           _id: referral._id,
//           referrer: referral.referrerId,
//           referredUser: referral.referredUserId,
//           referralId: referral.referralId,
//           createdAt: referral.createdAt,
//           active: activeInvestments.length > 0,
//         };
//       })
//     );

//     return NextResponse.json(
//       { referrals: referralsWithStatus },
//       { status: 200, headers: corsHeaders }
//     );
//   } catch (error) {
//     console.error('Error fetching referrals:', error);
//     return NextResponse.json(
//       { error: 'Internal server error' },
//       { status: 500, headers: corsHeaders }
//     );
//   }
// }

// app/api/admin/referrals/route.ts
import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { AdminReferralService } from '@/lib/services/adminReferralService';
import '@/models'; // Import all models

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function GET(req: NextRequest) {
  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  const { searchParams } = req.nextUrl;
  const page = parseInt(searchParams.get('page') || '1', 10);
  const search = searchParams.get('search') || '';
  const sort = searchParams.get('sort') || 'createdAt';
  const order = searchParams.get('order') || 'desc';
  const status = searchParams.get('status') || 'all';
  const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);
  const exportAll = searchParams.get('exportAll') === 'true';
  const useUserModel = searchParams.get('useUserModel') === 'true'; // Option to use User model instead

  console.log('🔍 Admin Referrals API - Request params:', {
    page, search, sort, order, status, pageSize, exportAll, useUserModel
  });

  // Verify admin authentication
  const authHeader = req.headers.get('authorization');
  if (!authHeader) {
    console.error('❌ Missing Authorization header');
    return NextResponse.json(
      { error: 'Unauthorized: Missing Authorization header' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    console.error('❌ Missing token in Authorization header');
    return NextResponse.json(
      { error: 'Unauthorized: Missing token' },
      { status: 401, headers: corsHeaders }
    );
  }

  try {
    // Verify JWT token and admin role
    const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { role: string };
    console.log('🔐 Decoded Token:', { role: decoded.role });

    if (decoded.role !== 'admin') {
      console.error('❌ Forbidden: User does not have admin role');
      return NextResponse.json(
        { error: 'Forbidden: Admin role required' },
        { status: 403, headers: corsHeaders }
      );
    }

    console.log('✅ Admin authentication verified');

    // Prepare filters for the service
    const filters = {
      search,
      status: status as 'all' | 'active' | 'inactive',
      page,
      pageSize: exportAll ? 999999 : pageSize, // Large number for export
      sortBy: sort,
      sortOrder: order as 'asc' | 'desc'
    };

    console.log('📋 Calling AdminReferralService with filters:', filters);

    // Use the appropriate service method
    let result;
    if (useUserModel) {
      console.log('🔄 Using User model approach for referrals');
      result = await AdminReferralService.getAllReferralsFromUsers(filters);
    } else {
      console.log('🔄 Using Referral model approach for referrals');
      result = await AdminReferralService.getAllReferrals(filters);
    }

    console.log('📊 Service result:', {
      referralsCount: result.referrals.length,
      totalCount: result.totalCount,
      stats: result.stats
    });

    // Return comprehensive response
    return NextResponse.json(
      {
        success: true,
        referrals: result.referrals,
        totalPages: result.totalPages,
        currentPage: result.currentPage,
        totalReferrals: result.totalCount,
        pageSize: filters.pageSize,
        actualCount: result.referrals.length,
        isExportMode: exportAll || pageSize >= 1000,
        exportAll: exportAll,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
        stats: result.stats,
        dataSource: useUserModel ? 'user_model' : 'referral_model'
      },
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('❌ Error in admin referrals API:', error);

    // Handle different types of errors
    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized: Invalid token',
          details: error.message
        },
        { status: 401, headers: corsHeaders }
      );
    }

    // Handle service errors
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch referrals',
        details: errorMessage
      },
      { status: 500, headers: corsHeaders }
    );
  }
}
