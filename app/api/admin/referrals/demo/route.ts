// app/api/admin/referrals/demo/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { Investment } from '@/models/Investment';
import '@/models'; // Import all models

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function GET(req: NextRequest) {
  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  const { searchParams } = req.nextUrl;
  const page = parseInt(searchParams.get('page') || '1', 10);
  const search = searchParams.get('search') || '';
  const status = searchParams.get('status') || 'all';
  const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);
  const exportAll = searchParams.get('exportAll') === 'true';

  console.log('🎯 Admin Referrals API - Request params:', {
    page, search, status, pageSize, exportAll
  });

  // Note: Authentication is handled by middleware - if we reach here, user is authenticated admin

  try {
    await connectToDatabase();

    console.log('✅ Database connected, fetching user-based referrals...');

    // Build aggregation pipeline for user-based referrals
    const pipeline: any[] = [
      // Match users who were referred (have a referrerId)
      {
        $match: {
          referrerId: { $ne: null }
        }
      },
      // Lookup referrer details
      {
        $lookup: {
          from: 'users',
          localField: 'referrerId',
          foreignField: '_id',
          as: 'referrer'
        }
      },
      // Unwind referrer
      {
        $unwind: '$referrer'
      },
      // Lookup investments for this referred user
      {
        $lookup: {
          from: 'investments',
          let: { userId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$userId', '$$userId'] },
                    { $eq: ['$paymentStatus', 'approved'] }
                  ]
                }
              }
            }
          ],
          as: 'investments'
        }
      },
      // Add computed fields
      {
        $addFields: {
          active: { $gt: [{ $size: '$investments' }, 0] },
          totalEarnings: {
            $multiply: [
              { $sum: '$investments.amount' },
              0.1 // 10% commission
            ]
          },
          investmentCount: { $size: '$investments' },
          earnings: {
            $map: {
              input: '$investments',
              as: 'investment',
              in: {
                investmentAmount: '$$investment.amount',
                percentage: 10,
                earningAmount: { $multiply: ['$$investment.amount', 0.1] }
              }
            }
          }
        }
      }
    ];

    // Add search filter
    if (search) {
      pipeline.push({
        $match: {
          $or: [
            { 'name': { $regex: search, $options: 'i' } },
            { 'email': { $regex: search, $options: 'i' } },
            { 'referrer.name': { $regex: search, $options: 'i' } },
            { 'referrer.email': { $regex: search, $options: 'i' } },
            { 'referrer.referralCode': { $regex: search, $options: 'i' } }
          ]
        }
      });
    }

    // Add status filter
    if (status !== 'all') {
      pipeline.push({
        $match: {
          active: status === 'active'
        }
      });
    }

    // Get total count before pagination
    const countPipeline = [...pipeline, { $count: 'total' }];
    const countResult = await User.aggregate(countPipeline);
    const totalCount = countResult[0]?.total || 0;

    // Add sorting
    pipeline.push({
      $sort: { createdAt: -1 }
    });

    // Add pagination for non-export requests
    if (!exportAll && pageSize < 1000) {
      pipeline.push(
        { $skip: (page - 1) * pageSize },
        { $limit: pageSize }
      );
    }

    // Project final structure
    pipeline.push({
      $project: {
        _id: 1,
        referrer: {
          _id: '$referrer._id',
          name: '$referrer.name',
          email: '$referrer.email',
          referralCode: '$referrer.referralCode'
        },
        referredUser: {
          _id: '$_id',
          name: '$name',
          email: '$email',
          referralCode: '$referralCode'
        },
        referralId: '$referrer.referralCode',
        createdAt: '$createdAt',
        active: 1,
        earnings: 1,
        totalEarnings: 1,
        investmentCount: 1
      }
    });

    console.log('📊 Executing user-based referral aggregation...');
    
    // Execute aggregation
    const referrals = await User.aggregate(pipeline);
    
    console.log(`✅ Found ${referrals.length} user-based referrals out of ${totalCount} total`);

    // Calculate statistics
    const stats = {
      totalReferrals: totalCount,
      activeReferrals: referrals.filter(r => r.active).length,
      inactiveReferrals: referrals.filter(r => !r.active).length,
      referralsWithUsers: totalCount, // All user-based referrals have users
      pendingReferrals: 0,
      totalEarnings: referrals.reduce((sum, r) => sum + (r.totalEarnings || 0), 0)
    };

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / pageSize);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json(
      {
        success: true,
        referrals,
        totalPages,
        currentPage: page,
        totalReferrals: totalCount,
        pageSize,
        actualCount: referrals.length,
        isExportMode: exportAll || pageSize >= 1000,
        exportAll: exportAll,
        hasNextPage,
        hasPrevPage,
        stats,
        dataSource: 'user_relationships',
        message: 'Admin API - Authentication handled by middleware'
      },
      { status: 200, headers: corsHeaders }
    );

  } catch (error) {
    console.error('❌ Error in demo referrals API:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch referrals',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500, headers: corsHeaders }
    );
  }
}
