// app/api/admin/referrals/test/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { Referral } from '@/models/Referral';
import { Investment } from '@/models/Investment';
import '@/models'; // Import all models

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function GET(req: NextRequest) {
  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  try {
    console.log('🧪 TEST: Starting basic referral system test...');

    // Test database connection
    await connectToDatabase();
    console.log('✅ Database connection successful');

    // Test basic counts without authentication first
    const [userCount, referralCount, investmentCount] = await Promise.all([
      User.countDocuments(),
      Referral.countDocuments(),
      Investment.countDocuments()
    ]);

    console.log('📊 Basic counts:', { userCount, referralCount, investmentCount });

    // Test User model referral relationships
    const usersWithReferrers = await User.countDocuments({ referrerId: { $ne: null } });
    console.log('👥 Users with referrers:', usersWithReferrers);

    // Get sample users with referrers (limit 3)
    const sampleUsersWithReferrers = await User.find({ referrerId: { $ne: null } })
      .populate('referrerId', 'name email referralCode')
      .select('name email referralCode referrerId createdAt')
      .limit(3)
      .lean();

    console.log('👤 Sample users with referrers:', sampleUsersWithReferrers);

    // Get sample referral documents (limit 3)
    const sampleReferrals = await Referral.find()
      .populate('referrerId', 'name email referralCode')
      .populate('referredUserId', 'name email referralCode')
      .limit(3)
      .lean();

    console.log('📋 Sample referral documents:', sampleReferrals);

    // Test aggregation for user-based referrals
    const userBasedReferrals = await User.aggregate([
      { $match: { referrerId: { $ne: null } } },
      {
        $lookup: {
          from: 'users',
          localField: 'referrerId',
          foreignField: '_id',
          as: 'referrer'
        }
      },
      { $unwind: '$referrer' },
      {
        $project: {
          referredUser: {
            name: '$name',
            email: '$email'
          },
          referrer: {
            name: '$referrer.name',
            email: '$referrer.email',
            referralCode: '$referrer.referralCode'
          },
          createdAt: '$createdAt'
        }
      },
      { $limit: 3 }
    ]);

    console.log('🔄 User-based referral aggregation result:', userBasedReferrals);

    // Determine best data source
    let primaryDataSource = 'none';
    let dataQuality = {
      referralCollection: 'empty',
      userRelationships: 'empty',
      investmentData: investmentCount > 0 ? 'available' : 'empty'
    };

    if (referralCount > 0) {
      primaryDataSource = 'referral_collection';
      dataQuality.referralCollection = 'available';
    } else if (usersWithReferrers > 0) {
      primaryDataSource = 'user_relationships';
      dataQuality.userRelationships = 'available';
    }

    const hasReferralData = referralCount > 0 || usersWithReferrers > 0;

    return NextResponse.json(
      {
        success: true,
        message: 'Basic referral system test completed',
        counts: {
          users: userCount,
          referrals: referralCount,
          investments: investmentCount,
          usersWithReferrers: usersWithReferrers
        },
        samples: {
          usersWithReferrers: sampleUsersWithReferrers,
          referralDocuments: sampleReferrals,
          userBasedReferrals: userBasedReferrals
        },
        recommendations: {
          primaryDataSource,
          hasReferralData,
          dataQuality,
          suggestedApproach: hasReferralData 
            ? (primaryDataSource === 'referral_collection' ? 'Use Referral collection' : 'Use User relationships')
            : 'No referral data found - system may need setup'
        }
      },
      { headers: corsHeaders }
    );

  } catch (error) {
    console.error('❌ Test endpoint error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Test failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500, headers: corsHeaders }
    );
  }
}
