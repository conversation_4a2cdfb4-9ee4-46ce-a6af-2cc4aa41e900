import { NextResponse } from 'next/server';
import { User } from '@/models/User';
import { connectToDatabase } from '@/lib/dbconnect'; 

export async function GET() {
  try {
    await connectToDatabase();
    
    const total = await User.countDocuments({ role: 'investor' });
    const newThisMonth = await User.countDocuments({
      role: 'investor',
      createdAt: {
        $gte: new Date(new Date().setDate(1)), // First day of current month
      },
    });

    return NextResponse.json({
      total,
      newThisMonth,
    });
  } catch (error) {
    console.error('Error fetching investor stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch investor statistics' },
      { status: 500 }
    );
  }
}

