// import { NextResponse } from 'next/server';
// import { Referral } from '@/models/Referral';
// import { ReferralPayments } from '@/models/ReferralPayments';
// import { connectToDatabase } from '@/lib/dbconnect';

// interface ChartDataPoint {
//   name: string;
//   value: number;
// }

// interface ChartData {
//   totalReferrals: ChartDataPoint[];
//   activeReferrers: ChartDataPoint[];
//   totalEarnings: ChartDataPoint[];
//   conversionRate: ChartDataPoint[];
// }

// export async function GET() {
//   try {
//     await connectToDatabase();
    
//     const totalReferrals = await Referral.countDocuments();
//     const activeReferrers = (await Referral.distinct('referrerId')).length;
    
//     const earningsData = await ReferralPayments.aggregate([
//       { $group: { _id: null, totalEarnings: { $sum: '$amountEarned' } } }
//     ]);
//     const totalEarnings = earningsData.length > 0 ? earningsData[0].totalEarnings : 0;

//     const conversionRate = await calculateConversionRate();

//     // Calculate trends and historical data (last 6 months)
//     const sixMonthsAgo = new Date();
//     sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 5);
    
//     const monthlyStats = await Referral.aggregate([
//       {
//         $match: {
//           createdAt: { $gte: sixMonthsAgo }
//         }
//       },
//       {
//         $group: {
//           _id: { $dateToString: { format: "%Y-%m", date: "$createdAt" } },
//           totalCount: { $sum: 1 },
//           uniqueReferrers: { $addToSet: '$referrerId' },
//         }
//       },
//       {
//         $sort: { _id: 1 }
//       }
//     ]);

//     const monthlyEarnings = await ReferralPayments.aggregate([
//       {
//         $match: {
//           createdAt: { $gte: sixMonthsAgo }
//         }
//       },
//       {
//         $group: {
//           _id: { $dateToString: { format: "%Y-%m", date: "$createdAt" } },
//           earnings: { $sum: '$amountEarned' },
//         }
//       },
//       {
//         $sort: { _id: 1 }
//       }
//     ]);

//     const chartData: ChartData = {
//       totalReferrals: [],
//       activeReferrers: [],
//       totalEarnings: [],
//       conversionRate: []
//     };

//     let cumulativeReferrals = 0;
//     let cumulativeEarnings = 0;

//     monthlyStats.forEach((stat, index) => {
//       cumulativeReferrals += stat.totalCount;
//       const monthEarnings = monthlyEarnings.find(e => e._id === stat._id)?.earnings || 0;
//       cumulativeEarnings += monthEarnings;

//       chartData.totalReferrals.push({ name: stat._id, value: cumulativeReferrals });
//       chartData.activeReferrers.push({ name: stat._id, value: stat.uniqueReferrers.length });
//       chartData.totalEarnings.push({ name: stat._id, value: cumulativeEarnings });
//       chartData.conversionRate.push({ name: stat._id, value: calculateMonthlyConversionRate(stat._id) });
//     });

//     // Calculate trends
//     const currentMonth = monthlyStats[monthlyStats.length - 1];
//     const previousMonth = monthlyStats[monthlyStats.length - 2];

//     const calculateTrend = (current: number, previous: number | undefined) => {
//       return previous ? ((current - previous) / previous * 100).toFixed(1) + '%' : '+0%';
//     };

//     const trends = {
//       total: calculateTrend(currentMonth.totalCount, previousMonth?.totalCount),
//       active: calculateTrend(currentMonth.uniqueReferrers.length, previousMonth?.uniqueReferrers.length),
//       earnings: calculateTrend(
//         monthlyEarnings[monthlyEarnings.length - 1]?.earnings || 0,
//         monthlyEarnings[monthlyEarnings.length - 2]?.earnings
//       ),
//       conversion: calculateTrend(
//         await calculateMonthlyConversionRate(currentMonth._id),
//         await calculateMonthlyConversionRate(previousMonth?._id)
//       ),
//     };

//     return NextResponse.json({
//       totalReferrals,
//       activeReferrers,
//       totalEarnings,
//       conversionRate,
//       trends,
//       chartData
//     });
//   } catch (error) {
//     console.error('Error fetching detailed referral stats:', error);
//     return NextResponse.json(
//       { error: 'Failed to fetch detailed referral statistics' },
//       { status: 500 }
//     );
//   }
// }

// async function calculateConversionRate(): Promise<number> {
//   const totalReferrals = await Referral.countDocuments();
//   const convertedReferrals = await Referral.countDocuments({ referredUserId: { $exists: true } });
//   return totalReferrals > 0 ? (convertedReferrals / totalReferrals) * 100 : 0;
// }

// async function calculateMonthlyConversionRate(monthYear: string): Promise<number> {
//   const [year, month] = monthYear.split('-');
//   const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
//   const endDate = new Date(parseInt(year), parseInt(month), 0);

//   const totalReferrals = await Referral.countDocuments({
//     createdAt: { $gte: startDate, $lte: endDate }
//   });
//   const convertedReferrals = await Referral.countDocuments({
//     createdAt: { $gte: startDate, $lte: endDate },
//     referredUserId: { $exists: true }
//   });

//   return totalReferrals > 0 ? (convertedReferrals / totalReferrals) * 100 : 0;
// }




import { NextResponse } from 'next/server';
import { Referral } from '@/models/Referral';
import { ReferralPayments } from '@/models/ReferralPayments';
import { connectToDatabase } from '@/lib/dbconnect';

interface ChartDataPoint {
  name: string;
  value: number;
}

interface ChartData {
  totalReferrals: ChartDataPoint[];
  activeReferrers: ChartDataPoint[];
  totalEarnings: ChartDataPoint[];
  conversionRate: ChartDataPoint[];
}

export async function GET() {
  try {
    await connectToDatabase();
    
    const totalReferrals = await Referral.countDocuments();
    const activeReferrers = (await Referral.distinct('referrerId')).length;
    
    const earningsData = await ReferralPayments.aggregate([
      { $group: { _id: null, totalEarnings: { $sum: '$amountEarned' } } }
    ]);
    const totalEarnings = earningsData.length > 0 ? earningsData[0].totalEarnings : 0;

    const conversionRate = await calculateConversionRate();

    // Calculate trends and historical data (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 5);
    
    const monthlyStats = await Referral.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: "%Y-%m", date: "$createdAt" } },
          totalCount: { $sum: 1 },
          uniqueReferrers: { $addToSet: '$referrerId' },
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    const monthlyEarnings = await ReferralPayments.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: "%Y-%m", date: "$createdAt" } },
          earnings: { $sum: '$amountEarned' },
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    const chartData: ChartData = {
      totalReferrals: [],
      activeReferrers: [],
      totalEarnings: [],
      conversionRate: []
    };

    let cumulativeReferrals = 0;
    let cumulativeEarnings = 0;

    const conversionRatePromises = monthlyStats.map(async (stat) => {
      cumulativeReferrals += stat.totalCount;
      const monthEarnings = monthlyEarnings.find(e => e._id === stat._id)?.earnings || 0;
      cumulativeEarnings += monthEarnings;

      chartData.totalReferrals.push({ name: stat._id, value: cumulativeReferrals });
      chartData.activeReferrers.push({ name: stat._id, value: stat.uniqueReferrers.length });
      chartData.totalEarnings.push({ name: stat._id, value: cumulativeEarnings });

      const monthlyConversionRate = await calculateMonthlyConversionRate(stat._id);
      return { name: stat._id, value: monthlyConversionRate };
    });

    chartData.conversionRate = await Promise.all(conversionRatePromises);

    // Calculate trends
    const currentMonth = monthlyStats[monthlyStats.length - 1];
    const previousMonth = monthlyStats[monthlyStats.length - 2];

    const calculateTrend = (current: number, previous: number | undefined) => {
      return previous ? ((current - previous) / previous * 100).toFixed(1) + '%' : '+0%';
    };

    const currentConversionRate = await calculateMonthlyConversionRate(currentMonth._id);
    const previousConversionRate = previousMonth ? await calculateMonthlyConversionRate(previousMonth._id) : undefined;

    const trends = {
      total: calculateTrend(currentMonth.totalCount, previousMonth?.totalCount),
      active: calculateTrend(currentMonth.uniqueReferrers.length, previousMonth?.uniqueReferrers.length),
      earnings: calculateTrend(
        monthlyEarnings[monthlyEarnings.length - 1]?.earnings || 0,
        monthlyEarnings[monthlyEarnings.length - 2]?.earnings
      ),
      conversion: calculateTrend(currentConversionRate, previousConversionRate),
    };

    return NextResponse.json({
      totalReferrals,
      activeReferrers,
      totalEarnings,
      conversionRate,
      trends,
      chartData
    });
  } catch (error) {
    console.error('Error fetching detailed referral stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch detailed referral statistics' },
      { status: 500 }
    );
  }
}

async function calculateConversionRate(): Promise<number> {
  const totalReferrals = await Referral.countDocuments();
  const convertedReferrals = await Referral.countDocuments({ referredUserId: { $exists: true } });
  return totalReferrals > 0 ? (convertedReferrals / totalReferrals) * 100 : 0;
}

async function calculateMonthlyConversionRate(monthYear: string): Promise<number> {
  const [year, month] = monthYear.split('-');
  const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
  const endDate = new Date(parseInt(year), parseInt(month), 0);

  const totalReferrals = await Referral.countDocuments({
    createdAt: { $gte: startDate, $lte: endDate }
  });
  const convertedReferrals = await Referral.countDocuments({
    createdAt: { $gte: startDate, $lte: endDate },
    referredUserId: { $exists: true }
  });

  return totalReferrals > 0 ? (convertedReferrals / totalReferrals) * 100 : 0;
}

