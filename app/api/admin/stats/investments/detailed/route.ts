import { NextResponse } from 'next/server';
import { Investment } from '@/models/Investment';
import { connectToDatabase } from '@/lib/dbconnect'; 

export async function GET() {
  try {
    await connectToDatabase();
    
    // Get all approved investments
    const investments = await Investment.find({
      paymentStatus: 'approved'
    });
    
    // Calculate total value
    const totalValue = investments.reduce((sum, inv) => sum + inv.amount, 0);
    
    // Calculate average investment size
    const averageSize = totalValue / (investments.length || 1);
    
    // Calculate success rate (approved vs total)
    const totalInvestments = await Investment.countDocuments();
    const successRate = ((investments.length / totalInvestments) * 100).toFixed(1);
    
    // Get active investments count
    const activeInvestments = await Investment.countDocuments({
      paymentStatus: 'approved',
      endDate: { $gt: new Date() }
    });

    // Calculate trends (comparing to last month)
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    
    const lastMonthInvestments = await Investment.find({
      paymentStatus: 'approved',
      createdAt: {
        $gte: lastMonth,
        $lt: new Date(new Date().setDate(1))
      }
    });

    const lastMonthTotal = lastMonthInvestments.reduce((sum, inv) => sum + inv.amount, 0);
    const lastMonthAvg = lastMonthTotal / (lastMonthInvestments.length || 1);
    const lastMonthSuccess = await Investment.countDocuments({
      paymentStatus: 'approved',
      createdAt: {
        $gte: lastMonth,
        $lt: new Date(new Date().setDate(1))
      }
    });
    const lastMonthActive = await Investment.countDocuments({
      paymentStatus: 'approved',
      endDate: { $gt: lastMonth },
      createdAt: { $lt: new Date(new Date().setDate(1)) }
    });

    // Calculate trend percentages
    const trends = {
      value: `${(((totalValue - lastMonthTotal) / lastMonthTotal) * 100).toFixed(1)}%`,
      size: `${(((averageSize - lastMonthAvg) / lastMonthAvg) * 100).toFixed(1)}%`,
      success: `${(((investments.length - lastMonthSuccess) / lastMonthSuccess) * 100).toFixed(1)}%`,
      active: `${(((activeInvestments - lastMonthActive) / lastMonthActive) * 100).toFixed(1)}%`
    };

    return NextResponse.json({
      totalValue,
      averageSize,
      successRate,
      activeInvestments,
      trends
    });
  } catch (error) {
    console.error('Error fetching detailed investment stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch detailed investment statistics' },
      { status: 500 }
    );
  }
}

