

// app/api/files/[id]/route.ts

import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import { GridFSBucket } from 'mongodb';
import { connectToDatabase } from '@/lib/dbconnect';
import '@/models'; // Import all models

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  // Ensure MongoDB connection
  await connectToDatabase();
  
  // Retrieve the database instance from Mongoose
  const db = mongoose.connection.db;

  // Check if the database connection is established
  if (!db) {
    return NextResponse.json({ error: 'Database connection not established' }, { status: 500 });
  }

  try {
    // Initialize GridFS bucket with the specified bucket name
    const bucket = new GridFSBucket(db, { bucketName: 'uploads' });

    // Retrieve the file ID from the route parameters
    const fileId = new mongoose.Types.ObjectId(params.id);

    // Find the file in GridFS
    const file = await bucket.find({ _id: fileId }).toArray();

    // Check if the file was found
    if (file.length === 0) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }

    // Get file metadata
    const [fileMetadata] = file;
    const contentType = fileMetadata.contentType || 'application/octet-stream';

    // Create a stream to read the file from GridFS
    const downloadStream = bucket.openDownloadStream(fileId);

    // Use a ReadableStream to pipe the download stream to the response
    const stream = new ReadableStream({
      start(controller) {
        downloadStream.on('data', (chunk) => controller.enqueue(chunk));
        downloadStream.on('end', () => controller.close());
        downloadStream.on('error', (error) => {
          console.error('Error streaming file from GridFS:', error);
          controller.error(error);
        });
      },
    });

    // Set response headers for the image
    const headers = {
      'Content-Type': contentType,
      'Cache-Control': 'max-age=31536000, immutable',
    };

    return new NextResponse(stream, { headers });
  } catch (error) {
    console.error('Error retrieving file:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
