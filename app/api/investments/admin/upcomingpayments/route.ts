// app/api/investments/admin/upcomingpayments/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Investment } from '@/models/Investment';
import '@/models'; // Import all models

export async function GET(req: NextRequest) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  try {
    await connectToDatabase();

    const investments = await Investment.find({
      paymentStatus: { $in: ['approved', 'pending'] },
    })
      .populate('userId', 'name email') // Include user details
      .populate('planId', 'title') // Include plan details
      .lean();

    return NextResponse.json(
      { investments },
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('[Admin Investments API] Error fetching investments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch investments' },
      { status: 500, headers: corsHeaders }
    );
  }
}
