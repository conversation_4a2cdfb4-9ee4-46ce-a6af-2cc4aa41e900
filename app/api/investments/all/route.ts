// import { NextResponse } from 'next/server';
// import { connectToDatabase } from '@/lib/dbconnect';
// import { Investment } from '@/models/Investment';
// import '@/models'; // Import all models

// const corsHeaders = {
//   'Access-Control-Allow-Origin': '*', // Adjust this in production
//   'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
//   'Access-Control-Allow-Headers': 'Content-Type, Authorization',
// };

// export async function OPTIONS() {
//   return new NextResponse(null, { headers: corsHeaders });
// }

// export async function GET() {
//   await connectToDatabase(); // Ensure database connection is established

//   try {
//     // Fetch all investments from the database
//     const investments = await Investment.find()
//       .populate('userId', 'name email') // Populate user details if needed
//       .populate('planId', 'title percentage') // Populate plan details if needed
//       .exec();

//     // Calculate statistics for payment statuses
//     const statistics = investments.reduce(
//       (acc, investment) => {
//         acc.total += 1;
//         if (investment.paymentStatus === 'approved') {
//           acc.approved += 1;
//         } else if (investment.paymentStatus === 'pending') {
//           acc.pending += 1;
//         } else if (investment.paymentStatus === 'declined') {
//           acc.declined += 1;
//         } else if (investment.paymentStatus === 'declinedWithReason') {
//           acc.declinedWithReason += 1;
//         } else if (investment.paymentStatus === 'pendingWithNotice') {
//           acc.pendingWithNotice += 1;
//         }
//         return acc;
//       },
//       {
//         total: 0,
//         approved: 0,
//         pending: 0,
//         declined: 0,
//         declinedWithReason: 0,
//         pendingWithNotice: 0,
//       }
//     );

//     // Return investments and statistics
//     return NextResponse.json(
//       { investments, statistics },
//       { status: 200, headers: corsHeaders }
//     );
//   } catch (error) {
//     console.error('Error fetching investments:', error);
//     return NextResponse.json(
//       { error: 'Failed to fetch investments' },
//       { status: 500, headers: corsHeaders }
//     );
//   }
// }


// app/api/investments/all/route.ts
import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Investment } from '@/models/Investment';
import '@/models'; // Import all models

const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Adjust this in production
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function OPTIONS() {
  return new NextResponse(null, { headers: corsHeaders });
}

export async function GET() {
  await connectToDatabase(); // Ensure database connection is established

  try {
    // Fetch all investments from the database
    const investments = await Investment.find()
      .populate('userId', 'name email') // Populate user details if needed
      .populate('planId', 'title percentage') // Populate plan details if needed
      .exec();

    // Calculate statistics for payment statuses and amounts
    const statistics = investments.reduce(
      (acc, investment) => {
        const amount = investment.amount || 0; // Default to 0 if amount is null/undefined
        acc.total += 1;
        acc.totalAmount += amount;

        if (investment.paymentStatus === 'approved') {
          acc.approvedCount += 1;
          acc.approvedAmount += amount;
        } else if (investment.paymentStatus === 'pending') {
          acc.pendingCount += 1;
          acc.pendingAmount += amount;
        } else if (investment.paymentStatus === 'declined') {
          acc.declinedCount += 1;
          acc.declinedAmount += amount;
        } else if (investment.paymentStatus === 'declinedWithReason') {
          acc.declinedWithReasonCount += 1;
          acc.declinedWithReasonAmount += amount;
        } else if (investment.paymentStatus === 'pendingWithNotice') {
          acc.pendingWithNoticeCount += 1;
          acc.pendingWithNoticeAmount += amount;
        }

        return acc;
      },
      {
        total: 0,
        totalAmount: 0,
        approvedCount: 0,
        approvedAmount: 0,
        pendingCount: 0,
        pendingAmount: 0,
        declinedCount: 0,
        declinedAmount: 0,
        declinedWithReasonCount: 0,
        declinedWithReasonAmount: 0,
        pendingWithNoticeCount: 0,
        pendingWithNoticeAmount: 0,
      }
    );

    // Return investments and statistics
    return NextResponse.json(
      { investments, statistics },
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error fetching investments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch investments' },
      { status: 500, headers: corsHeaders }
    );
  }
}
