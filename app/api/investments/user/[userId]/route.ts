// app/api/investments/user/[userId]/route.ts
import { NextResponse, NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Investment } from '@/models/Investment';
import jwt from 'jsonwebtoken';

import '@/models'; // Import all models

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

export async function GET(request: NextRequest, { params }: { params: { userId: string } }) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*', // Adjust in production
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  // Handle preflight OPTIONS request
  if (request.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  // Verify JWT
  const authHeader = request.headers.get('Authorization');
  if (!authHeader) {
    return NextResponse.json(
      { error: 'Authorization header missing' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.split(' ')[1]; // e.g. Bearer <token>
  if (!token) {
    return NextResponse.json(
      { error: 'Token missing' },
      { status: 401, headers: corsHeaders }
    );
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { id: string; role: string };
    if (decoded.id !== params.userId && decoded.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden: You are not authorized to access this resource' },
        { status: 403, headers: corsHeaders }
      );
    }
  } catch (error) {
    console.error('Invalid token:', error);
    return NextResponse.json(
      { error: 'Invalid or expired token. Please log in again.' },
      { status: 401, headers: corsHeaders }
    );
  }

  try {
    // Connect to DB
    await connectToDatabase();

    // Find the user's investments
    const investments = await Investment.find({ userId: params.userId })
      .populate('planId')
      .populate('subscriptionId')
      .exec();

    // Return 200 with empty array if none
    if (investments.length === 0) {
      return NextResponse.json(
        { investments: [] },
        { status: 200, headers: corsHeaders }
      );
    }

    // Otherwise return found investments
    return NextResponse.json({ investments }, { status: 200, headers: corsHeaders });

  } catch (error) {
    console.error('Error fetching investments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch investments. Please try again later.' },
      { status: 500, headers: corsHeaders }
    );
  }
}
