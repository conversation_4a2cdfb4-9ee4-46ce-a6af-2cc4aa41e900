import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Investment } from '@/models/Investment';
import '@/models'; // Import all models
import mongoose from 'mongoose';

export async function DELETE(request: NextRequest) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  try {
    const { ids } = await request.json();

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'Invalid or empty IDs array' },
        { status: 400, headers: corsHeaders }
      );
    }

    // Validate that all IDs are valid ObjectIds
    const validIds = ids.filter(id => {
      try {
        return mongoose.Types.ObjectId.isValid(id);
      } catch {
        return false;
      }
    });

    if (validIds.length === 0) {
      return NextResponse.json(
        { error: 'No valid IDs provided' },
        { status: 400, headers: corsHeaders }
      );
    }

    await connectToDatabase();

    // Convert string IDs to ObjectIds
    const objectIds = validIds.map(id => new mongoose.Types.ObjectId(id));

    // Delete the investments using Mongoose
    const result = await Investment.deleteMany({
      _id: { $in: objectIds }
    });

    console.log(`Bulk delete result:`, result);

    return NextResponse.json(
      {
        success: true,
        deletedCount: result.deletedCount,
        message: `Successfully deleted ${result.deletedCount} investment(s)`
      },
      { headers: corsHeaders }
    );

  } catch (error) {
    console.error('Error in bulk delete:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    );
  }
}

export async function OPTIONS() {
  return NextResponse.json(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
