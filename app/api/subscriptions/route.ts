// // app/api/subscriptions/route.ts
// import { NextResponse } from 'next/server';
// import { connectToDatabase } from '@/lib/dbconnect';
// import { Subscription } from '@/models/Subscription';
// import jwt, { JwtPayload } from 'jsonwebtoken';
// import '@/models'; // Import all models

// const corsHeaders = {
//   'Access-Control-Allow-Origin': '*', // Replace '*' with your frontend URL in production
//   'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
//   'Access-Control-Allow-Headers': 'Content-Type, Authorization',
// };

// interface DecodedToken extends JwtPayload {
//   id: string;
//   name: string;
//   email: string;
//   role: string; // e.g., 'admin', 'user'
// }

// export async function OPTIONS() {
//   return new NextResponse(null, { headers: corsHeaders });
// }

// export async function GET(request: Request) {
//   const token = request.headers.get('authorization')?.split(' ')[1];
//   console.log('Received token:', token);

//   if (!token) {
//     return NextResponse.json({ error: 'Authorization token missing' }, { status: 401, headers: corsHeaders });
//   }

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as DecodedToken;
//     console.log('Decoded token:', decoded);
//     const userId = decoded.id;
//     const userRole = decoded.role;

//     if (!userId) {
//       return NextResponse.json({ error: 'Invalid token or user not found' }, { status: 401, headers: corsHeaders });
//     }

//     // Authorization: Only admins can fetch all subscriptions
//     if (userRole !== 'admin') {
//       return NextResponse.json({ error: 'Access denied. Admins only.' }, { status: 403, headers: corsHeaders });
//     }

//     await connectToDatabase();

//     const subscriptions = await Subscription.find().populate('plan'); // Assuming 'plan' is a ref in Subscription model
//     return NextResponse.json({ subscriptions }, { headers: corsHeaders });
//   } catch (error) {
//     console.error('Error fetching subscriptions:', error);
//     if (error instanceof jwt.JsonWebTokenError) {
//       return NextResponse.json({ error: 'Invalid token' }, { status: 401, headers: corsHeaders });
//     }
//     return NextResponse.json({ error: 'Internal Server Error' }, { status: 500, headers: corsHeaders });
//   }
// }




// app/api/subscriptions/route.ts

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Subscription } from '@/models/Subscription';
import jwt, { JwtPayload } from 'jsonwebtoken';
import '@/models'; // Import all models

import { getCorsHeaders } from '@/lib/cors'; // Centralized CORS handling

interface DecodedToken extends JwtPayload {
  id: string;
  name: string;
  email: string;
  role: string; // e.g., 'admin', 'user'
}

/**
 * **OPTIONS Handler**
 * Handles preflight CORS requests by returning appropriate headers.
 */
export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

/**
 * **GET Handler**
 * Fetches all subscriptions. Only accessible by admin users.
 */
export async function GET(request: Request) {
  const origin = request.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  const authHeader = request.headers.get('authorization');
  const token = authHeader?.split(' ')[1];
  console.log('Received token:', token);

  if (!token) {
    return NextResponse.json(
      { error: 'Authorization token missing' },
      { status: 401, headers: corsHeaders }
    );
  }

  try {
    // Verify the JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as DecodedToken;
    console.log('Decoded token:', decoded);
    const userId = decoded.id;
    const userRole = decoded.role;

    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid token or user not found' },
        { status: 401, headers: corsHeaders }
      );
    }

    // Authorization: Only admins can fetch all subscriptions
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Access denied. Admins only.' },
        { status: 403, headers: corsHeaders }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Fetch all subscriptions and populate the 'plan' field
    const subscriptions = await Subscription.find().populate('plan'); // Assuming 'plan' is a ref in Subscription model

    return NextResponse.json(
      { subscriptions },
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error('Error fetching subscriptions:', error);

    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401, headers: corsHeaders }
      );
    }

    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500, headers: corsHeaders }
    );
  }
}
