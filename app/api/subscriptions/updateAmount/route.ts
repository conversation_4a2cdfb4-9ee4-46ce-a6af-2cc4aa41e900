// app/api/subscriptions/updateAmount/route.ts

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Subscription } from '@/models/Subscription';
import '@/models'; // Import all models

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function POST(req: Request) {
  await connectToDatabase();
  const { subscriptionId, amount } = await req.json();

  if (!subscriptionId || amount === undefined) {
    return NextResponse.json({ error: 'Subscription ID and amount are required' }, { status: 400, headers: corsHeaders });
  }

  try {
    const subscription = await Subscription.findById(subscriptionId);
    if (!subscription) {
      return NextResponse.json({ error: 'Subscription not found' }, { status: 404, headers: corsHeaders });
    }

    subscription.amount = amount;
    await subscription.save();

    return NextResponse.json({ success: true, subscription }, { status: 200, headers: corsHeaders });
  } catch (error) {
    console.error('Error updating subscription amount:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500, headers: corsHeaders });
  }
}
