

// app/api/subscriptions/user/[userId]/route.ts

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Subscription } from '@/models/Subscription';
import jwt from 'jsonwebtoken';

import '@/models'; // Import all models
// Ensure database connection
 connectToDatabase();

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';

export async function GET(request: Request, { params }: { params: { userId: string } }) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*', // Adjust this in production
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  // Handle preflight OPTIONS request
  if (request.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  // Extract and verify JWT token from headers
  const authHeader = request.headers.get('Authorization');
  if (!authHeader) {
    return NextResponse.json({ error: 'Authorization header missing' }, { status: 401, headers: corsHeaders });
  }

  const token = authHeader.split(' ')[1]; // Assuming 'Bearer <token>'
  if (!token) {
    return NextResponse.json({ error: 'Token missing' }, { status: 401, headers: corsHeaders });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { id: string; role: string };
    if (decoded.id !== params.userId && decoded.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403, headers: corsHeaders });
    }
  } catch (error) {
    console.error('Invalid token:', error);
    return NextResponse.json({ error: 'Invalid token' }, { status: 401, headers: corsHeaders });
  }

  try {
    // Populate 'plan' field with Plan details
    const subscriptions = await Subscription.find({ userId: params.userId })
      .populate('plan') // Ensures 'plan' field contains the Plan document
      .exec();
    return NextResponse.json({ subscriptions }, { status: 200, headers: corsHeaders });
  } catch (error) {
    console.error('Error fetching subscriptions:', error);
    return NextResponse.json({ error: 'Failed to fetch subscriptions' }, { status: 500, headers: corsHeaders });
  }
}



