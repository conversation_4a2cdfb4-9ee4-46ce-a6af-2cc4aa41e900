// app/api/subscriptions/all/route.ts

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Subscription } from '@/models/Subscription';
import '@/models'; // Import all models

const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Adjust this in production
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function OPTIONS() {
  return new NextResponse(null, { headers: corsHeaders });
}

export async function GET() {
  await connectToDatabase(); // Ensure database connection is established

  try {
    const subscriptions = await Subscription.find().populate('plan'); // Populate 'plan' to get details
    return NextResponse.json({ subscriptions }, { status: 200, headers: corsHeaders });
  } catch (error) {
    console.error('Error fetching subscriptions:', error);
    return NextResponse.json({ error: 'Failed to fetch subscriptions' }, { status: 500, headers: corsHeaders });
  }
}
