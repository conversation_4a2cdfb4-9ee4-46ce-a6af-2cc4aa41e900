// app/api/monthly-payments/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { MonthlyPayment } from '@/models/MonthlyPayment'; 
import { verifyToken } from '@/lib/tokenUtils';
import { connectToDatabase } from '@/lib/dbconnect';
import '@/models'; // Import all models

export async function GET(req: NextRequest) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  const authHeader = req.headers.get('authorization');
  if (!authHeader) {
    return NextResponse.json(
      { error: 'Unauthorized: Missing token' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.split(' ')[1];
  let userId: string;

  try {
    const decoded = verifyToken(token, process.env.JWT_SECRET!);
    userId = decoded.id;
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid or expired token' },
      { status: 401, headers: corsHeaders }
    );
  }

  try {
    await connectToDatabase();

    // Fetch monthly payments for the logged-in user
    const payments = await MonthlyPayment.find({ userId })
      .populate('planId', 'title')
      .sort({ nextDueDate: 1 }) // Sort by the nearest due date
      .exec();

    return NextResponse.json({ payments }, { headers: corsHeaders });
  } catch (error) {
    console.error('Error fetching monthly payments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch monthly payments' },
      { status: 500, headers: corsHeaders }
    );
  }
}
