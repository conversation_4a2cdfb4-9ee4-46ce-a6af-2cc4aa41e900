

// //app/api/downloads/download.ts
// import { NextApiRequest, NextApiResponse } from 'next';

// export default function handler(req: NextApiRequest, res: NextApiResponse) {
//   const googleDriveLink = 'https://play.google.com/store/apps/details?id=com.orangecloudtechnologies.mall97';
//   res.redirect(googleDriveLink);
// }

// import { NextResponse } from 'next/server';

// export const GET = async () => {
//   const googleDriveLink = 'https://play.google.com/store/apps/details?id=com.orangecloudtechnologies.mall97';
  

//   // Redirect the user to the Google Drive APK link
//   return NextResponse.redirect(googleDriveLink);
// };


// import { NextApiRequest, NextApiResponse } from 'next';

// export default function handler(req: NextApiRequest, res: NextApiResponse) {
//   const googleDriveLink = 'https://drive.google.com/uc?export=download&id=1PVfWg3FsDbEaz3G6ZplL_tV8iT-scypj';
//   res.redirect(googleDriveLink);
// }


// https://drive.google.com/file/d/16sp3pZTdkJOe-O3q6b9rORTEr4xxU2fv/view?usp=sharing



// app/api/downloads/route.ts
// import { NextResponse } from 'next/server';

// export const GET = async () => {
//   const googleDriveLink = 'https://drive.google.com/uc?export=download&id=16sp3pZTdkJOe-O3q6b9rORTEr4xxU2fv';

//   // Redirect the user to the Google Drive APK link
//   return NextResponse.redirect(googleDriveLink);
// };


// https://drive.google.com/file/d/1_ifObnHZnUvcQOBPN_WDeKu3dr5UiAp6/view?usp=sharing


// // app/api/downloads/route.ts
// import { NextResponse } from 'next/server';

// export const GET = async () => {
//   // New direct download link
//   const googleDriveLink = 'https://drive.google.com/uc?export=download&id=1_ifObnHZnUvcQOBPN_WDeKu3dr5UiAp6';

//   // Redirect the user to the Google Drive APK link
//   return NextResponse.redirect(googleDriveLink);
// };



// // app/api/downloads/route.ts
// import { NextResponse } from 'next/server';

// export const GET = async () => {
//   // Updated direct download link
//   const googleDriveLink = 'https://drive.google.com/uc?export=download&id=1lyzODIjU_oLGH-wzSkFl1tgk9II6TtSv';

//   // Redirect the user to the Google Drive APK link
//   return NextResponse.redirect(googleDriveLink);
// };


import { NextResponse } from 'next/server';

export const GET = async () => {
  // Correct direct download link
  const googleDriveLink = 'https://drive.google.com/uc?export=download&id=1tLwSm1xnSwmFw-j-mlcz4DTou7b3HtwB';

  // Redirect the user to the Google Drive APK link
  return NextResponse.redirect(googleDriveLink);
};
