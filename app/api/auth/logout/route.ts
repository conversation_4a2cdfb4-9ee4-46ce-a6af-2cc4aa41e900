
// app/api/auth/logout/route.ts

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { verify } from 'jsonwebtoken';
import { NextRequest } from 'next/server';
import { getCorsHeaders } from '@/lib/cors';
import rateLimiter from '@/lib/rateLimit';
import { getClientIp } from '@/lib/getClientIp';

// Define the DecodedToken interface
interface DecodedToken {
  id: string; // User ID
  role: string; // User role
  iat?: number; // Issued at
  exp?: number; // Expiration time
}

const REFRESH_TOKEN_SECRET = process.env.REFRESH_TOKEN_SECRET!;

export async function POST(req: Request) {
  const nextReq = req as unknown as NextRequest; // Type assertion to NextRequest
  const clientIp = getClientIp(nextReq);
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  // Handle preflight (OPTIONS) requests
  if (nextReq.method === 'OPTIONS') {
    return NextResponse.json({}, { headers: corsHeaders, status: 200 });
  }

  // Apply rate limiting
  if (!rateLimiter.allowRequest(clientIp)) {
    console.warn(`Rate limit exceeded for IP: ${clientIp}`);
    return NextResponse.json(
      { error: 'Too many requests. Please try again later.' },
      { headers: corsHeaders, status: 429 }
    );
  }

  try {
    await connectToDatabase();

    // Determine client type
    const clientType = nextReq.headers.get('x-client-type') || 'mobile'; // Default to mobile

    let refreshToken: string | undefined;

    if (clientType === 'web') {
      refreshToken = nextReq.cookies.get('refreshToken')?.value;
    } else {
      const body = await req.json();
      refreshToken = body.refreshToken;
    }

    if (!refreshToken) {
      return NextResponse.json(
        { error: 'No refresh token provided.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Verify refresh token
    let decoded: DecodedToken;
    try {
      decoded = verify(refreshToken, REFRESH_TOKEN_SECRET) as DecodedToken;
    } catch (err) {
      console.error('Refresh token verification failed during logout:', err);
      return NextResponse.json(
        { error: 'Invalid or expired refresh token.' },
        { headers: corsHeaders, status: 403 }
      );
    }

    // Use decoded.id or decoded.role as needed
    // Example: Log user ID who is logging out
    console.log(`User with ID ${decoded.id} is logging out.`);

    // Optionally, implement token revocation by removing the refresh token from the store/database
    // Since you're not using RefreshToken model, this step is skipped

    if (clientType === 'web') {
      // Clear cookies
      const response = NextResponse.json(
        { message: 'Logged out successfully.' },
        { headers: corsHeaders, status: 200 }
      );

      response.cookies.set('accessToken', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        expires: new Date(0),
      });

      response.cookies.set('refreshToken', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        expires: new Date(0),
      });

      return response;
    } else {
      // For mobile clients, confirm logout. Ensure that the client clears tokens from secure storage.
      return NextResponse.json(
        { message: 'Logged out successfully.' },
        { headers: corsHeaders, status: 200 }
      );
    }
  } catch (error) {
    console.error('Logout error:', error);

    const errorMessage =
      process.env.NODE_ENV === 'development' && error instanceof Error
        ? error.message
        : 'An error occurred during logout.';

    return NextResponse.json(
      { error: errorMessage },
      { headers: getCorsHeaders(origin), status: 500 }
    );
  }
}

export async function OPTIONS() {
  // OPTIONS requests are handled by the middleware
  return NextResponse.json({}, { status: 200 });
}
