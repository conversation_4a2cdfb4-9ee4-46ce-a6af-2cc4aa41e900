

// app/api/auth/login/refresh-token.ts

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { verify, sign } from 'jsonwebtoken';
import { NextRequest } from 'next/server';
import { getCorsHeaders } from '@/lib/cors';
import rateLimiter from '@/lib/rateLimit';
import { getClientIp } from '@/lib/getClientIp';
import { User } from '@/models/User'; // Import the User model

// Define the DecodedToken interface
interface DecodedToken {
  id: string; // User ID
  role: string; // User role
  iat?: number; // Issued at
  exp?: number; // Expiration time
}

const ACCESS_TOKEN_SECRET = process.env.ACCESS_TOKEN_SECRET!;
const REFRESH_TOKEN_SECRET = process.env.REFRESH_TOKEN_SECRET!;

export async function POST(req: Request) {
  const nextReq = req as unknown as NextRequest; // Type assertion to NextRequest
  const clientIp = getClientIp(nextReq);
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  // Handle preflight (OPTIONS) requests
  if (nextReq.method === 'OPTIONS') {
    return NextResponse.json({}, { headers: corsHeaders, status: 200 });
  }

  // Apply rate limiting
  if (!rateLimiter.allowRequest(clientIp)) {
    console.warn(`Rate limit exceeded for IP: ${clientIp}`);
    return NextResponse.json(
      { error: 'Too many requests. Please try again later.' },
      { headers: corsHeaders, status: 429 }
    );
  }

  try {
    await connectToDatabase();

    // Determine client type
    const clientType = nextReq.headers.get('x-client-type') || 'mobile'; // Default to mobile

    let refreshToken: string | undefined;

    if (clientType === 'web') {
      refreshToken = nextReq.cookies.get('refreshToken')?.value;
    } else {
      const body = await req.json();
      refreshToken = body.refreshToken;
    }

    if (!refreshToken) {
      return NextResponse.json(
        { error: 'No refresh token provided.' },
        { headers: corsHeaders, status: 401 }
      );
    }

    // Verify refresh token
    let decoded: DecodedToken;
    try {
      decoded = verify(refreshToken, REFRESH_TOKEN_SECRET) as DecodedToken;
    } catch (err) {
      console.error('Refresh token verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid or expired refresh token.' },
        { headers: corsHeaders, status: 403 }
      );
    }

    // Fetch user using Mongoose's User model
    const user = await User.findById(decoded.id).select('-password'); // Exclude password

    if (!user) {
      return NextResponse.json(
        { error: 'User not found.' },
        { headers: corsHeaders, status: 401 }
      );
    }

    // Generate new access token
    const newAccessToken = sign(
      { id: user._id, role: user.role },
      ACCESS_TOKEN_SECRET,
      { expiresIn: '15m' }
    );

    if (clientType === 'web') {
      // Set new access token in HttpOnly cookie
      const response = NextResponse.json(
        { message: 'Access token refreshed successfully.' },
        { headers: corsHeaders, status: 200 }
      );

      response.cookies.set('accessToken', newAccessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        maxAge: 15 * 60, // 15 minutes in seconds
      });

      return response;
    } else {
      // For mobile clients, send the new access token in the response body
      return NextResponse.json(
        { accessToken: newAccessToken },
        { headers: corsHeaders, status: 200 }
      );
    }
  } catch (error) {
    console.error('Refresh Token error:', error);

    const errorMessage =
      process.env.NODE_ENV === 'development' && error instanceof Error
        ? error.message
        : 'An error occurred while refreshing token.';

    return NextResponse.json(
      { error: errorMessage },
      { headers: getCorsHeaders(origin), status: 500 }
    );
  }
}

export async function OPTIONS() {
  // OPTIONS requests are handled by the middleware
  return NextResponse.json({}, { status: 200 });
}

