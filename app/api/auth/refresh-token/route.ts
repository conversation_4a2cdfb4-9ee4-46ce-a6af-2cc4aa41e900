

 // app/api/auth/refresh-token/route.ts
import { NextResponse } from 'next/server';
import { User } from '@/models/User';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

export async function POST(req: Request) {
  try {
    const { refreshToken } = await req.json();

    if (!refreshToken) {
      return NextResponse.json({ error: 'Refresh token is required' }, { status: 400 });
    }

    const user = await User.findOne({ refreshTokenHash: { $exists: true } });

    if (!user || !user.refreshTokenHash || !user.refreshTokenExpiry) {
      return NextResponse.json({ error: 'Invalid refresh token' }, { status: 400 });
    }

    if (new Date() > user.refreshTokenExpiry) {
      return NextResponse.json({ error: 'Refresh token expired' }, { status: 401 });
    }

    const isValidRefreshToken = await bcrypt.compare(refreshToken, user.refreshTokenHash);

    if (!isValidRefreshToken) {
      return NextResponse.json({ error: 'Invalid refresh token' }, { status: 400 });
    }

    // Ensure JWT_SECRET is defined
    if (!process.env.JWT_SECRET) {
      console.error('JWT_SECRET is not defined');
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }

    // Generate a new access token
    const accessToken = jwt.sign(
      { id: user._id.toString(), role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '1h' } // Short-lived access token
    );

    return NextResponse.json({ token: accessToken });
  } catch (error) {
    console.error('Error refreshing token:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
