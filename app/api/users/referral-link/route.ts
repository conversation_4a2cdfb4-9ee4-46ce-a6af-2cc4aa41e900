// app/api/users/referral-link/route.ts
import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import '@/models'; // Import all models

export async function GET(req: NextRequest) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  const authHeader = req.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return NextResponse.json(
      { error: 'Authorization header missing or invalid' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    return NextResponse.json(
      { error: 'Authorization token missing' },
      { status: 401, headers: corsHeaders }
    );
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { id: string };
    const userId = decoded.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid token or user not found' },
        { status: 401, headers: corsHeaders }
      );
    }

    await connectToDatabase();
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404, headers: corsHeaders }
      );
    }

    // Generate referral link
    const baseUrl = process.env.BASE_URL || 'https://www.thegoldenmiller.com';
    const referralLink = `${baseUrl}/register?referralCode=${user.referralCode}`;

    return NextResponse.json(
      { referralLink },
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error generating referral link:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    );
  }
}
