// app/api/users/referrals/route.ts
import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { Subscription } from '@/models/Subscription';
import { Investment } from '@/models/Investment';
import '@/models'; // Import all models

const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Adjust for production
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function GET(req: NextRequest) {
  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  const authHeader = req.headers.get('authorization');
  if (!authHeader) {
    return NextResponse.json(
      { error: 'Authorization header missing' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    return NextResponse.json(
      { error: 'Authorization token missing' },
      { status: 401, headers: corsHeaders }
    );
  }

  try {
    // Verify the JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { id: string };
    const userId = decoded.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid token or user not found' },
        { status: 401, headers: corsHeaders }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Fetch referred users
    const referredUsers = await User.find({ referrerId: userId }).select('name email createdAt');

    // Fetch subscriptions and investments for each referred user
    const referralsData = await Promise.all(
      referredUsers.map(async (user) => {
        // Fetch subscriptions with populated plan fields
        const subscriptions = await Subscription.find({ userId: user._id })
          .populate<{ plan: { title: string; earningRate: number; duration: number } }>('plan', 'title earningRate duration')
          .lean();

        // Fetch investments with populated planId fields
        const investments = await Investment.find({ userId: user._id })
          .populate<{ planId: { title: string; earningRate: number; duration: number } }>('planId', 'title earningRate duration')
          .lean();

        // Transform and combine data
        return {
          user: {
            name: user.name,
            email: user.email,
            createdAt: user.createdAt.toISOString(),
          },
          subscriptions: subscriptions.map((sub) => ({
            ...sub,
            planTitle: sub.plan?.title ?? 'Unknown',
            earningRate: sub.plan?.earningRate ?? 0,
            duration: sub.plan?.duration ?? 0,
          })),
          investments: investments.map((inv) => ({
            ...inv,
            planTitle: inv.planId?.title ?? 'Unknown',
            earningRate: inv.planId?.earningRate ?? 0,
            duration: inv.planId?.duration ?? 0,
          })),
        };
      })
    );

    return NextResponse.json(
      { referrals: referralsData },
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error fetching referrals:', error);

    if (error instanceof jwt.JsonWebTokenError) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401, headers: corsHeaders }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    );
  }
}
