import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { Investment } from '@/models/Investment';
import '@/models'; // Import all models
import mongoose from 'mongoose';

export async function DELETE(request: NextRequest) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  try {
    const { ids } = await request.json();

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'Invalid or empty IDs array' },
        { status: 400, headers: corsHeaders }
      );
    }

    // Validate that all IDs are valid ObjectIds
    const validIds = ids.filter(id => {
      try {
        return mongoose.Types.ObjectId.isValid(id);
      } catch {
        return false;
      }
    });

    if (validIds.length === 0) {
      return NextResponse.json(
        { error: 'No valid IDs provided' },
        { status: 400, headers: corsHeaders }
      );
    }

    await connectToDatabase();

    // Convert string IDs to ObjectIds
    const objectIds = validIds.map(id => new mongoose.Types.ObjectId(id));

    // Start a transaction to ensure data consistency
    const session = await mongoose.startSession();
    
    try {
      await session.withTransaction(async () => {
        // First, delete all investments associated with these users
        await Investment.deleteMany({
          userId: { $in: objectIds }
        }).session(session);

        // Then delete the users
        await User.deleteMany({
          _id: { $in: objectIds }
        }).session(session);
      });

      console.log(`Bulk delete completed for user IDs:`, validIds);

      return NextResponse.json(
        {
          success: true,
          deletedCount: validIds.length,
          message: `Successfully deleted ${validIds.length} user(s) and their associated data`
        },
        { headers: corsHeaders }
      );

    } finally {
      await session.endSession();
    }

  } catch (error) {
    console.error('Error in bulk delete:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    );
  }
}

export async function OPTIONS() {
  return NextResponse.json(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
