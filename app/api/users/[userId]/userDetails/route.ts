// app/api/user/[userId]/userDetails/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function GET(req: NextRequest, { params }: { params: { userId: string } }) {
  try {
    // Establish database connection
    await connectToDatabase();

    // Validate userId
    const { userId } = params;
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400, headers: corsHeaders }
      );
    }

    // Retrieve and verify token from Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is missing' },
        { status: 401, headers: corsHeaders }
      );
    }

    const token = authHeader.split(' ')[1]; // Format: 'Bearer <token>'
    if (!token) {
      return NextResponse.json(
        { error: 'Token is missing' },
        { status: 401, headers: corsHeaders }
      );
    }

    let decodedToken;
    try {
      decodedToken = jwt.verify(token, JWT_SECRET) as { id: string; role: string };
    } catch (error) {
      console.error('Invalid token:', error);
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401, headers: corsHeaders }
      );
    }

    // Ensure the requesting user matches the target user or has admin privileges
    if (decodedToken.id !== userId && decodedToken.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403, headers: corsHeaders }
      );
    }

    // Fetch user details, excluding sensitive fields like password
    const user = await User.findById(userId).select('-password');
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404, headers: corsHeaders }
      );
    }

    return NextResponse.json(
      { user },
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error fetching user details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user details' },
      { status: 500, headers: corsHeaders }
    );
  }
}

export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}
