
// // lib/updateSubscriptionAmount.ts

// import { Subscription } from '@/models/Subscription';

// /**
//  * Updates the amount field in the corresponding Subscription.
//  * 
//  * @param subscriptionId - The ID of the subscription to update.
//  * @param investmentAmount - The amount from the investment.
//  * @returns A promise that resolves to the updated subscription or throws an error.
//  */
// export const updateSubscriptionAmount = async (subscriptionId: string, investmentAmount: number) => {
//   try {
//     const subscription = await Subscription.findById(subscriptionId);
    
//     if (!subscription) {
//       throw new Error(`Subscription with ID ${subscriptionId} not found.`);
//     }

//     // Update the subscription amount
//     subscription.amount = investmentAmount;

//     // Save the updated subscription
//     const updatedSubscription = await subscription.save();
    
//     return updatedSubscription;
//   } catch (error) {
//     // Type guard to check if error is an instance of Error
//     if (error instanceof Error) {
//       console.error(`Error updating subscription: ${error.message}`);
//     } else {
//       console.error(`Unknown error occurred while updating subscription: ${error}`);
//     }
//     throw error;
//   }
// };





// lib/updateSubscriptionAmount.ts

import { Subscription } from '@/models/Subscription';

/**
 * Adds the investment amount to the Subscription's existing amount.
 * 
 * @param subscriptionId - The ID of the subscription to update.
 * @param investmentAmount - The amount from the investment.
 * @returns A promise that resolves to the updated subscription or throws an error.
 */
export const updateSubscriptionAmount = async (subscriptionId: string, investmentAmount: number) => {
  try {
    const subscription = await Subscription.findById(subscriptionId);
    
    if (!subscription) {
      throw new Error(`Subscription with ID ${subscriptionId} not found.`);
    }

    // Update the subscription amount by adding the investment amount
    subscription.amount += investmentAmount;

    // Save the updated subscription
    const updatedSubscription = await subscription.save();
    
    return updatedSubscription;
  } catch (error) {
    // Type guard to check if error is an instance of Error
    if (error instanceof Error) {
      console.error(`Error updating subscription: ${error.message}`);
    } else {
      console.error(`Unknown error occurred while updating subscription: ${error}`);
    }
    throw error;
  }
};
