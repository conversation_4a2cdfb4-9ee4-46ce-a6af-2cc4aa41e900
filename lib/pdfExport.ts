// lib/pdfExport.ts
import jsPDF from 'jspdf';

// Only import autoTable on client side to avoid SSR issues
let autoTable: any = null;
if (typeof window !== 'undefined') {
  import('jspdf-autotable').then(() => {
    console.log('jspdf-autotable loaded successfully');
  }).catch(err => {
    console.error('Failed to load jspdf-autotable:', err);
  });
}

// Extend jsPDF type to include autoTable
interface AutoTableStyles {
  fillColor?: number[];
  textColor?: number[];
  fontStyle?: string;
  fontSize?: number;
  halign?: string;
  cellPadding?: number;
}

interface AutoTableOptions {
  head?: string[][];
  body?: string[][];
  startY?: number;
  theme?: string;
  headStyles?: AutoTableStyles;
  bodyStyles?: AutoTableStyles;
  columnStyles?: Record<number, AutoTableStyles & { cellWidth?: number }>;
  alternateRowStyles?: AutoTableStyles;
  didParseCell?: (data: { column: { index: number }; cell: { text: string[]; styles: AutoTableStyles } }) => void;
  margin?: { left?: number; right?: number };
  tableWidth?: string;
  showHead?: string;
}

declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: AutoTableOptions) => jsPDF;
  }
}

interface UserData {
  name: string;
  email: string;
  phone: string;
  role: string;
  status: string;
  totalInvestments: number;
  totalInvestmentAmount: number;
  referralCode: string;
  bankName: string;
  accountNumber: string;
  accountName: string;
  joinDate: string;
  lastActivity: string;
}

export const generateUsersPDF = (users: UserData[], filters?: {
  search?: string;
  role?: string;
  status?: string;
}) => {
  console.log(`Generating PDF for ${users.length} users`);

  // Create new PDF document in landscape for better table display
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  // Set document properties
  doc.setProperties({
    title: 'Golden Miller - Investors & Members Report',
    subject: 'Complete Member Database Export',
    author: 'Golden Miller System',
    creator: 'Golden Miller Admin Panel'
  });

  // Colors for Golden Miller branding
  const primaryColor = [234, 179, 8]; // Golden Miller yellow
  const secondaryColor = [31, 41, 55]; // Dark gray
  const lightGray = [249, 250, 251];

  // Calculate statistics
  const totalMembers = users.length;
  const activeMembers = users.filter(u => u.status === 'Active').length;
  const inactiveMembers = totalMembers - activeMembers;
  const totalInvestmentValue = users.reduce((sum, u) => sum + u.totalInvestmentAmount, 0);
  const avgInvestment = totalMembers > 0 ? totalInvestmentValue / totalMembers : 0;
  const investors = users.filter(u => u.role.toLowerCase() === 'investor').length;
  const members = users.filter(u => u.role.toLowerCase() === 'member').length;
  const admins = users.filter(u => u.role.toLowerCase() === 'admin').length;

  // Add header to first page
  doc.setFillColor(...primaryColor);
  doc.rect(0, 0, 297, 30, 'F');

  // Logo/Title
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(22);
  doc.setFont('helvetica', 'bold');
  doc.text('🌾 GOLDEN MILLER', 20, 18);

  doc.setFontSize(14);
  doc.setFont('helvetica', 'normal');
  doc.text('Complete Investors & Members Database Report', 20, 25);

  // Date and time
  doc.setFontSize(10);
  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  const currentTime = new Date().toLocaleTimeString('en-US');
  doc.text(`Generated: ${currentDate} at ${currentTime}`, 200, 18);
  doc.text(`Total Records: ${totalMembers}`, 200, 25);

  // Reset text color
  doc.setTextColor(...secondaryColor);

  // Enhanced summary section
  doc.setFillColor(...lightGray);
  doc.rect(20, 40, 257, 35, 'F');
  doc.setDrawColor(200, 200, 200);
  doc.rect(20, 40, 257, 35, 'S');

  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text('📊 EXECUTIVE SUMMARY', 25, 50);

  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');

  // Summary stats in organized layout
  doc.text('MEMBERSHIP OVERVIEW:', 25, 60);
  doc.text(`• Total Members: ${totalMembers}`, 30, 65);
  doc.text(`• Active Members: ${activeMembers} (${Math.round((activeMembers/totalMembers)*100)}%)`, 30, 70);
  doc.text(`• Inactive Members: ${inactiveMembers} (${Math.round((inactiveMembers/totalMembers)*100)}%)`, 30, 75);

  doc.text('ROLE DISTRIBUTION:', 150, 60);
  doc.text(`• Investors: ${investors}`, 155, 65);
  doc.text(`• Members: ${members}`, 155, 70);
  doc.text(`• Administrators: ${admins}`, 155, 75);

  doc.text('FINANCIAL SUMMARY:', 25, 85);
  doc.text(`• Total Investment Value: $${totalInvestmentValue.toLocaleString()}`, 30, 90);
  doc.text(`• Average Investment: $${Math.round(avgInvestment).toLocaleString()}`, 30, 95);
  doc.text(`• Active Investors: ${users.filter(u => u.totalInvestments > 0).length}`, 30, 100);

  // Add filters info if any
  if (filters && (filters.search || filters.role !== 'all' || filters.status !== 'all')) {
    doc.setFontSize(9);
    doc.setTextColor(100, 100, 100);
    let filterText = 'APPLIED FILTERS: ';
    if (filters.search) filterText += `Search: "${filters.search}" `;
    if (filters.role && filters.role !== 'all') filterText += `Role: ${filters.role} `;
    if (filters.status && filters.status !== 'all') filterText += `Status: ${filters.status}`;
    doc.text(filterText, 25, 110);
    doc.setTextColor(...secondaryColor);
  }

  // Comprehensive table headers
  const tableHeaders = [
    '#',
    'Full Name',
    'Email Address',
    'Phone Number',
    'Role',
    'Status',
    'Investments',
    'Investment Amount',
    'Referral Code',
    'Bank Details',
    'Join Date',
    'Last Activity'
  ];

  // Prepare comprehensive table data
  const tableData = users.map((user, index) => [
    (index + 1).toString(), // Row number
    user.name || 'N/A',
    user.email || 'N/A',
    user.phone || 'N/A',
    user.role.toUpperCase(),
    user.status,
    user.totalInvestments.toString(),
    `$${user.totalInvestmentAmount.toLocaleString()}`,
    user.referralCode || 'N/A',
    user.bankName !== 'N/A' ? `${user.bankName}\n${user.accountNumber}` : 'N/A',
    user.joinDate,
    user.lastActivity
  ]);

  console.log(`Prepared table data for ${tableData.length} rows`);

  // Add comprehensive table with proper styling
  doc.autoTable({
    head: [tableHeaders],
    body: tableData,
    startY: 120,
    theme: 'grid',
    headStyles: {
      fillColor: primaryColor,
      textColor: [255, 255, 255],
      fontStyle: 'bold',
      fontSize: 8,
      halign: 'center',
      cellPadding: 3
    },
    bodyStyles: {
      fontSize: 7,
      cellPadding: 2,
      lineColor: [200, 200, 200],
      lineWidth: 0.1
    },
    columnStyles: {
      0: { cellWidth: 8, halign: 'center' },   // #
      1: { cellWidth: 30, halign: 'left' },    // Name
      2: { cellWidth: 35, halign: 'left' },    // Email
      3: { cellWidth: 22, halign: 'center' },  // Phone
      4: { cellWidth: 18, halign: 'center' },  // Role
      5: { cellWidth: 15, halign: 'center' },  // Status
      6: { cellWidth: 12, halign: 'center' },  // Investments
      7: { cellWidth: 20, halign: 'right' },   // Amount
      8: { cellWidth: 18, halign: 'center' },  // Referral
      9: { cellWidth: 25, halign: 'left' },    // Bank Details
      10: { cellWidth: 18, halign: 'center' }, // Join Date
      11: { cellWidth: 18, halign: 'center' }  // Last Activity
    },
    alternateRowStyles: {
      fillColor: [248, 250, 252]
    },
    didParseCell: function(data: { column: { index: number }; cell: { text: string[]; styles: AutoTableStyles } }) {
      // Color code status column
      if (data.column.index === 5) { // Status column
        if (data.cell.text[0] === 'Active') {
          data.cell.styles.textColor = [5, 150, 105]; // Green
          data.cell.styles.fontStyle = 'bold';
        } else if (data.cell.text[0] === 'Inactive') {
          data.cell.styles.textColor = [220, 38, 38]; // Red
          data.cell.styles.fontStyle = 'bold';
        }
      }

      // Color code roles
      if (data.column.index === 4) { // Role column
        const role = data.cell.text[0];
        if (role === 'ADMIN') {
          data.cell.styles.textColor = [220, 38, 38]; // Red
          data.cell.styles.fontStyle = 'bold';
        } else if (role === 'INVESTOR') {
          data.cell.styles.textColor = [37, 99, 235]; // Blue
          data.cell.styles.fontStyle = 'bold';
        } else if (role === 'MEMBER') {
          data.cell.styles.textColor = [107, 114, 128]; // Gray
          data.cell.styles.fontStyle = 'bold';
        }
      }

      // Highlight high investment amounts
      if (data.column.index === 7) { // Investment Amount column
        const amount = parseFloat(data.cell.text[0].replace(/[$,]/g, ''));
        if (amount > 10000) {
          data.cell.styles.textColor = [5, 150, 105]; // Green for high amounts
          data.cell.styles.fontStyle = 'bold';
        }
      }
    },
    margin: { left: 15, right: 15 },
    tableWidth: 'auto',
    showHead: 'everyPage',
    pageBreak: 'auto'
  });

  // Add comprehensive footer to all pages
  const pageCount = doc.getNumberOfPages();
  console.log(`PDF generated with ${pageCount} pages`);

  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);

    // Footer background
    doc.setFillColor(245, 245, 245);
    doc.rect(0, 200, 297, 10, 'F');

    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text('Golden Miller Financial System | Confidential Database Export', 15, 206);
    doc.text(`Page ${i} of ${pageCount} | ${totalMembers} Total Records`, 220, 206);
    doc.text('⚠️ This report contains sensitive financial information - Handle according to data protection policies', 15, 209);
  }

  console.log('PDF generation completed successfully');
  return doc;
};

// Enhanced PDF generation without autoTable - includes all users in table format
export const generateSimplePDF = (users: UserData[], filters?: {
  search?: string;
  role?: string;
  status?: string;
}) => {
  console.log('Generating comprehensive PDF without autoTable');

  const doc = new jsPDF({
    orientation: 'landscape', // Better for table data
    unit: 'mm',
    format: 'a4'
  });

  // Header
  doc.setFillColor(234, 179, 8);
  doc.rect(0, 0, 297, 25, 'F');

  doc.setTextColor(255, 255, 255);
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text('🌾 GOLDEN MILLER', 20, 15);

  doc.setFontSize(12);
  doc.text('Complete Members & Investors Database Report', 20, 20);

  // Reset text color
  doc.setTextColor(0, 0, 0);

  // Enhanced Summary
  doc.setFillColor(249, 250, 251);
  doc.rect(20, 35, 257, 30, 'F');
  doc.setDrawColor(200, 200, 200);
  doc.rect(20, 35, 257, 30, 'S');

  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('📊 EXECUTIVE SUMMARY', 25, 45);

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');

  const totalMembers = users.length;
  const activeMembers = users.filter(u => u.status === 'Active').length;
  const totalInvestment = users.reduce((sum, u) => sum + u.totalInvestmentAmount, 0);
  const investors = users.filter(u => u.role.toLowerCase() === 'investor').length;

  doc.text(`Total Members: ${totalMembers}`, 25, 52);
  doc.text(`Active: ${activeMembers} (${Math.round((activeMembers/totalMembers)*100)}%)`, 25, 57);
  doc.text(`Total Investment: $${totalInvestment.toLocaleString()}`, 150, 52);
  doc.text(`Investors: ${investors} | Members: ${totalMembers - investors}`, 150, 57);
  doc.text(`Generated: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 25, 62);

  // Table Headers
  let yPos = 80;
  doc.setFillColor(234, 179, 8);
  doc.rect(20, yPos - 5, 257, 10, 'F');

  doc.setTextColor(255, 255, 255);
  doc.setFontSize(8);
  doc.setFont('helvetica', 'bold');

  // Column positions
  const cols = {
    num: 25,
    name: 40,
    email: 85,
    phone: 125,
    role: 150,
    status: 170,
    investments: 190,
    amount: 215,
    referral: 245,
    joined: 270
  };

  doc.text('#', cols.num, yPos);
  doc.text('Name', cols.name, yPos);
  doc.text('Email', cols.email, yPos);
  doc.text('Phone', cols.phone, yPos);
  doc.text('Role', cols.role, yPos);
  doc.text('Status', cols.status, yPos);
  doc.text('Inv.', cols.investments, yPos);
  doc.text('Amount', cols.amount, yPos);
  doc.text('Referral', cols.referral, yPos);
  doc.text('Joined', cols.joined, yPos);

  yPos += 10;
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(7);

  // Table Data - All users
  users.forEach((user, index) => {
    if (yPos > 190) { // Add new page if needed
      doc.addPage();
      yPos = 20;

      // Repeat headers on new page
      doc.setFillColor(234, 179, 8);
      doc.rect(20, yPos - 5, 257, 10, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(8);
      doc.setFont('helvetica', 'bold');

      doc.text('#', cols.num, yPos);
      doc.text('Name', cols.name, yPos);
      doc.text('Email', cols.email, yPos);
      doc.text('Phone', cols.phone, yPos);
      doc.text('Role', cols.role, yPos);
      doc.text('Status', cols.status, yPos);
      doc.text('Inv.', cols.investments, yPos);
      doc.text('Amount', cols.amount, yPos);
      doc.text('Referral', cols.referral, yPos);
      doc.text('Joined', cols.joined, yPos);

      yPos += 10;
      doc.setTextColor(0, 0, 0);
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(7);
    }

    // Alternating row background
    if (index % 2 === 0) {
      doc.setFillColor(248, 250, 252);
      doc.rect(20, yPos - 3, 257, 8, 'F');
    }

    // Row data
    doc.text((index + 1).toString(), cols.num, yPos);
    doc.text(user.name.substring(0, 18), cols.name, yPos);
    doc.text(user.email.substring(0, 20), cols.email, yPos);
    doc.text((user.phone || 'N/A').substring(0, 12), cols.phone, yPos);

    // Color-coded role
    if (user.role.toLowerCase() === 'admin') {
      doc.setTextColor(220, 38, 38);
    } else if (user.role.toLowerCase() === 'investor') {
      doc.setTextColor(37, 99, 235);
    } else {
      doc.setTextColor(107, 114, 128);
    }
    doc.text(user.role.toUpperCase(), cols.role, yPos);

    // Color-coded status
    if (user.status === 'Active') {
      doc.setTextColor(5, 150, 105);
    } else {
      doc.setTextColor(220, 38, 38);
    }
    doc.text(user.status, cols.status, yPos);

    // Reset color for remaining fields
    doc.setTextColor(0, 0, 0);
    doc.text(user.totalInvestments.toString(), cols.investments, yPos);
    doc.text(`$${user.totalInvestmentAmount.toLocaleString()}`, cols.amount, yPos);
    doc.text((user.referralCode || 'N/A').substring(0, 8), cols.referral, yPos);
    doc.text(user.joinDate.substring(0, 10), cols.joined, yPos);

    yPos += 8;
  });

  // Footer on all pages
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);

    doc.setFillColor(245, 245, 245);
    doc.rect(0, 200, 297, 10, 'F');

    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text('Golden Miller Financial System | Complete Database Export', 20, 206);
    doc.text(`Page ${i} of ${pageCount} | ${totalMembers} Total Records`, 220, 206);
  }

  console.log(`Simple PDF generated with ${pageCount} pages for ${users.length} users`);
  return doc;
};

export const downloadUsersPDF = (users: UserData[], filters?: {
  search?: string;
  role?: string;
  status?: string;
}) => {
  try {
    console.log('Starting PDF generation with users:', users.length);

    if (!users || users.length === 0) {
      throw new Error('No user data provided for PDF generation');
    }

    let doc;

    // Check if we're on client side and autoTable is available
    const hasAutoTable = typeof window !== 'undefined' && (window as any).jsPDF?.API?.autoTable;

    if (hasAutoTable) {
      try {
        console.log('AutoTable available, attempting comprehensive PDF...');
        doc = generateUsersPDF(users, filters);
        console.log('Comprehensive PDF generated successfully');
      } catch (autoTableError) {
        console.warn('AutoTable PDF failed, falling back to simple PDF:', autoTableError);
        doc = generateSimplePDF(users, filters);
        console.log('Simple PDF generated successfully');
      }
    } else {
      console.log('AutoTable not available, using simple PDF...');
      doc = generateSimplePDF(users, filters);
      console.log('Simple PDF generated successfully');
    }

    if (!doc) {
      throw new Error('PDF document generation failed');
    }

    const fileName = `golden-miller-members-report-${new Date().toISOString().split('T')[0]}.pdf`;
    console.log('PDF generated successfully, attempting to save:', fileName);

    // Try to save the PDF
    doc.save(fileName);
    console.log('PDF save completed successfully');

  } catch (error) {
    console.error('Error in downloadUsersPDF:', error);
    console.error('Error stack:', (error as Error).stack);
    throw new Error(`PDF export failed: ${(error as Error).message}`);
  }
};
