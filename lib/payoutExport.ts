import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { formatCurrencyDisplay, formatCurrencyExport } from './currency';

interface PaymentExportData {
  _id: string;
  userId: { name: string; email: string };
  planId: { title: string };
  investmentAmount: number;
  paymentAmount: number;
  investmentDate: string;
  dueDate: string;
  paymentStatus: 'ready' | 'pending' | 'completed';
  investmentId: {
    waitPeriod: number;
  };
}

interface PaymentStats {
  totalPayments: number;
  totalAmount: number;
  pendingCount: number;
  readyCount: number;
  completedCount: number;
  averageAmount: number;
}

// PDF Export Function
export const exportPaymentsToPDF = (payments: PaymentExportData[], stats: PaymentStats) => {
  const doc = new jsPDF();
  
  // Header
  doc.setFontSize(20);
  doc.setTextColor(40, 40, 40);
  doc.text('TGM Finance - Payout Report', 20, 20);
  
  // Date
  doc.setFontSize(10);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 30);
  
  // Summary Statistics
  doc.setFontSize(14);
  doc.setTextColor(40, 40, 40);
  doc.text('Summary Statistics', 20, 45);
  
  const summaryData = [
    ['Total Payments', stats.totalPayments.toString()],
    ['Total Amount', formatCurrencyExport(stats.totalAmount)],
    ['Pending Payments', stats.pendingCount.toString()],
    ['Ready Payments', stats.readyCount.toString()],
    ['Completed Payments', stats.completedCount.toString()],
    ['Average Payment', formatCurrencyExport(stats.averageAmount)],
  ];
  
  (doc as any).autoTable({
    startY: 50,
    head: [['Metric', 'Value']],
    body: summaryData,
    theme: 'grid',
    headStyles: { fillColor: [251, 191, 36] },
    margin: { left: 20, right: 20 },
    tableWidth: 'auto',
    columnStyles: {
      0: { cellWidth: 80 },
      1: { cellWidth: 80 }
    }
  });
  
  // Payment Details Table
  const finalY = (doc as any).lastAutoTable.finalY + 20;
  doc.setFontSize(14);
  doc.text('Payment Details', 20, finalY);
  
  const tableData = payments.map(payment => [
    payment.userId?.name || 'N/A',
    payment.userId?.email || 'N/A',
    payment.planId?.title || 'N/A',
    formatCurrencyExport(payment.investmentAmount),
    formatCurrencyExport(payment.paymentAmount),
    payment.paymentStatus.charAt(0).toUpperCase() + payment.paymentStatus.slice(1),
    new Date(payment.dueDate).toLocaleDateString(),
  ]);
  
  (doc as any).autoTable({
    startY: finalY + 5,
    head: [['User Name', 'Email', 'Plan', 'Investment', 'Payout', 'Status', 'Due Date']],
    body: tableData,
    theme: 'striped',
    headStyles: { fillColor: [251, 191, 36] },
    margin: { left: 20, right: 20 },
    styles: { fontSize: 8 },
    columnStyles: {
      0: { cellWidth: 25 },
      1: { cellWidth: 35 },
      2: { cellWidth: 25 },
      3: { cellWidth: 20 },
      4: { cellWidth: 20 },
      5: { cellWidth: 18 },
      6: { cellWidth: 22 }
    }
  });
  
  // Footer
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text(
      `TGM Finance Payout Report - Page ${i} of ${pageCount}`,
      doc.internal.pageSize.width / 2,
      doc.internal.pageSize.height - 10,
      { align: 'center' }
    );
  }
  
  // Save the PDF
  doc.save(`TGM-Finance-Payouts-${new Date().toISOString().split('T')[0]}.pdf`);
};

// Excel Export Function
export const exportPaymentsToExcel = (payments: PaymentExportData[], stats: PaymentStats) => {
  // Create workbook
  const wb = XLSX.utils.book_new();
  
  // Summary Sheet
  const summaryData = [
    ['TGM Finance - Payout Report'],
    [`Generated on: ${new Date().toLocaleDateString()}`],
    [''],
    ['Summary Statistics'],
    ['Metric', 'Value'],
    ['Total Payments', stats.totalPayments],
    ['Total Amount', formatCurrencyExport(stats.totalAmount)],
    ['Pending Payments', stats.pendingCount],
    ['Ready Payments', stats.readyCount],
    ['Completed Payments', stats.completedCount],
    ['Average Payment', formatCurrencyExport(stats.averageAmount)],
  ];
  
  const summaryWS = XLSX.utils.aoa_to_sheet(summaryData);
  
  // Style the summary sheet
  summaryWS['!cols'] = [{ width: 20 }, { width: 25 }];
  
  // Payment Details Sheet
  const paymentData = [
    ['User Name', 'Email', 'Plan Title', 'Investment Amount', 'Payout Amount', 'Status', 'Due Date', 'Wait Period (Days)'],
    ...payments.map(payment => [
      payment.userId?.name || 'N/A',
      payment.userId?.email || 'N/A',
      payment.planId?.title || 'N/A',
      formatCurrencyExport(payment.investmentAmount),
      formatCurrencyExport(payment.paymentAmount),
      payment.paymentStatus.charAt(0).toUpperCase() + payment.paymentStatus.slice(1),
      new Date(payment.dueDate).toLocaleDateString(),
      payment.investmentId?.waitPeriod || 0,
    ])
  ];
  
  const paymentWS = XLSX.utils.aoa_to_sheet(paymentData);
  
  // Style the payment details sheet
  paymentWS['!cols'] = [
    { width: 20 }, // User Name
    { width: 30 }, // Email
    { width: 25 }, // Plan Title
    { width: 18 }, // Investment Amount
    { width: 18 }, // Payout Amount
    { width: 12 }, // Status
    { width: 15 }, // Due Date
    { width: 18 }, // Wait Period
  ];
  
  // Add sheets to workbook
  XLSX.utils.book_append_sheet(wb, summaryWS, 'Summary');
  XLSX.utils.book_append_sheet(wb, paymentWS, 'Payment Details');
  
  // Save the Excel file
  XLSX.writeFile(wb, `TGM-Finance-Payouts-${new Date().toISOString().split('T')[0]}.xlsx`);
};

// Export filtered payments
export const exportFilteredPayments = (
  payments: PaymentExportData[], 
  stats: PaymentStats, 
  format: 'pdf' | 'excel'
) => {
  if (format === 'pdf') {
    exportPaymentsToPDF(payments, stats);
  } else {
    exportPaymentsToExcel(payments, stats);
  }
};

// Utility function to prepare payment data for export
export const preparePaymentExportData = (payments: any[]): PaymentExportData[] => {
  return payments.map(payment => ({
    _id: payment._id,
    userId: {
      name: payment.userId?.name || 'N/A',
      email: payment.userId?.email || 'N/A'
    },
    planId: {
      title: payment.planId?.title || 'N/A'
    },
    investmentAmount: payment.investmentAmount || 0,
    paymentAmount: payment.paymentAmount || 0,
    investmentDate: payment.investmentDate || new Date().toISOString(),
    dueDate: payment.dueDate || new Date().toISOString(),
    paymentStatus: payment.paymentStatus || 'pending',
    investmentId: {
      waitPeriod: payment.investmentId?.waitPeriod || 0
    }
  }));
};

// Generate export summary for user feedback
export const generateExportSummary = (payments: PaymentExportData[], format: 'pdf' | 'excel') => {
  const totalAmount = payments.reduce((sum, p) => sum + p.paymentAmount, 0);
  const statusCounts = payments.reduce((acc, p) => {
    acc[p.paymentStatus] = (acc[p.paymentStatus] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  return {
    format: format.toUpperCase(),
    totalRecords: payments.length,
    totalAmount: formatCurrencyDisplay(totalAmount),
    statusBreakdown: statusCounts,
    fileName: `TGM-Finance-Payouts-${new Date().toISOString().split('T')[0]}.${format === 'pdf' ? 'pdf' : 'xlsx'}`,
    generatedAt: new Date().toLocaleString()
  };
};
