// // utils/changeStream.ts
// import mongoose from 'mongoose';
// import { Payment } from '@/models/Payment';

// export const startChangeStream = async () => {
//   const db = mongoose.connection;

//   // Monitor changes to the Investment collection
//   const investmentCollection = db.collection('investments');

//   const changeStream = investmentCollection.watch();

//   changeStream.on('change', async (change) => {
//     if (change.operationType === 'update') {
//       const updatedFields = change.updateDescription.updatedFields;

//       // Check if the waitPeriod is updated to 0
//       if (updatedFields.waitPeriod === 0) {
//         const investmentId = change.documentKey._id;

//         // Update corresponding Payment record
//         await Payment.updateOne(
//           { investmentId },
//           { $set: { paymentStatus: 'ready' } }
//         );

//         console.log(`Payment status updated to "ready" for investment ${investmentId}`);
//       }
//     }
//   });

//   console.log('MongoDB Change Stream started...');
// };


// utils/changeStream.ts
import mongoose from 'mongoose';
import { Payment } from '@/models/Payment';
import { IInvestment } from '@/models/Investment';
import { ChangeStreamDocument, ChangeStreamUpdateDocument } from 'mongodb';

export const startChangeStream = async () => {
  const db = mongoose.connection;

  // Monitor changes to the Investment collection
  const investmentCollection = db.collection('investments');

  const changeStream = investmentCollection.watch();

  changeStream.on('change', async (change: ChangeStreamDocument<IInvestment>) => {
    if (change.operationType === 'update') {
      const updatedFields = (change as ChangeStreamUpdateDocument<IInvestment>).updateDescription?.updatedFields;

      // Check if the waitPeriod is updated to 0
      if (updatedFields?.waitPeriod === 0) {
        const investmentId = change.documentKey._id;

        // Update corresponding Payment record
        await Payment.updateOne(
          { investmentId },
          { $set: { paymentStatus: 'ready' } }
        );

        console.log(`Payment status updated to "ready" for investment ${investmentId}`);
      }
    }
  });

  console.log('MongoDB Change Stream started...');
};


