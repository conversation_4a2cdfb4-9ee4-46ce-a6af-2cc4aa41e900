// lib/investmentCalculations.ts
import { Subscription } from '@/types/subscription';
/**
 * Calculate the dynamic membership fee as a percentage of the investment amount.
 * @param investmentAmount - The total amount invested by the user.
 * @returns The membership fee based on investment amount.
 */
export const calculateMembershipFee = (investmentAmount: number): number => {
    const membershipFeeRate = 0.3; // 30% based on our findings
    return investmentAmount * membershipFeeRate;
  };
  
  /**
   * Calculate the initial profit allocation as a percentage of the investment amount.
   * @param investmentAmount - The total amount invested by the user.
   * @returns The initial profit.
   */
  export const calculateInitialProfit = (investmentAmount: number): number => {
    const initialProfitRate = 0.13; // 13% based on our findings
    return investmentAmount * initialProfitRate;
  };
  
  /**
   * Calculate the reinvestable amount after deducting the membership fee and initial profit.
   * @param investmentAmount - The total amount invested by the user.
   * @returns The reinvestable amount for purchasing bags of maize flour.
   */
  export const calculateReinvestableAmount = (investmentAmount: number): number => {
    const membershipFee = calculateMembershipFee(investmentAmount);
    const initialProfit = calculateInitialProfit(investmentAmount);
    return investmentAmount - membershipFee - initialProfit;
  };
  
  /**
   * Calculate the number of maize flour bags that can be bought with the reinvestable amount.
   * @param reinvestableAmount - The amount available for reinvestment.
   * @param costPerBag - The cost per bag of maize flour.
   * @returns The number of bags purchased.
   */
  export const calculateBagsPurchased = (reinvestableAmount: number, costPerBag: number = 95): number => {
    return Math.floor(reinvestableAmount / costPerBag);
  };
  
  /**
   * Calculate the monthly profit earned from reselling maize flour bags.
   * @param bagsPurchased - The number of bags purchased.
   * @param profitPerBag - The profit earned per bag after resale.
   * @returns The total monthly profit.
   */
  export const calculateMonthlyProfit = (bagsPurchased: number, profitPerBag: number = 20): number => {
    return bagsPurchased * profitPerBag;
  };
  
  /**
   * Calculate the total profit accumulated over a specified period.
   * @param initialProfit - The initial profit allocation.
   * @param monthlyProfit - The profit earned monthly.
   * @param months - The investment duration in months.
   * @returns The total accumulated profit.
   */
  export const calculateTotalProfit = (initialProfit: number, monthlyProfit: number, months: number = 4): number => {
    return initialProfit + monthlyProfit * months;
  };
  

  /**
 * Calculate the total investment amount from all subscriptions without any subtractions.
 * @param subscriptions - Array of Subscription objects
 * @returns Total investment amount
 */
export const calculateTotalInvestment = (subscriptions: Subscription[]): number => {
  return subscriptions.reduce((total, sub) => total + (sub.amount || 0), 0);
};


  /**
   * Comprehensive calculation that provides all investment details.
   * @param investmentAmount - The total investment amount.
   * @param costPerBag - The cost per bag of maize flour.
   * @param profitPerBag - The profit per bag from resale.
   * @param months - Investment duration in months.
   * @returns An object with all calculated values.
   */
  export const calculateInvestmentSummary = (
    investmentAmount: number,
    costPerBag: number = 95,
    profitPerBag: number = 17,
    months: number = 4
  ) => {
    const membershipFee = calculateMembershipFee(investmentAmount);
    const initialProfit = calculateInitialProfit(investmentAmount);
    const reinvestableAmount = calculateReinvestableAmount(investmentAmount);
    const bagsPurchased = calculateBagsPurchased(reinvestableAmount, costPerBag);
    const monthlyProfit = calculateMonthlyProfit(bagsPurchased, profitPerBag);
    const totalProfit = calculateTotalProfit(initialProfit, monthlyProfit, months);
  
    return {
      membershipFee,
      initialProfit,
      reinvestableAmount,
      bagsPurchased,
      monthlyProfit,
      totalProfit,
    };
  };
  

