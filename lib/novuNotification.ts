// // lib/novuNotification.ts
// import axios from 'axios';

// const NOVU_API_KEY = process.env.NOVU_API_KEY; // Store in your environment variables

// /**
//  * Sends a notification via the Novu API.
//  * 
//  * @param recipient - The phone number of the recipient (admin or user).
//  * @param message - The message to send.
//  * @returns A promise resolving to the result of the SMS API request.
//  */
// export const sendSmsNotification = async (recipient: string, message: string) => {
//   try {
//     const response = await axios.post(
//       'https://api.novu.co/v1/sms/send',
//       {
//         to: recipient,
//         message,
//       },
//       {
//         headers: {
//           Authorization: `ApiKey ${NOVU_API_KEY}`,
//         },
//       }
//     );
//     return response.data;
//   } catch (error) {
//     console.error('Failed to send SMS notification:', error.message);
//     throw new Error('SMS notification failed.');
//   }
// };

// /**
//  * Sends notifications to both the user and admin when a payment is approved.
//  * 
//  * @param userPhone - The phone number of the user.
//  * @param adminPhone - The phone number of the admin.
//  * @param userName - The name of the user making the payment.
//  * @param planName - The name of the investment plan.
//  * @param amount - The investment amount.
//  * @param earningsStartDate - The date when earnings will start.
//  */
// export const sendPaymentApprovalNotifications = async (
//   userPhone: string,
//   adminPhone: string,
//   userName: string,
//   planName: string,
//   amount: number,
//   earningsStartDate: Date
// ) => {
//   // Message to the user
//   const userMessage = `Your payment on Goldenmiller Investment has been approved.`;

//   // Message to the admin
//   const adminMessage = `The user "${userName}" has made a payment of $${amount} for the "${planName}" investment plan. Their payment schedule starts from ${earningsStartDate.toDateString()}.`;

//   // Send notifications
//   await sendSmsNotification(userPhone, userMessage);
//   await sendSmsNotification(adminPhone, adminMessage);
// };








import axios from 'axios';

const NOVU_API_KEY = process.env.NOVU_API_KEY; // Store in your environment variables

/**
 * Sends a notification via the Novu API.
 * 
 * @param recipient - The phone number of the recipient (admin or user).
 * @param message - The message to send.
 * @returns A promise resolving to the result of the SMS API request.
 */
export const sendSmsNotification = async (recipient: string, message: string) => {
  try {
    const response = await axios.post(
      'https://api.novu.co/v1/sms/send',
      {
        to: recipient,
        message,
      },
      {
        headers: {
          Authorization: `ApiKey ${NOVU_API_KEY}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    // Type guard to check if error is an instance of Error
    if (error instanceof Error) {
      console.error('Failed to send SMS notification:', error.message);
    } else {
      console.error('Unknown error occurred while sending SMS notification:', error);
    }
    throw new Error('SMS notification failed.');
  }
};

/**
 * Sends notifications to both the user and admin when a payment is approved.
 * 
 * @param userPhone - The phone number of the user.
 * @param adminPhone - The phone number of the admin.
 * @param userName - The name of the user making the payment.
 * @param planName - The name of the investment plan.
 * @param amount - The investment amount.
 * @param earningsStartDate - The date when earnings will start.
 */
export const sendPaymentApprovalNotifications = async (
  userPhone: string,
  adminPhone: string,
  userName: string,
  planName: string,
  amount: number,
  earningsStartDate: Date
) => {
  // Message to the user
  const userMessage = `Your payment on Goldenmiller Investment has been approved.`;

  // Message to the admin
  const adminMessage = `The user "${userName}" has made a payment of $${amount} for the "${planName}" investment plan. Their payment schedule starts from ${earningsStartDate.toDateString()}.`;

  // Send notifications
  await sendSmsNotification(userPhone, userMessage);
  await sendSmsNotification(adminPhone, adminMessage);
};



// import axios from 'axios';

// const NOVU_API_KEY = process.env.NOVU_API_KEY; // Store in your environment variables

// /**
//  * Sends a notification via the Novu API.
//  * 
//  * @param recipient - The phone number of the recipient (admin or user).
//  * @param message - The message to send.
//  * @returns A promise resolving to the result of the SMS API request.
//  */
// export const sendSmsNotification = async (recipient: string, message: string) => {
//   try {
//     const response = await axios.post(
//       'https://api.novu.co/v1/sms/send',
//       {
//         to: recipient,
//         message,
//       },
//       {
//         headers: {
//           Authorization: `ApiKey ${NOVU_API_KEY}`,
//         },
//       }
//     );
//     return response.data;
//   } catch (error) {
//     // Use type guard to check if the error is an instance of Error
//     if (error instanceof Error) {
//       console.error('Failed to send SMS notification:', error.message);
//     } else {
//       console.error('Failed to send SMS notification:', error);
//     }
//     throw new Error('SMS notification failed.');
//   }
// };

// /**
//  * Sends notifications to both the user and admin when a payment is approved.
//  * 
//  * @param userPhone - The phone number of the user.
//  * @param adminPhone - The phone number of the admin.
//  * @param userName - The name of the user making the payment.
//  * @param planName - The name of the investment plan.
//  * @param amount - The investment amount.
//  * @param earningsStartDate - The date when earnings will start.
//  */
// export const sendPaymentApprovalNotifications = async (
//   userPhone: string,
//   adminPhone: string,
//   userName: string,
//   planName: string,
//   amount: number,
//   earningsStartDate: Date
// ) => {
//   // Message to the user
//   const userMessage = `Your payment on Goldenmiller Investment has been approved.`;

//   // Message to the admin
//   const adminMessage = `The user "${userName}" has made a payment of $${amount} for the "${planName}" investment plan. Their payment schedule starts from ${earningsStartDate.toDateString()}.`;

//   // Send notifications
//   await sendSmsNotification(userPhone, userMessage);
//   await sendSmsNotification(adminPhone, adminMessage);
// };
