// // // lib/cors.ts



// import Cors from 'cors';
// import { NextApiRequest, NextApiResponse } from 'next';

// // Initialize the cors middleware
// const cors = Cors({
//     // origin: ['https://goldenmiller.vercel.app', 'your-mobile-app-url'],
//     origin: '*',
//     methods: ['GET', 'POST', 'PUT', 'DELETE'],
// });

// // Helper method to wait for a middleware to execute before continuing
// export function runMiddleware(
//     req: NextApiRequest,
//     res: NextApiResponse,
//     fn: (req: NextApiRequest, res: NextApiResponse, callback: (result: unknown) => void) => void
// ) {
//     return new Promise<void>((resolve, reject) => {
//         fn(req, res, (result) => {
//             if (result instanceof Error) {
//                 return reject(result);
//             }
//             return resolve();
//         });
//     });
// }

// export default cors;



// // lib/cors.ts
// export const getCorsHeaders = (origin: string | null): Record<string, string> => {
//     const allowedOrigins = ['https://www.thegoldenmiller.com'];
//     const isAllowed = allowedOrigins.includes(origin || '');
//     return {
//       'Access-Control-Allow-Origin': isAllowed ? origin : allowedOrigins[0],
//       'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
//       'Access-Control-Allow-Headers': 'Content-Type, Authorization',
//       'Access-Control-Allow-Credentials': 'true',
//     };
//   };
  


// // lib/cors.ts

// // Define Allowed Origins (Replace with your actual frontend domain)
// const allowedOrigins = ['https://www.thegoldenmiller.com'];

// // Utility function to get CORS headers based on the request origin
// export const getCorsHeaders = (origin: string | null): Record<string, string> => {
//   if (origin && allowedOrigins.includes(origin)) {
//     return {
//       'Access-Control-Allow-Origin': origin,
//       'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
//       'Access-Control-Allow-Headers': 'Content-Type, Authorization',
//       'Access-Control-Allow-Credentials': 'true', // Enable credentials
//     };
//   }
//   // Default to the first allowed origin if the request origin is not allowed
//   return {
//     'Access-Control-Allow-Origin': allowedOrigins[0],
//     'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
//     'Access-Control-Allow-Headers': 'Content-Type, Authorization',
//     'Access-Control-Allow-Credentials': 'true', // Enable credentials
//   };
// };



// lib/cors.ts

// Define Allowed Origins (Replace with your actual frontend domain)
const allowedOrigins = ['https://www.thegoldenmiller.com'];

// Utility function to get CORS headers based on the request origin
export const getCorsHeaders = (origin: string | null): Record<string, string> => {
  if (origin && allowedOrigins.includes(origin)) {
    return {
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Client-Type',
      'Access-Control-Allow-Credentials': 'true', // Enable credentials
    };
  }
  // Default CORS headers if origin is not allowed
  return {
    'Access-Control-Allow-Origin': allowedOrigins[0],
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Client-Type',
    'Access-Control-Allow-Credentials': 'true', // Enable credentials
  };
};
