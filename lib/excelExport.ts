// lib/excelExport.ts
import * as XLSX from 'xlsx';
import { formatCurrencyDisplay } from './currency';

interface UserData {
  name: string;
  email: string;
  phone: string;
  role: string;
  status: string;
  totalInvestments: number;
  totalInvestmentAmount: number;
  referralCode: string;
  bankName: string;
  accountNumber: string;
  accountName: string;
  joinDate: string;
  lastActivity: string;
}

export const generateUsersExcel = (users: UserData[], filters?: {
  search?: string;
  role?: string;
  status?: string;
}) => {
  // Create a new workbook
  const workbook = XLSX.utils.book_new();

  // Summary statistics
  const totalMembers = users.length;
  const activeMembers = users.filter(u => u.status === 'Active').length;
  const inactiveMembers = totalMembers - activeMembers;
  const totalInvestmentValue = users.reduce((sum, u) => sum + u.totalInvestmentAmount, 0);
  const avgInvestment = totalMembers > 0 ? totalInvestmentValue / totalMembers : 0;

  // Create summary data
  const summaryData = [
    ['GOLDEN MILLER - MEMBERS EXPORT REPORT'],
    [`Generated on: ${new Date().toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })}`],
    [''],
    ['SUMMARY STATISTICS'],
    [`Total Members: ${totalMembers}`],
    [`Active Members: ${activeMembers}`],
    [`Inactive Members: ${inactiveMembers}`],
    [`Total Investment Value: ${formatCurrencyDisplay(totalInvestmentValue)}`],
    [`Average Investment per Member: ${formatCurrencyDisplay(Math.round(avgInvestment))}`],
    ['']
  ];

  // Add filter information if any
  if (filters && (filters.search || filters.role !== 'all' || filters.status !== 'all')) {
    summaryData.push(['APPLIED FILTERS']);
    if (filters.search) summaryData.push([`Search Term: ${filters.search}`]);
    if (filters.role && filters.role !== 'all') summaryData.push([`Role Filter: ${filters.role}`]);
    if (filters.status && filters.status !== 'all') summaryData.push([`Status Filter: ${filters.status}`]);
    summaryData.push(['']);
  }

  // Headers for detailed data
  const headers = [
    'Member ID',
    'Full Name',
    'Email Address',
    'Phone Number',
    'Role',
    'Account Status',
    'Total Investments',
    'Investment Amount (USD)',
    'Referral Code',
    'Bank Name',
    'Account Number',
    'Account Holder Name',
    'Registration Date',
    'Last Activity Date',
    'Activity Status',
    'Investment Participation'
  ];

  summaryData.push(['DETAILED MEMBER DATA']);
  summaryData.push(headers);

  // Convert user data to rows
  const dataRows = users.map((user, index) => [
    `GM${String(index + 1).padStart(4, '0')}`, // Member ID
    user.name,
    user.email,
    user.phone || 'N/A',
    user.role.toUpperCase(),
    user.status,
    user.totalInvestments,
    formatCurrencyDisplay(user.totalInvestmentAmount),
    user.referralCode || 'N/A',
    user.bankName || 'N/A',
    user.accountNumber || 'N/A',
    user.accountName || 'N/A',
    user.joinDate,
    user.lastActivity,
    user.status === 'Active' ? 'ACTIVE' : 'INACTIVE',
    user.totalInvestments > 0 ? 'PARTICIPATING' : 'NON-PARTICIPATING'
  ]);

  // Combine all data
  const allData = [...summaryData, ...dataRows];

  // Create worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(allData);

  // Set column widths
  const columnWidths = [
    { wch: 12 }, // Member ID
    { wch: 25 }, // Full Name
    { wch: 30 }, // Email
    { wch: 15 }, // Phone
    { wch: 12 }, // Role
    { wch: 15 }, // Status
    { wch: 12 }, // Total Investments
    { wch: 18 }, // Investment Amount
    { wch: 15 }, // Referral Code
    { wch: 20 }, // Bank Name
    { wch: 18 }, // Account Number
    { wch: 25 }, // Account Holder
    { wch: 15 }, // Registration Date
    { wch: 15 }, // Last Activity
    { wch: 15 }, // Activity Status
    { wch: 20 }  // Investment Participation
  ];

  worksheet['!cols'] = columnWidths;

  // Style the header row (find where headers start)
  const headerRowIndex = summaryData.length - 1; // Headers are the last row in summaryData
  const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

  // Apply styles to header row
  for (let col = range.s.c; col <= range.e.c; col++) {
    const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: col });
    if (!worksheet[cellAddress]) continue;
    
    worksheet[cellAddress].s = {
      font: { bold: true, color: { rgb: "FFFFFF" } },
      fill: { fgColor: { rgb: "EAB308" } }, // Golden Miller yellow
      alignment: { horizontal: "center", vertical: "center" },
      border: {
        top: { style: "thin", color: { rgb: "000000" } },
        bottom: { style: "thin", color: { rgb: "000000" } },
        left: { style: "thin", color: { rgb: "000000" } },
        right: { style: "thin", color: { rgb: "000000" } }
      }
    };
  }

  // Style summary section
  for (let row = 0; row < 4; row++) {
    const cellAddress = XLSX.utils.encode_cell({ r: row, c: 0 });
    if (worksheet[cellAddress]) {
      worksheet[cellAddress].s = {
        font: { bold: true, size: row === 0 ? 16 : 12 },
        alignment: { horizontal: row === 0 ? "center" : "left" }
      };
    }
  }

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Members Report');

  // Create a separate sheet for statistics
  const statsData = [
    ['GOLDEN MILLER - STATISTICS SUMMARY'],
    [''],
    ['Metric', 'Value', 'Percentage'],
    ['Total Members', totalMembers, '100%'],
    ['Active Members', activeMembers, `${Math.round((activeMembers / totalMembers) * 100)}%`],
    ['Inactive Members', inactiveMembers, `${Math.round((inactiveMembers / totalMembers) * 100)}%`],
    [''],
    ['Financial Summary'],
    ['Total Investment Value', formatCurrencyDisplay(totalInvestmentValue), ''],
    ['Average Investment', formatCurrencyDisplay(Math.round(avgInvestment)), ''],
    ['Members with Investments', users.filter(u => u.totalInvestments > 0).length, `${Math.round((users.filter(u => u.totalInvestments > 0).length / totalMembers) * 100)}%`],
    [''],
    ['Role Distribution'],
    ['Admins', users.filter(u => u.role === 'admin').length, `${Math.round((users.filter(u => u.role === 'admin').length / totalMembers) * 100)}%`],
    ['Investors', users.filter(u => u.role === 'investor').length, `${Math.round((users.filter(u => u.role === 'investor').length / totalMembers) * 100)}%`],
    ['Members', users.filter(u => u.role === 'member').length, `${Math.round((users.filter(u => u.role === 'member').length / totalMembers) * 100)}%`]
  ];

  const statsWorksheet = XLSX.utils.aoa_to_sheet(statsData);
  
  // Set column widths for stats sheet
  statsWorksheet['!cols'] = [
    { wch: 25 }, // Metric
    { wch: 20 }, // Value
    { wch: 15 }  // Percentage
  ];

  // Style the stats header
  const statsHeaderCell = XLSX.utils.encode_cell({ r: 0, c: 0 });
  if (statsWorksheet[statsHeaderCell]) {
    statsWorksheet[statsHeaderCell].s = {
      font: { bold: true, size: 16 },
      alignment: { horizontal: "center" }
    };
  }

  // Style the table headers
  const tableHeaderRow = 2;
  for (let col = 0; col < 3; col++) {
    const cellAddress = XLSX.utils.encode_cell({ r: tableHeaderRow, c: col });
    if (statsWorksheet[cellAddress]) {
      statsWorksheet[cellAddress].s = {
        font: { bold: true },
        fill: { fgColor: { rgb: "F3F4F6" } },
        alignment: { horizontal: "center" }
      };
    }
  }

  XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'Statistics');

  return workbook;
};

export const downloadUsersExcel = (users: UserData[], filters?: {
  search?: string;
  role?: string;
  status?: string;
}) => {
  const workbook = generateUsersExcel(users, filters);
  const fileName = `golden-miller-members-${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(workbook, fileName);
};
