
// lib/liferrals/referralApi.ts

import axiosInstance from "../axiosInstance";

export const fetchUserReferrals = async () => {
  try {
    const response = await axiosInstance.get('/users/referrals');
    return response.data.referrals;
  } catch (error) {
    console.error('Error fetching referrals:', error);
    throw error;
  }
};

export const fetchReferralLink = async () => {
  try {
    const response = await axiosInstance.get('/users/referral-link');
    return response.data.referralLink;
  } catch (error) {
    console.error('Error fetching referral link:', error);
    throw error;
  }
};
