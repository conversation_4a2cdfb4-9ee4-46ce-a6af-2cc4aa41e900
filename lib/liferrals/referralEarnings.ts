// lib/referralEarnings.ts
import { Referral } from '@/models/Referral';
import { ReferralPayments } from '@/models/ReferralPayments';

/**
 * Update referral earnings for an investment.
 * @param userId - ID of the user who made the investment
 * @param investmentAmount - The amount of the approved investment
 */
export const updateReferralEarnings = async (userId: string, investmentAmount: number) => {
  // Find the referral linking the investor to their referrer
  const referral = await Referral.findOne({ referredUserId: userId });

  if (!referral) {
    console.log('No referral found for this user');
    return;
  }

  const earningAmount = investmentAmount * 0.1; // 10% of the investment

  // Add earning to referral's earnings array
  referral.earnings.push({
    investmentAmount,
    percentage: 10,
    earningAmount,
  });

  await referral.save();

  // Log the earning in ReferralPayments for accounting
  await ReferralPayments.create({
    referrerId: referral.referrerId,
    referredUserId: userId,
    amountEarned: earningAmount,
    investmentAmount,
  });

  console.log(`Referral earnings updated for referrer ${referral.referrerId}`);
};
