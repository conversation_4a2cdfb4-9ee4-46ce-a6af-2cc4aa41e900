// lib/liferrals/referralintroductions.ts
import { User, IUser } from '@/models/User';
import mongoose from 'mongoose';

interface ReferralNode {
  userId: string;
  name: string;
  email: string;
  referralCode: string;
  introducerId: string | null;
  introducerName: string | null;
  introducerEmail: string | null;
  level: number;
  children: ReferralNode[];
}

export async function getCompleteReferralStructure(userId: string): Promise<ReferralNode> {
  const user = await User.findById(userId).lean<IUser & { _id: mongoose.Types.ObjectId }>();
  if (!user) {
    throw new Error('User not found');
  }

  async function buildReferralTree(currentUserId: string, currentLevel: number): Promise<ReferralNode> {
    const currentUser = await User.findById(currentUserId).lean<IUser & { _id: mongoose.Types.ObjectId }>();
    if (!currentUser) {
      throw new Error(`User not found: ${currentUserId}`);
    }

    const introducer = currentUser.referrerId
      ? await User.findById(currentUser.referrerId).lean<IUser & { _id: mongoose.Types.ObjectId }>()
      : null;

    const node: ReferralNode = {
      userId: currentUser._id.toString(),
      name: currentUser.name,
      email: currentUser.email,
      referralCode: currentUser.referralCode,
      introducerId: introducer ? introducer._id.toString() : null,
      introducerName: introducer ? introducer.name : null,
      introducerEmail: introducer ? introducer.email : null,
      level: currentLevel,
      children: [],
    };

    const referrals = await User.find({ referrerId: currentUserId }).lean<Array<IUser & { _id: mongoose.Types.ObjectId }>>();
    for (const referral of referrals) {
      const childNode = await buildReferralTree(referral._id.toString(), currentLevel + 1);
      node.children.push(childNode);
    }

    return node;
  }

  const referralTree = await buildReferralTree(userId, 0);

  // Get upline (introducers)
  let currentUser = user;
  let uplineLevel = -1;
  const upline: ReferralNode[] = [];

  while (currentUser.referrerId) {
    const introducer = await User.findById(currentUser.referrerId).lean<IUser & { _id: mongoose.Types.ObjectId }>();
    if (!introducer) break;

    upline.unshift({
      userId: introducer._id.toString(),
      name: introducer.name,
      email: introducer.email,
      referralCode: introducer.referralCode,
      introducerId: introducer.referrerId ? introducer.referrerId.toString() : null,
      introducerName: null, // We don't fetch the introducer's introducer
      introducerEmail: null,
      level: uplineLevel,
      children: [],
    });

    currentUser = introducer;
    uplineLevel--;
  }

  return {
    ...referralTree,
    children: [...upline, ...referralTree.children],
  };
}

