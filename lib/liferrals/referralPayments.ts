// lib/liferrals/referralPayments.ts
import mongoose from 'mongoose';
import { User, IUser } from '@/models/User';
import { Investment, IInvestment } from '@/models/Investment';
import { ReferralPayments } from '@/models/ReferralPayments';

const LEVEL1_COMMISSION_RATE = 0.1; // 10% for level 1 referrals
const OTHER_LEVEL_COMMISSION = 45; // R45 for levels 2-4
const PAYMENT_DELAY_DAYS = 120;

interface UpcomingPayment {
  amount: number;
  dueDate: Date;
  referralId: mongoose.Types.ObjectId;
  investmentId: mongoose.Types.ObjectId;
}

interface ReferralPaymentResult {
  totalEarnings: number;
  upcomingPayments: UpcomingPayment[];
}

interface ReferralInfo {
  _id: mongoose.Types.ObjectId;
  name: string;
  email: string;
  depositAmount: number;
  isActive: boolean;
  introducerId: mongoose.Types.ObjectId;
  introducerName: string;
}

export async function calculateReferralPayments(userId: string | mongoose.Types.ObjectId): Promise<ReferralPaymentResult> {
  const userObjectId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
  
  const user = await User.findById(userObjectId);
  if (!user) {
    throw new Error('User not found');
  }

  const referrals = await User.find({ referrerId: userObjectId });
  let totalEarnings = 0;
  const upcomingPayments: UpcomingPayment[] = [];

  for (const referral of referrals) {
    const investments = await Investment.find({ 
      userId: referral._id, 
      paymentStatus: 'approved' 
    });

    for (const investment of investments) {
      let commissionAmount: number;
      if (referral.referrerId?.toString() === userObjectId.toString()) {
        // Level 1 referral
        commissionAmount = investment.amount * LEVEL1_COMMISSION_RATE;
      } else {
        // Other levels (2-4)
        commissionAmount = OTHER_LEVEL_COMMISSION;
      }

      if (investment.approvalDate) {
        const paymentDueDate = new Date(investment.approvalDate);
        paymentDueDate.setDate(paymentDueDate.getDate() + PAYMENT_DELAY_DAYS);

        totalEarnings += commissionAmount;
        upcomingPayments.push({
          amount: commissionAmount,
          dueDate: paymentDueDate,
          referralId: referral._id,
          investmentId: investment._id
        });

        // Create or update ReferralPayment record
        await ReferralPayments.findOneAndUpdate(
          { 
            referrerId: userObjectId, 
            referredUserId: referral._id, 
            investmentAmount: investment.amount 
          },
          {
            referrerId: userObjectId,
            referredUserId: referral._id,
            amountEarned: commissionAmount,
            investmentAmount: investment.amount,
            createdAt: new Date()
          },
          { upsert: true, new: true }
        );
      }
    }
  }

  return { totalEarnings, upcomingPayments };
}

export async function getReferralLevels(userId: string | mongoose.Types.ObjectId): Promise<{ [key: string]: mongoose.Types.ObjectId[] }> {
  const userObjectId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
  
  const levels: { [key: string]: mongoose.Types.ObjectId[] } = {
    level1: [],
    level2: [],
    level3: [],
    level4: []
  };

  // Get level 1 referrals
  const level1Referrals = await User.find({ referrerId: userObjectId });
  levels.level1 = level1Referrals.map(referral => referral._id);

  // Get level 2 referrals
  for (const level1Id of levels.level1) {
    const level2Referrals = await User.find({ referrerId: level1Id });
    levels.level2.push(...level2Referrals.map(referral => referral._id));
  }

  // Get level 3 referrals
  for (const level2Id of levels.level2) {
    const level3Referrals = await User.find({ referrerId: level2Id });
    levels.level3.push(...level3Referrals.map(referral => referral._id));
  }

  // Get level 4 referrals
  for (const level3Id of levels.level3) {
    const level4Referrals = await User.find({ referrerId: level3Id });
    levels.level4.push(...level4Referrals.map(referral => referral._id));
  }

  return levels;
}

export async function getTotalReferralEarnings(userId: string | mongoose.Types.ObjectId): Promise<number> {
  const userObjectId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
  
  const result = await ReferralPayments.aggregate([
    { $match: { referrerId: userObjectId } },
    { $group: { _id: null, totalEarnings: { $sum: "$amountEarned" } } }
  ]);

  return result.length > 0 ? result[0].totalEarnings : 0;
}

export async function getReferralDetails(userId: string | mongoose.Types.ObjectId): Promise<ReferralInfo[]> {
  const userObjectId = typeof userId === 'string' ? new mongoose.Types.ObjectId(userId) : userId;
  
  const referrals = await User.find({ referrerId: userObjectId }).lean<IUser & { _id: mongoose.Types.ObjectId; }>() as unknown as (IUser & { _id: mongoose.Types.ObjectId })[];

  const referralDetails: ReferralInfo[] = await Promise.all(referrals.map(async (referral: IUser & { _id: mongoose.Types.ObjectId }) => {
    const investment = await Investment.findOne({ userId: referral._id }).sort({ createdAt: -1 }).lean<IInvestment>();
    const introducer = await User.findById(referral.referrerId).lean<IUser>();

    return {
      _id: referral._id,
      name: referral.name || 'Unknown',
      email: referral.email || 'No email',
      depositAmount: investment ? investment.amount : 0,
      isActive: investment ? investment.paymentStatus === 'approved' : false,
      introducerId: referral.referrerId as mongoose.Types.ObjectId,
      introducerName: introducer ? introducer.name : 'Unknown',
    };
  }));

  return referralDetails;
}

