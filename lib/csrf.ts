// lib/csrf.ts

import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

export function generateCsrfToken(): string {
  return crypto.randomBytes(24).toString('hex');
}

export function setCsrfToken(response: NextResponse): string {
  const csrfToken = generateCsrfToken();
  response.cookies.set('csrfToken', csrfToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60, // 1 hour
    path: '/',
  });
  return csrfToken;
}

export function verifyCsrfToken(req: NextRequest, token: string): boolean {
  const csrfCookie = req.cookies.get('csrfToken')?.value;
  return csrfCookie === token;
}
