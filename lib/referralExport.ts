// lib/referralExport.ts
import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';

// Import autoTable directly for table formatting
if (typeof window !== 'undefined') {
  require('jspdf-autotable');
}

interface ReferralData {
  _id: string;
  referrer: { name: string; email: string };
  referredUser: { name: string; email: string } | null;
  referralId: string;
  createdAt: string;
  active: boolean;
  earnings?: {
    investmentAmount: number;
    percentage: number;
    earningAmount: number;
  }[];
}

interface ExportFilters {
  search?: string;
  status?: string;
}

// Force table format PDF generation
export const generateTableReferralsPDF = (referrals: ReferralData[], filters?: ExportFilters) => {
  console.log('🔥 FORCING TABLE FORMAT PDF with ALL referral records:', referrals.length);
  
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  // Header
  doc.setFillColor(147, 51, 234); // Purple theme
  doc.rect(0, 0, 297, 25, 'F');
  
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text('🌾 GOLDEN MILLER', 20, 15);
  
  doc.setFontSize(12);
  doc.text('Complete Referral Database - TABLE FORMAT', 20, 20);

  // Reset text color
  doc.setTextColor(0, 0, 0);
  
  // Summary
  const totalReferrals = referrals.length;
  const activeReferrals = referrals.filter(ref => ref.active).length;
  const totalEarnings = referrals.reduce((sum, ref) => 
    sum + (ref.earnings?.reduce((earningSum, earning) => earningSum + earning.earningAmount, 0) || 0), 0
  );
  
  doc.setFontSize(10);
  doc.text(`Total Referrals: ${totalReferrals} | Active: ${activeReferrals} | Total Earnings: $${totalEarnings.toLocaleString()}`, 20, 35);
  doc.text(`Generated: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 20, 40);

  // Table headers
  const headers = [
    ['#', 'Referrer Name', 'Referrer Email', 'Referred User', 'Referred Email', 'Referral ID', 'Earnings', 'Status', 'Date Created']
  ];

  // Table data
  const tableData = referrals.map((referral, index) => [
    (index + 1).toString(),
    referral.referrer?.name || 'N/A',
    referral.referrer?.email || 'N/A',
    referral.referredUser?.name || 'Pending',
    referral.referredUser?.email || 'No user yet',
    referral.referralId,
    referral.earnings && referral.earnings.length > 0 
      ? `$${referral.earnings.reduce((sum, earning) => sum + earning.earningAmount, 0).toLocaleString()}`
      : '$0',
    referral.active ? 'ACTIVE' : 'INACTIVE',
    new Date(referral.createdAt).toLocaleDateString()
  ]);

  console.log('Table data prepared:', tableData.length, 'rows');

  // Force autoTable usage
  try {
    (doc as any).autoTable({
      head: headers,
      body: tableData,
      startY: 50,
      theme: 'grid',
      styles: {
        fontSize: 7,
        cellPadding: 2,
        overflow: 'linebreak'
      },
      headStyles: {
        fillColor: [147, 51, 234], // Purple theme
        textColor: [255, 255, 255],
        fontStyle: 'bold'
      },
      columnStyles: {
        0: { halign: 'center', cellWidth: 12 },  // #
        1: { cellWidth: 30 },                    // Referrer Name
        2: { cellWidth: 35 },                    // Referrer Email
        3: { cellWidth: 30 },                    // Referred User
        4: { cellWidth: 35 },                    // Referred Email
        5: { halign: 'center', cellWidth: 25 },  // Referral ID
        6: { halign: 'right', cellWidth: 20 },   // Earnings
        7: { halign: 'center', cellWidth: 20 },  // Status
        8: { halign: 'center', cellWidth: 25 }   // Date Created
      },
      alternateRowStyles: {
        fillColor: [248, 250, 252]
      },
      margin: { top: 50, left: 10, right: 10 },
      pageBreak: 'auto',
      showHead: 'everyPage',
      didDrawCell: function(data: any) {
        // Color-code status column (index 7)
        if (data.column.index === 7) {
          const status = data.cell.text[0];
          if (status === 'ACTIVE') {
            doc.setTextColor(5, 150, 105);
          } else {
            doc.setTextColor(220, 38, 38);
          }
        }
      }
    });
    
    console.log('✅ TABLE PDF generated successfully with', doc.getNumberOfPages(), 'pages');
    return doc;
    
  } catch (error) {
    console.error('❌ AutoTable failed, using manual table:', error);
    
    // Manual table fallback
    let yPos = 60;
    const pageHeight = 200;
    const rowHeight = 8;
    
    // Headers
    doc.setFillColor(147, 51, 234);
    doc.rect(20, yPos - 5, 257, 10, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(8);
    doc.setFont('helvetica', 'bold');
    
    const colPositions = [25, 45, 75, 105, 135, 165, 185, 205, 225];
    const colHeaders = ['#', 'Referrer', 'R.Email', 'Referred', 'Ref.Email', 'ID', 'Earnings', 'Status', 'Date'];
    
    colHeaders.forEach((header, i) => {
      doc.text(header, colPositions[i], yPos);
    });
    
    yPos += 10;
    doc.setTextColor(0, 0, 0);
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(6);
    
    // Data rows
    tableData.forEach((row, index) => {
      if (yPos > pageHeight) {
        doc.addPage();
        yPos = 20;
        
        // Repeat headers on new page
        doc.setFillColor(147, 51, 234);
        doc.rect(20, yPos - 5, 257, 10, 'F');
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(8);
        doc.setFont('helvetica', 'bold');
        
        colHeaders.forEach((header, i) => {
          doc.text(header, colPositions[i], yPos);
        });
        
        yPos += 10;
        doc.setTextColor(0, 0, 0);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(6);
      }
      
      // Alternate row colors
      if (index % 2 === 0) {
        doc.setFillColor(248, 250, 252);
        doc.rect(20, yPos - 3, 257, rowHeight, 'F');
      }
      
      // Row data
      row.forEach((cell, i) => {
        const text = cell.toString().substring(0, 12); // Truncate long text
        doc.text(text, colPositions[i], yPos);
      });
      
      yPos += rowHeight;
    });
    
    console.log('✅ Manual table PDF generated with', doc.getNumberOfPages(), 'pages');
    return doc;
  }
};

export const downloadReferralsPDF = (referrals: ReferralData[], filters?: ExportFilters) => {
  try {
    console.log('Starting comprehensive referral PDF generation with ALL database records:', referrals.length);
    
    if (!referrals || referrals.length === 0) {
      throw new Error('No referral data provided for PDF generation');
    }
    
    let doc;
    
    // Always try table format first
    try {
      console.log('🔥 FORCING TABLE FORMAT PDF generation...');
      doc = generateTableReferralsPDF(referrals, filters);
      console.log('✅ TABLE FORMAT PDF generated successfully');
    } catch (tableError) {
      console.warn('❌ Table format failed:', tableError);
      throw tableError;
    }
    
    if (!doc) {
      throw new Error('PDF document generation failed');
    }
    
    const fileName = `golden-miller-complete-referrals-database-${new Date().toISOString().split('T')[0]}.pdf`;
    console.log('Referral PDF with ALL database records generated successfully, saving:', fileName);
    
    doc.save(fileName);
    console.log('Complete referral database PDF save completed successfully');
    
  } catch (error) {
    console.error('Error in downloadReferralsPDF:', error);
    console.error('Error stack:', (error as Error).stack);
    throw new Error(`Referral PDF export failed: ${(error as Error).message}`);
  }
};

export const downloadReferralsExcel = (referrals: ReferralData[], filters?: ExportFilters) => {
  try {
    console.log('Starting comprehensive referral Excel generation with ALL database records:', referrals.length);
    
    if (!referrals || referrals.length === 0) {
      throw new Error('No referral data provided for Excel generation');
    }

    // Prepare comprehensive data for Excel - ALL FIELDS
    const excelData = referrals.map((referral, index) => ({
      '#': index + 1,
      'Referrer Name': referral.referrer?.name || 'N/A',
      'Referrer Email': referral.referrer?.email || 'N/A',
      'Referred User Name': referral.referredUser?.name || 'Pending',
      'Referred User Email': referral.referredUser?.email || 'No user yet',
      'Referral ID': referral.referralId,
      'Total Earnings ($)': referral.earnings?.reduce((sum, earning) => sum + earning.earningAmount, 0) || 0,
      'Number of Earnings': referral.earnings?.length || 0,
      'Status': referral.active ? 'ACTIVE' : 'INACTIVE',
      'Date Created': new Date(referral.createdAt).toLocaleDateString(),
      'Has Referred User': referral.referredUser ? 'Yes' : 'No'
    }));

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(excelData);

    // Set comprehensive column widths for ALL FIELDS
    const colWidths = [
      { wch: 5 },   // #
      { wch: 20 },  // Referrer Name
      { wch: 30 },  // Referrer Email
      { wch: 20 },  // Referred User Name
      { wch: 30 },  // Referred User Email
      { wch: 15 },  // Referral ID
      { wch: 15 },  // Total Earnings
      { wch: 12 },  // Number of Earnings
      { wch: 10 },  // Status
      { wch: 12 },  // Date Created
      { wch: 15 }   // Has Referred User
    ];
    ws['!cols'] = colWidths;

    // Add comprehensive summary sheet with ALL DATABASE STATISTICS
    const totalEarnings = referrals.reduce((sum, ref) => 
      sum + (ref.earnings?.reduce((earningSum, earning) => earningSum + earning.earningAmount, 0) || 0), 0
    );
    const averageEarnings = totalEarnings / referrals.length || 0;
    
    const summaryData = [
      { Metric: 'COMPLETE DATABASE EXPORT', Value: 'ALL RECORDS INCLUDED' },
      { Metric: '', Value: '' },
      { Metric: 'Total Referrals', Value: referrals.length },
      { Metric: 'Active Referrals', Value: referrals.filter(ref => ref.active).length },
      { Metric: 'Inactive Referrals', Value: referrals.filter(ref => !ref.active).length },
      { Metric: 'Referrals with Users', Value: referrals.filter(ref => ref.referredUser).length },
      { Metric: 'Pending Referrals', Value: referrals.filter(ref => !ref.referredUser).length },
      { Metric: '', Value: '' },
      { Metric: 'Total Earnings', Value: `$${totalEarnings.toLocaleString()}` },
      { Metric: 'Average Earnings per Referral', Value: `$${averageEarnings.toLocaleString()}` },
      { Metric: 'Referrals with Earnings', Value: referrals.filter(ref => ref.earnings && ref.earnings.length > 0).length },
      { Metric: '', Value: '' },
      { Metric: 'Export Generated Date', Value: new Date().toLocaleDateString() },
      { Metric: 'Export Generated Time', Value: new Date().toLocaleTimeString() },
      { Metric: 'Export Type', Value: 'Complete Database Export' }
    ];

    if (filters?.search || filters?.status !== 'all') {
      summaryData.push({ Metric: '', Value: '' });
      summaryData.push({ Metric: 'APPLIED FILTERS', Value: '' });
      if (filters?.search) summaryData.push({ Metric: 'Search Filter', Value: filters.search });
      if (filters?.status !== 'all') summaryData.push({ Metric: 'Status Filter', Value: filters.status });
    }

    const summaryWs = XLSX.utils.json_to_sheet(summaryData);
    summaryWs['!cols'] = [{ wch: 25 }, { wch: 30 }];

    // Add sheets to workbook with descriptive names
    XLSX.utils.book_append_sheet(wb, summaryWs, 'Executive Summary');
    XLSX.utils.book_append_sheet(wb, ws, 'Complete Referral Database');

    // Generate filename and save
    const fileName = `golden-miller-complete-referrals-database-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
    
    console.log('Complete referral database Excel export completed successfully');
    
  } catch (error) {
    console.error('Error in downloadReferralsExcel:', error);
    throw new Error(`Referral Excel export failed: ${(error as Error).message}`);
  }
};
