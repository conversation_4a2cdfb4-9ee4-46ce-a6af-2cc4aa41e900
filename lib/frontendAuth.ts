

// lib/frontendAuth.ts

import { User } from '@/types/user';

interface LoginResponse {
  message: string;
  user: User;
  accessTokenExpiresIn: number; 
  // any other fields your backend sends back for `x-client-type: web`
}

/**
 * Log in the user by sending email/password to the server.
 * The server will set accessToken and refreshToken in HttpOnly cookies
 * when x-client-type = 'web' and credentials = 'include' are used.
 */
export async function loginUser(
  email: string,
  password: string,
  rememberMe: boolean
): Promise<{ user: User }> {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    credentials: 'include', // <--- Important: include cookies
    headers: {
      'Content-Type': 'application/json',
      'x-client-type': 'web', // <--- Key for web-based cookie auth
    },
    body: JSON.stringify({ email, password, rememberMe }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Login failed');
  }

  // The server's response for "web" should contain user info in JSON.
  // The server sets HttpOnly cookies for the tokens.
  const data: LoginResponse = await response.json();
  return { user: data.user };
}

/**
 * Log out the user by calling the backend /api/auth/logout route.
 * The server should clear/invalidate the cookies.
 */
export async function logoutUser(userId?: string | null): Promise<void> {
  const response = await fetch('/api/auth/logout', {
    method: 'POST',
    credentials: 'include', // <--- include cookies in request
    headers: {
      'Content-Type': 'application/json',
      'x-client-type': 'web',
    },
    body: JSON.stringify({ userId: userId || null }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    console.error('Logout failed:', errorData.error || 'Unknown error');
  }
}

/**
 * Optionally, you might have a "getLoggedInUser" endpoint that returns
 * the user profile if the user has a valid cookie on the server side.
 * You can call this to re-hydrate the user on page refresh.
 */


export async function getLoggedInUser(): Promise<User | null> {
  const response = await fetch('/api/auth/me', {
    method: 'GET',
    credentials: 'include', // <--- Important so that browser sends cookies
    headers: {
      'Content-Type': 'application/json',
      'x-client-type': 'web',    // <--- Tells server to look for cookie
    },
  });

  if (!response.ok) {
    // Could be 401 if not logged in or token invalid
    return null;
  }

  const data = await response.json();
  return data.user as User;
}