/**
 * Currency Utility for TGM Finance
 * Handles all currency formatting and display for South African Rand (ZAR)
 */

export interface CurrencyConfig {
  code: string;
  symbol: string;
  name: string;
  locale: string;
  decimals: number;
}

// South African Rand configuration
export const ZAR_CONFIG: CurrencyConfig = {
  code: '<PERSON><PERSON>',
  symbol: 'R',
  name: 'South African Rand',
  locale: 'en-ZA',
  decimals: 2,
};

// Default currency configuration
export const DEFAULT_CURRENCY = ZAR_CONFIG;

/**
 * Format a number as South African Rand currency
 * @param amount - The amount to format
 * @param options - Optional formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number | string,
  options: {
    showSymbol?: boolean;
    showCode?: boolean;
    decimals?: number;
    locale?: string;
  } = {}
): string {
  const {
    showSymbol = true,
    showCode = false,
    decimals = DEFAULT_CURRENCY.decimals,
    locale = DEFAULT_CURRENCY.locale,
  } = options;

  // Convert string to number if needed
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  // Handle invalid numbers
  if (isNaN(numericAmount)) {
    return showSymbol ? `${DEFAULT_CURRENCY.symbol}0.00` : '0.00';
  }

  // Format the number using Intl.NumberFormat
  const formatter = new Intl.NumberFormat(locale, {
    style: 'decimal',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });

  const formattedAmount = formatter.format(numericAmount);

  // Build the final string
  let result = '';
  
  if (showSymbol) {
    result = `${DEFAULT_CURRENCY.symbol}${formattedAmount}`;
  } else {
    result = formattedAmount;
  }

  if (showCode) {
    result = `${result} ${DEFAULT_CURRENCY.code}`;
  }

  return result;
}

/**
 * Format currency for display in tables and lists
 * @param amount - The amount to format
 * @returns Formatted currency string with symbol
 */
export function formatCurrencyDisplay(amount: number | string): string {
  return formatCurrency(amount, { showSymbol: true, showCode: false });
}

/**
 * Format currency for compact display (e.g., in cards or summaries)
 * @param amount - The amount to format
 * @returns Formatted currency string
 */
export function formatCurrencyCompact(amount: number | string): string {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numericAmount)) {
    return `${DEFAULT_CURRENCY.symbol}0`;
  }

  // For large amounts, use compact notation
  if (numericAmount >= 1000000) {
    return `${DEFAULT_CURRENCY.symbol}${(numericAmount / 1000000).toFixed(1)}M`;
  } else if (numericAmount >= 1000) {
    return `${DEFAULT_CURRENCY.symbol}${(numericAmount / 1000).toFixed(1)}K`;
  } else {
    return formatCurrency(numericAmount, { decimals: 0 });
  }
}

/**
 * Format currency for export documents (PDF, Excel)
 * @param amount - The amount to format
 * @returns Formatted currency string with code
 */
export function formatCurrencyExport(amount: number | string): string {
  return formatCurrency(amount, { showSymbol: true, showCode: true });
}

/**
 * Format currency for input fields (no symbol, just number)
 * @param amount - The amount to format
 * @returns Formatted number string
 */
export function formatCurrencyInput(amount: number | string): string {
  return formatCurrency(amount, { showSymbol: false, showCode: false });
}

/**
 * Parse a currency string back to a number
 * @param currencyString - The currency string to parse
 * @returns Parsed number or 0 if invalid
 */
export function parseCurrency(currencyString: string): number {
  if (!currencyString) return 0;
  
  // Remove currency symbols, codes, and spaces
  const cleanString = currencyString
    .replace(/[R$€£¥₹]/g, '') // Remove common currency symbols
    .replace(/[A-Z]{3}/g, '') // Remove currency codes
    .replace(/\s/g, '') // Remove spaces
    .replace(/,/g, ''); // Remove thousands separators
  
  const parsed = parseFloat(cleanString);
  return isNaN(parsed) ? 0 : parsed;
}

/**
 * Get currency symbol
 * @returns Currency symbol
 */
export function getCurrencySymbol(): string {
  return DEFAULT_CURRENCY.symbol;
}

/**
 * Get currency code
 * @returns Currency code
 */
export function getCurrencyCode(): string {
  return DEFAULT_CURRENCY.code;
}

/**
 * Get currency name
 * @returns Currency name
 */
export function getCurrencyName(): string {
  return DEFAULT_CURRENCY.name;
}

/**
 * Format percentage with currency context
 * @param percentage - The percentage to format
 * @returns Formatted percentage string
 */
export function formatPercentage(percentage: number | string): string {
  const numericPercentage = typeof percentage === 'string' ? parseFloat(percentage) : percentage;
  
  if (isNaN(numericPercentage)) {
    return '0%';
  }
  
  return `${numericPercentage.toFixed(2)}%`;
}

/**
 * Calculate and format currency with percentage
 * @param amount - Base amount
 * @param percentage - Percentage to calculate
 * @returns Object with original amount, calculated amount, and formatted strings
 */
export function calculateCurrencyWithPercentage(
  amount: number | string,
  percentage: number | string
): {
  originalAmount: number;
  calculatedAmount: number;
  originalFormatted: string;
  calculatedFormatted: string;
  percentageFormatted: string;
} {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  const numericPercentage = typeof percentage === 'string' ? parseFloat(percentage) : percentage;
  
  const calculatedAmount = numericAmount * (numericPercentage / 100);
  
  return {
    originalAmount: numericAmount,
    calculatedAmount,
    originalFormatted: formatCurrency(numericAmount),
    calculatedFormatted: formatCurrency(calculatedAmount),
    percentageFormatted: formatPercentage(numericPercentage),
  };
}

/**
 * Format currency range (e.g., "R1,000 - R5,000")
 * @param minAmount - Minimum amount
 * @param maxAmount - Maximum amount
 * @returns Formatted currency range string
 */
export function formatCurrencyRange(
  minAmount: number | string,
  maxAmount: number | string
): string {
  const minFormatted = formatCurrency(minAmount);
  const maxFormatted = formatCurrency(maxAmount);
  return `${minFormatted} - ${maxFormatted}`;
}

/**
 * Validate if a string is a valid currency amount
 * @param value - The value to validate
 * @returns True if valid currency amount
 */
export function isValidCurrencyAmount(value: string): boolean {
  const parsed = parseCurrency(value);
  return !isNaN(parsed) && parsed >= 0;
}

// Export default currency configuration
export default {
  formatCurrency,
  formatCurrencyDisplay,
  formatCurrencyCompact,
  formatCurrencyExport,
  formatCurrencyInput,
  parseCurrency,
  getCurrencySymbol,
  getCurrencyCode,
  getCurrencyName,
  formatPercentage,
  calculateCurrencyWithPercentage,
  formatCurrencyRange,
  isValidCurrencyAmount,
  ZAR_CONFIG,
  DEFAULT_CURRENCY,
};
