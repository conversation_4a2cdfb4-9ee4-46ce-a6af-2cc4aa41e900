// lib/tokenUtils.ts
import jwt, { JwtPayload } from 'jsonwebtoken';

/**
 * Decodes and verifies a JWT token.
 * @param token - The JWT to verify.
 * @param secret - The secret key for signing.
 * @returns The decoded payload if valid.
 * @throws Error if the token is invalid or expired.
 */
export const verifyToken = (token: string, secret: string): JwtPayload => {
  try {
    return jwt.verify(token, secret) as JwtPayload;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
};

/**
 * Generates a signed JWT token.
 * @param payload - The data to include in the token.
 * @param secret - The secret key for signing.
 * @param options - Token signing options.
 * @returns The signed JWT token.
 */
export const generateToken = (payload: object, secret: string, options: jwt.SignOptions): string => {
  return jwt.sign(payload, secret, options);
};
