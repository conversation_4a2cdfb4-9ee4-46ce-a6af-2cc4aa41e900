// lib/paymentCountdown.ts


import { addDays } from 'date-fns';
import { thirtyDayPaymentCountdown } from './thirtyDayPaymentCountdown';
// import { sendPaymentApprovalNotifications } from './sendNotifications';

export const startPaymentCountdown = async (subscriptionId: string, approvalDate: Date, amount: number, userId: string) => {
  const earningsStartDate = addDays(approvalDate, 120);

  console.log(`120-day countdown started, earnings will begin on: ${earningsStartDate.toDateString()}`);

  // Simulate the 120-day countdown
  await new Promise((resolve) => setTimeout(resolve, 120 * 24 * 60 * 60 * 1000));

  console.log(`120-day countdown completed. Starting 30-day payment countdown for user ${userId}`);
  await thirtyDayPaymentCountdown(subscriptionId, userId, amount, earningsStartDate);
};
