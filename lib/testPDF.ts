// lib/testPDF.ts
import jsPDF from 'jspdf';

export const generateTestPDF = () => {
  try {
    console.log('Creating test PDF...');

    // Create a simple PDF document
    const doc = new jsPDF();
    console.log('jsPDF instance created');

    // Add some basic content
    doc.setFontSize(20);
    doc.text('Golden Miller Test PDF', 20, 30);
    console.log('Title added');

    doc.setFontSize(12);
    doc.text('This is a test PDF to verify jsPDF is working correctly.', 20, 50);
    doc.text('Generated on: ' + new Date().toLocaleString(), 20, 70);
    console.log('Basic text added');

    // Add some sample data
    doc.text('Sample Member Data:', 20, 100);
    doc.text('Name: <PERSON>', 30, 120);
    doc.text('Email: <EMAIL>', 30, 140);
    doc.text('Role: Investor', 30, 160);
    doc.text('Status: Active', 30, 180);
    console.log('Sample data added');

    console.log('Test PDF created successfully, attempting to save...');

    // Try manual download approach
    const fileName = `golden-miller-test-${Date.now()}.pdf`;
    console.log('About to save with filename:', fileName);

    try {
      // Method 1: Use jsPDF's built-in save
      doc.save(fileName);
      console.log('doc.save() completed successfully');
    } catch (saveError) {
      console.error('doc.save() failed, trying manual approach:', saveError);

      // Method 2: Manual blob download
      const pdfBlob = doc.output('blob');
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      console.log('Manual download completed');
    }
    return true;
  } catch (error) {
    console.error('Error creating test PDF:', error);
    console.error('Error stack:', (error as Error).stack);
    throw error;
  }
};

export const generateSimpleUsersPDF = (users: any[]) => {
  try {
    console.log('Creating simple users PDF with', users.length, 'users');
    
    const doc = new jsPDF();
    
    // Header
    doc.setFontSize(16);
    doc.text('Golden Miller Members Report', 20, 20);
    
    doc.setFontSize(10);
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, 20, 30);
    doc.text(`Total Members: ${users.length}`, 20, 40);
    
    // Simple list of users
    let yPosition = 60;
    doc.setFontSize(12);
    doc.text('Members List:', 20, yPosition);
    
    yPosition += 20;
    doc.setFontSize(10);
    
    users.slice(0, 20).forEach((user, index) => { // Limit to first 20 users
      if (yPosition > 250) { // Add new page if needed
        doc.addPage();
        yPosition = 20;
      }
      
      doc.text(`${index + 1}. ${user.name} - ${user.email} - ${user.role}`, 20, yPosition);
      yPosition += 15;
    });
    
    if (users.length > 20) {
      yPosition += 10;
      doc.text(`... and ${users.length - 20} more members`, 20, yPosition);
    }
    
    console.log('Simple PDF created successfully');
    
    const fileName = `golden-miller-simple-${Date.now()}.pdf`;
    doc.save(fileName);
    
    console.log('Simple PDF saved:', fileName);
    return true;
  } catch (error) {
    console.error('Error creating simple PDF:', error);
    throw error;
  }
};
