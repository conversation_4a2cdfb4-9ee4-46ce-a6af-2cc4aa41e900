// // services/planService.ts
// export async function subscribeToPlan(token: string, planDetails: any) {
//     try {
//       const response = await fetch('/api/plans/subscribe', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//           Authorization: `<PERSON><PERSON> ${token}`,
//         },
//         body: JSON.stringify(planDetails),
//       });
  
//       const data = await response.json();
//       if (!response.ok) {
//         throw new Error(data.error || 'Something went wrong');
//       }
//       return data; // Return data for success
//     } catch (error) {
//       console.error('Subscription error:', error);
//       throw error; // Rethrow error for error handling in the component
//     }
//   }
  