

// lib/dbconnect.ts
import mongoose from 'mongoose';
import { GridFSBucket } from 'mongodb';

declare global {
  // eslint-disable-next-line no-var
  var mongooseConnection: Promise<typeof mongoose> | null;
  // eslint-disable-next-line no-var
  var gridFSBucket: GridFSBucket | null;
}

// Initialize global variables if they don't exist
if (!global.mongooseConnection) {
  global.mongooseConnection = null;
}
if (!global.gridFSBucket) {
  global.gridFSBucket = null;
}

export const connectToDatabase = async (): Promise<{ gridFSBucket: GridFSBucket | null }> => {
  mongoose.set('strictQuery', true);

  if (!process.env.MONGODB_URL) {
    console.error('MISSING MONGODB_URL');
    return { gridFSBucket: null };
  }

  // Check if Mongoose is already connected
  if (mongoose.connection.readyState === 1) {
    console.log('Using existing MongoDB connection');
    return { gridFSBucket: global.gridFSBucket };
  }

  if (!global.mongooseConnection) {
    try {
      // Create a new connection
      global.mongooseConnection = mongoose.connect(process.env.MONGODB_URL, {
        dbName: 'goldenmillerdb',
        // Optional: Add options like poolSize, useUnifiedTopology, etc.
      });

      await global.mongooseConnection;
      console.log('MongoDB connected successfully');

      // Initialize GridFSBucket
      const db = mongoose.connection.db;
      if (!db) {
        throw new Error('Database object not found after connection');
      }

      global.gridFSBucket = new GridFSBucket(db, {
        bucketName: 'uploads', // Name your bucket
      });
    } catch (error) {
      console.error('MongoDB connection error:', error);
      global.mongooseConnection = null;
      return { gridFSBucket: null };
    }
  } else {
    // Wait for the existing connection promise to resolve
    await global.mongooseConnection;
  }

  return { gridFSBucket: global.gridFSBucket };
};

// Function to get GridFSBucket instance
export const getGridFSBucket = async (): Promise<GridFSBucket | null> => {
  if (!global.gridFSBucket) {
    const result = await connectToDatabase();
    return result.gridFSBucket;
  }
  return global.gridFSBucket;
};




