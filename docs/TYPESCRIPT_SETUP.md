# TypeScript Configuration Setup

This project uses a dual TypeScript configuration approach to maintain strict type checking in development while allowing easier deployment to Vercel.

## Configuration Files

- `tsconfig.json` - Base TypeScript configuration
- `tsconfig.dev.json` - Strict TypeScript settings for development
- `tsconfig.prod.json` - Relaxed TypeScript settings for production
- `next.config.mjs` - Next.js configuration with environment-based TypeScript handling
- `vercel.json` - Vercel deployment configuration

## How It Works

### Development Mode
- Uses strict TypeScript checking by default
- All type errors must be resolved
- Helps maintain code quality and catch issues early

### Production/Vercel Deployment
- Automatically uses relaxed TypeScript settings
- Ignores TypeScript and ESLint errors during build
- Allows deployment even with minor type issues

## Available Scripts

### TypeScript Mode Switching
```bash
# Switch to strict TypeScript (development)
npm run ts:strict

# Switch to relaxed TypeScript (easier deployment)
npm run ts:relaxed

# Switch to production TypeScript settings
npm run ts:production
```

### Build Scripts
```bash
# Development build (strict TypeScript)
npm run build

# Production build (relaxed TypeScript)
npm run build:prod

# Strict build (fails on any TypeScript errors)
npm run build:strict
```

### Type Checking
```bash
# Type check with current config
npm run type-check

# Type check with production config
npm run type-check:prod
```

## Environment Variables

The configuration automatically detects:
- `NODE_ENV=production` - Uses production TypeScript config
- `VERCEL=1` - Vercel deployment environment (ignores TypeScript errors)

## Vercel Deployment

The `vercel.json` file ensures that:
1. Production build command is used
2. Environment variables are set correctly
3. TypeScript errors don't block deployment

## Best Practices

1. **Development**: Keep strict TypeScript enabled to catch issues early
2. **Before Deployment**: Test with relaxed settings using `npm run ts:relaxed`
3. **CI/CD**: Use `npm run build:prod` for production builds
4. **Code Quality**: Regularly run `npm run ts:strict` to maintain type safety

## Troubleshooting

If you encounter TypeScript errors during deployment:

1. Check if the error is critical or just a type annotation issue
2. Use `npm run ts:relaxed` to test locally with relaxed settings
3. For critical errors, fix them before deployment
4. For minor type issues, the production build will ignore them

## Manual Configuration

If you need to manually adjust TypeScript strictness:

1. Edit `tsconfig.prod.json` for production settings
2. Edit `tsconfig.dev.json` for development settings
3. Run the appropriate `npm run ts:*` command to apply changes
