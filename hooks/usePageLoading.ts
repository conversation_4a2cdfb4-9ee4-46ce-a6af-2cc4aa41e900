'use client';

import { useState, useEffect } from 'react';
import { useLoading } from '@/context/LoadingContext';

interface UsePageLoadingOptions {
  initialLoading?: boolean;
  loadingText?: string;
  minLoadingTime?: number; // Minimum time to show loading (in ms)
}

export const usePageLoading = (options: UsePageLoadingOptions = {}) => {
  const {
    initialLoading = false,
    loadingText = 'Loading...',
    minLoadingTime = 500,
  } = options;

  const { showLoading, hideLoading } = useLoading();
  const [isPageLoading, setIsPageLoading] = useState(initialLoading);
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null);

  // Start loading
  const startLoading = (text?: string) => {
    setLoadingStartTime(Date.now());
    setIsPageLoading(true);
    showLoading(text || loadingText);
  };

  // Stop loading with minimum time enforcement
  const stopLoading = async () => {
    if (loadingStartTime) {
      const elapsedTime = Date.now() - loadingStartTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);
      
      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }
    }
    
    setIsPageLoading(false);
    setLoadingStartTime(null);
    hideLoading();
  };

  // Auto-start loading if initialLoading is true
  useEffect(() => {
    if (initialLoading) {
      startLoading();
    }
    
    // Cleanup function
    return () => {
      if (isPageLoading) {
        hideLoading();
      }
    };
  }, []);

  return {
    isLoading: isPageLoading,
    startLoading,
    stopLoading,
  };
};
