// // context/ThemeContext.tsx

// 'use client';
// import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// interface ThemeContextType {
//   theme: 'light' | 'dark';
//   toggleTheme: () => void;
// }

// const ThemeContext = createContext<ThemeContextType | null>(null);

// export const useTheme = () => {
//   const context = useContext(ThemeContext);
//   if (!context) {
//     throw new Error('useTheme must be used within a ThemeProvider');
//   }
//   return context;
// };

// export const ThemeProvider = ({ children }: { children: ReactNode }) => {
//   const [theme, setTheme] = useState<'light' | 'dark'>('light');

//   useEffect(() => {
//     const storedTheme = localStorage.getItem('theme') as 'light' | 'dark';
//     if (storedTheme) {
//       setTheme(storedTheme);
//       document.documentElement.classList.toggle('dark', storedTheme === 'dark');
//     }
//   }, []);

//   const toggleTheme = () => {
//     const newTheme = theme === 'light' ? 'dark' : 'light';
//     setTheme(newTheme);
//     document.documentElement.classList.toggle('dark', newTheme === 'dark');
//     localStorage.setItem('theme', newTheme);
//   };

//   return (
//     <ThemeContext.Provider value={{ theme, toggleTheme }}>
//       {children}
//     </ThemeContext.Provider>
//   );
// };


// 'use client';
// import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
// import { Sun, Moon } from 'lucide-react';

// interface ThemeContextType {
//   theme: 'light' | 'dark';
//   toggleTheme: () => void;
//   SunIcon: typeof Sun;
//   MoonIcon: typeof Moon;
// }

// const ThemeContext = createContext<ThemeContextType | null>(null);

// export const useTheme = () => {
//   const context = useContext(ThemeContext);
//   if (!context) {
//     throw new Error('useTheme must be used within a ThemeProvider');
//   }
//   return context;
// };

// export const ThemeProvider = ({ children }: { children: ReactNode }) => {
//   const [theme, setTheme] = useState<'light' | 'dark'>('light');

//   useEffect(() => {
//     const storedTheme = localStorage.getItem('theme') as 'light' | 'dark';
//     if (storedTheme) {
//       setTheme(storedTheme);
//       document.documentElement.classList.toggle('dark', storedTheme === 'dark');
//     }
//   }, []);

//   const toggleTheme = () => {
//     const newTheme = theme === 'light' ? 'dark' : 'light';
//     setTheme(newTheme);
//     document.documentElement.classList.toggle('dark', newTheme === 'dark');
//     localStorage.setItem('theme', newTheme);
//   };

//   return (
//     <ThemeContext.Provider value={{ theme, toggleTheme, SunIcon: Sun, MoonIcon: Moon }}>
//       {children}
//     </ThemeContext.Provider>
//   );
// };

// export { Sun as SunIcon, Moon as MoonIcon };




'use client';
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Sun, Moon } from 'lucide-react';

interface ThemeContextType {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
  SunIcon: typeof Sun;
  MoonIcon: typeof Moon;
}

const ThemeContext = createContext<ThemeContextType | null>(null);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    const storedTheme = localStorage.getItem('theme') as 'light' | 'dark';
    if (storedTheme) {
      setTheme(storedTheme);
      updateTheme(storedTheme);
    }
  }, []);

  const updateTheme = (newTheme: 'light' | 'dark') => {
    document.documentElement.classList.toggle('dark', newTheme === 'dark');
    document.documentElement.style.setProperty('--bg-color', newTheme === 'dark' ? '#b45309' : '#ffffff');
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    updateTheme(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, SunIcon: Sun, MoonIcon: Moon }}>
      {children}
    </ThemeContext.Provider>
  );
};

export { Sun as SunIcon, Moon as MoonIcon };

