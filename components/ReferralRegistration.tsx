// components/ReferralRegistration.tsx


import React from 'react';
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

interface UserSignupFormProps {
  name: string;
  email: string;
  password: string;
  phone: string;
  error: string | null;
  loading: boolean;
  success: boolean;
  onChange: (field: string, value: string) => void;
  onSubmit: () => void;
}

const ReferralRegistration: React.FC<UserSignupFormProps> = ({
  name,
  email,
  password,
  phone,
  error,
  loading,
  success,
  onChange,
  onSubmit,
}) => {
  return (
    <div className="bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-md space-y-4 sm:space-y-6 text-gray-800 w-full max-w-md mx-auto">
      <h2 className="text-xl sm:text-2xl font-semibold text-center text-yellow-700">Join <PERSON></h2>
      {loading && <p className="text-center text-blue-600">Loading...</p>}
      {error && <p className="text-red-600 text-center text-sm sm:text-base">{error}</p>}
      {success && <p className="text-green-600 text-center text-sm sm:text-base">Account created successfully!</p>}

      {!loading && !success && (
        <form onSubmit={(e) => { e.preventDefault(); onSubmit(); }} className="space-y-3 sm:space-y-4">
          <Input
            type="text"
            placeholder="Name"
            value={name}
            onChange={(e) => onChange('name', e.target.value)}
            className="w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-600 focus:border-transparent"
          />
          <Input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => onChange('email', e.target.value)}
            className="w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-600 focus:border-transparent"
          />
          <Input
            type="password"
            placeholder="Password"
            value={password}
            onChange={(e) => onChange('password', e.target.value)}
            className="w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-600 focus:border-transparent"
          />
          <Input
            type="tel"
            placeholder="Phone Number"
            value={phone}
            onChange={(e) => onChange('phone', e.target.value)}
            className="w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-600 focus:border-transparent"
          />
          <Button 
            type="submit"
            className="w-full bg-yellow-700 hover:bg-yellow-600 text-white font-semibold py-2 sm:py-3 px-4 text-sm sm:text-base rounded-lg transition duration-300 ease-in-out transform hover:scale-105"
          >
            Submit Now
          </Button>
        </form>
      )}
    </div>
  );
};

export default ReferralRegistration;

