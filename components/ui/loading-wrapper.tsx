import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface LoadingWrapperProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  loadingVariant?: 'skeleton' | 'spinner' | 'pulse';
  className?: string;
}

export const LoadingWrapper: React.FC<LoadingWrapperProps> = ({
  isLoading,
  children,
  loadingText = 'Loading...',
  loadingVariant = 'skeleton',
  className = ''
}) => {
  if (!isLoading) {
    return <>{children}</>;
  }

  const renderLoadingContent = () => {
    switch (loadingVariant) {
      case 'spinner':
        return (
          <div className={`flex items-center justify-center p-8 ${className}`}>
            <div className="flex flex-col items-center space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
              <p className="text-slate-600 dark:text-slate-400">{loadingText}</p>
            </div>
          </div>
        );

      case 'pulse':
        return (
          <div className={`animate-pulse ${className}`}>
            <Card className="border-0 shadow-xl bg-white/90 dark:bg-slate-900/90">
              <CardHeader className="border-b border-slate-200 dark:border-slate-700">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="h-6 bg-slate-200 dark:bg-slate-700 rounded w-[200px]"></div>
                    <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-[300px]"></div>
                  </div>
                  <div className="h-10 bg-slate-200 dark:bg-slate-700 rounded w-[120px]"></div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center space-x-4 p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
                      <div className="h-4 w-4 bg-slate-200 dark:bg-slate-700 rounded"></div>
                      <div className="h-10 w-10 bg-slate-200 dark:bg-slate-700 rounded-full"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-[150px]"></div>
                        <div className="h-3 bg-slate-200 dark:bg-slate-700 rounded w-[200px]"></div>
                      </div>
                      <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-[100px]"></div>
                      <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-[80px]"></div>
                      <div className="h-8 w-8 bg-slate-200 dark:bg-slate-700 rounded"></div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'skeleton':
      default:
        return (
          <div className={`space-y-4 ${className}`}>
            <Card className="border-0 shadow-xl bg-white/90 dark:bg-slate-900/90">
              <CardHeader className="border-b border-slate-200 dark:border-slate-700">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-[200px]" />
                    <Skeleton className="h-4 w-[300px]" />
                  </div>
                  <Skeleton className="h-10 w-[120px]" />
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <Skeleton className="h-10 w-[400px]" />
                    <Skeleton className="h-10 w-[120px]" />
                    <Skeleton className="h-10 w-[100px]" />
                  </div>
                  <div className="space-y-3">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div key={i} className="flex items-center space-x-4 p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
                        <Skeleton className="h-4 w-4" />
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-[150px]" />
                          <Skeleton className="h-3 w-[200px]" />
                        </div>
                        <Skeleton className="h-4 w-[100px]" />
                        <Skeleton className="h-4 w-[80px]" />
                        <Skeleton className="h-4 w-[60px]" />
                        <Skeleton className="h-8 w-8" />
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
            {loadingText && (
              <div className="text-center">
                <p className="text-sm text-slate-600 dark:text-slate-400">{loadingText}</p>
              </div>
            )}
          </div>
        );
    }
  };

  return renderLoadingContent();
};
