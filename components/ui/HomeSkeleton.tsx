'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  animate?: boolean;
}

const Skeleton: React.FC<SkeletonProps> = ({ className, animate = true }) => {
  const baseClasses = "bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded";
  
  if (animate) {
    return (
      <motion.div
        className={cn(baseClasses, className)}
        animate={{
          backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
        }}
        transition={{
          duration: 2,
          ease: 'linear',
          repeat: Infinity,
        }}
        style={{
          backgroundSize: '200% 100%',
        }}
      />
    );
  }

  return <div className={cn(baseClasses, className)} />;
};

const HeroSkeleton: React.FC = () => (
  <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-yellow-100 flex items-center justify-center">
    <div className="container mx-auto px-4">
      <div className="text-center space-y-8">
        {/* Hero Title Skeleton */}
        <div className="space-y-4">
          <Skeleton className="h-12 w-3/4 mx-auto" />
          <Skeleton className="h-12 w-2/3 mx-auto" />
        </div>
        
        {/* Hero Subtitle Skeleton */}
        <div className="space-y-2">
          <Skeleton className="h-6 w-5/6 mx-auto" />
          <Skeleton className="h-6 w-4/6 mx-auto" />
        </div>
        
        {/* CTA Buttons Skeleton */}
        <div className="flex justify-center space-x-4">
          <Skeleton className="h-12 w-32" />
          <Skeleton className="h-12 w-32" />
        </div>
        
        {/* Hero Image Skeleton */}
        <div className="mt-12">
          <Skeleton className="h-64 w-full max-w-2xl mx-auto" />
        </div>
      </div>
    </div>
  </div>
);

const SectionSkeleton: React.FC<{ title?: boolean; cards?: number }> = ({ 
  title = true, 
  cards = 3 
}) => (
  <div className="py-16 bg-white">
    <div className="container mx-auto px-4">
      {title && (
        <div className="text-center mb-12 space-y-4">
          <Skeleton className="h-8 w-64 mx-auto" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>
      )}
      
      <div className={cn(
        "grid gap-8",
        cards === 2 && "grid-cols-1 md:grid-cols-2",
        cards === 3 && "grid-cols-1 md:grid-cols-3",
        cards === 4 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
      )}>
        {Array.from({ length: cards }).map((_, index) => (
          <div key={index} className="space-y-4">
            <Skeleton className="h-48 w-full" />
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
          </div>
        ))}
      </div>
    </div>
  </div>
);

const PlansSkeleton: React.FC = () => (
  <div className="py-16 bg-gray-50">
    <div className="container mx-auto px-4">
      <div className="text-center mb-12 space-y-4">
        <Skeleton className="h-8 w-64 mx-auto" />
        <Skeleton className="h-4 w-96 mx-auto" />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="bg-white rounded-lg p-6 shadow-lg space-y-4">
            <Skeleton className="h-6 w-32 mx-auto" />
            <Skeleton className="h-12 w-24 mx-auto" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
              <Skeleton className="h-4 w-4/6" />
            </div>
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
      </div>
    </div>
  </div>
);

const HomeSkeleton: React.FC = () => (
  <div className="animate-pulse">
    {/* Hero Section Skeleton */}
    <HeroSkeleton />
    
    {/* Welcome Section Skeleton */}
    <SectionSkeleton title={true} cards={1} />
    
    {/* Key Benefits Section Skeleton */}
    <SectionSkeleton title={true} cards={3} />
    
    {/* How It Works Section Skeleton */}
    <SectionSkeleton title={true} cards={4} />
    
    {/* Plans Section Skeleton */}
    <PlansSkeleton />
    
    {/* CTA Section Skeleton */}
    <div className="py-16 bg-yellow-500">
      <div className="container mx-auto px-4 text-center space-y-6">
        <Skeleton className="h-8 w-96 mx-auto bg-yellow-300" />
        <Skeleton className="h-4 w-64 mx-auto bg-yellow-300" />
        <Skeleton className="h-12 w-40 mx-auto bg-yellow-300" />
      </div>
    </div>
  </div>
);

export default HomeSkeleton;
export { HeroSkeleton, SectionSkeleton, PlansSkeleton, Skeleton };
