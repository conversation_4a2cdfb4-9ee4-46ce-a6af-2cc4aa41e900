# Loading Components Documentation

This document provides comprehensive documentation for the modernized loading components system in the Golden Miller application.

## Overview

The loading system consists of several components designed to provide consistent and beautiful loading states across the application:

- **Loading**: Core loading component with multiple variants
- **LoadingWrapper**: Wrapper component for conditional loading states
- **LoadingButton**: Button component with integrated loading states
- **LoadingContext**: Global loading state management
- **HomeSkeleton**: Skeleton loading for the home page
- **usePageLoading**: Hook for page-level loading management

## Components

### 1. Loading Component

The main loading component with multiple variants and customization options.

```tsx
import Loading from '@/components/ui/loading';

// Basic usage
<Loading />

// With customization
<Loading
  variant="spinner"
  size="lg"
  color="primary"
  text="Loading..."
/>
```

**Props:**
- `variant`: 'spinner' | 'dots' | 'pulse' | 'skeleton' | 'page'
- `size`: 'sm' | 'md' | 'lg' | 'xl'
- `color`: 'primary' | 'secondary' | 'accent' | 'white'
- `text`: Optional loading text
- `fullScreen`: Boolean for full-screen loading
- `className`: Additional CSS classes

### 2. LoadingWrapper Component

Wraps content and shows loading state conditionally.

```tsx
import LoadingWrapper from '@/components/ui/LoadingWrapper';

<LoadingWrapper
  isLoading={isLoading}
  loadingText="Loading content..."
>
  <YourContent />
</LoadingWrapper>

// With overlay
<LoadingWrapper
  isLoading={isLoading}
  overlay={true}
  loadingText="Processing..."
>
  <YourContent />
</LoadingWrapper>
```

**Props:**
- `isLoading`: Boolean loading state
- `children`: Content to wrap
- `loadingVariant`: Loading component variant
- `loadingSize`: Loading component size
- `loadingColor`: Loading component color
- `loadingText`: Loading text
- `className`: Additional CSS classes
- `minHeight`: Minimum height for loading area
- `fallback`: Custom loading component
- `overlay`: Boolean for overlay mode

### 3. LoadingButton Component

Button with integrated loading states.

```tsx
import LoadingButton from '@/components/ui/LoadingButton';

<LoadingButton
  isLoading={isSubmitting}
  loadingText="Saving..."
  onClick={handleSubmit}
  icon={<SaveIcon />}
>
  Save Document
</LoadingButton>
```

**Props:**
- `isLoading`: Boolean loading state
- `loadingText`: Text to show when loading
- `children`: Button content
- `variant`: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'
- `size`: 'sm' | 'md' | 'lg'
- `fullWidth`: Boolean for full width
- `icon`: Icon element
- `loadingIcon`: Custom loading icon

### 4. LoadingContext

Global loading state management.

```tsx
import { useLoading } from '@/context/LoadingContext';

function MyComponent() {
  const { showLoading, hideLoading } = useLoading();

  const handleAction = async () => {
    showLoading('Processing...');
    try {
      await someAsyncOperation();
    } finally {
      hideLoading();
    }
  };
}
```

**Methods:**
- `showLoading(text?)`: Show global loading
- `hideLoading()`: Hide global loading
- `setLoadingText(text)`: Update loading text
- `isLoading`: Current loading state
- `loadingText`: Current loading text

### 5. usePageLoading Hook

Hook for page-level loading management.

```tsx
import { usePageLoading } from '@/hooks/usePageLoading';

function MyPage() {
  const { isLoading, startLoading, stopLoading } = usePageLoading({
    initialLoading: true,
    loadingText: 'Loading page...',
    minLoadingTime: 500
  });

  useEffect(() => {
    const loadData = async () => {
      startLoading('Fetching data...');
      await fetchData();
      stopLoading();
    };
    loadData();
  }, []);

  if (isLoading) {
    return <Loading variant="page" text="Loading..." />;
  }

  return <PageContent />;
}
```

### 6. HomeSkeleton Component

Skeleton loading specifically designed for the home page.

```tsx
import HomeSkeleton from '@/components/ui/HomeSkeleton';

// Full home page skeleton
<HomeSkeleton />

// Individual skeleton components
import { HeroSkeleton, SectionSkeleton, PlansSkeleton } from '@/components/ui/HomeSkeleton';

<HeroSkeleton />
<SectionSkeleton title={true} cards={3} />
<PlansSkeleton />
```

## Usage Examples

### Page Loading

```tsx
'use client';

import { useState, useEffect } from 'react';
import HomeSkeleton from '@/components/ui/HomeSkeleton';

export default function HomePage() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setIsLoading(false);
    };
    loadData();
  }, []);

  if (isLoading) {
    return <HomeSkeleton />;
  }

  return <PageContent />;
}
```

### Form Submission

```tsx
'use client';

import { useState } from 'react';
import LoadingButton from '@/components/ui/LoadingButton';

export default function MyForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await submitForm();
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form>
      {/* Form fields */}
      <LoadingButton
        isLoading={isSubmitting}
        loadingText="Submitting..."
        onClick={handleSubmit}
        variant="primary"
        fullWidth
      >
        Submit Form
      </LoadingButton>
    </form>
  );
}
```

### Data Fetching

```tsx
'use client';

import { useState, useEffect } from 'react';
import LoadingWrapper from '@/components/ui/LoadingWrapper';

export default function DataComponent() {
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const result = await api.getData();
        setData(result);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, []);

  return (
    <LoadingWrapper
      isLoading={isLoading}
      loadingText="Fetching data..."
      loadingVariant="skeleton"
    >
      {data && <DataDisplay data={data} />}
    </LoadingWrapper>
  );
}
```

### Global Loading

```tsx
'use client';

import { useLoading } from '@/context/LoadingContext';

export default function ActionComponent() {
  const { showLoading, hideLoading } = useLoading();

  const handleGlobalAction = async () => {
    showLoading('Processing your request...');
    try {
      await performGlobalAction();
    } finally {
      hideLoading();
    }
  };

  return (
    <button onClick={handleGlobalAction}>
      Perform Action
    </button>
  );
}
```

## Best Practices

1. **Use appropriate variants**: Choose the right loading variant for your use case
2. **Provide meaningful text**: Always include descriptive loading text
3. **Minimum loading time**: Use minimum loading times to prevent flashing
4. **Consistent styling**: Use the same color scheme across your application
5. **Accessibility**: Ensure loading states are accessible to screen readers
6. **Performance**: Use skeleton loading for better perceived performance

## Customization

All components support custom styling through the `className` prop and can be themed using Tailwind CSS classes. The color variants can be customized by modifying the component styles.

## Integration

The loading system is already integrated into:
- Main application layout
- Authentication context
- Home page
- Dashboard components

To add loading to new components, simply import and use the appropriate loading component based on your needs.
