'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  animate?: boolean;
}

const Skeleton: React.FC<SkeletonProps> = ({ className, animate = true }) => {
  const baseClasses = "bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded";
  
  if (animate) {
    return (
      <motion.div
        className={cn(baseClasses, className)}
        animate={{
          backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
        }}
        transition={{
          duration: 2,
          ease: 'linear',
          repeat: Infinity,
        }}
        style={{
          backgroundSize: '200% 100%',
        }}
      />
    );
  }

  return <div className={cn(baseClasses, className)} />;
};

// Admin Dashboard Stats Skeleton
const AdminStatsSkeleton: React.FC = () => (
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
    {Array.from({ length: 4 }).map((_, index) => (
      <div key={index} className="bg-white rounded-lg p-6 shadow-sm space-y-3">
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-3 w-24" />
      </div>
    ))}
  </div>
);

// Admin Table Skeleton
const AdminTableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 10, 
  columns = 6 
}) => (
  <div className="bg-white rounded-lg shadow-sm overflow-hidden">
    {/* Table Header */}
    <div className="border-b border-gray-200 bg-gray-50 px-6 py-3">
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-32" />
        <div className="flex space-x-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
        </div>
      </div>
    </div>
    
    {/* Search and Filters */}
    <div className="p-4 border-b border-gray-200 space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-10 w-64" />
        <div className="flex space-x-2">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
    </div>
    
    {/* Table Content */}
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {Array.from({ length: columns }).map((_, index) => (
              <th key={index} className="px-6 py-3">
                <Skeleton className="h-4 w-20" />
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <tr key={rowIndex}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <td key={colIndex} className="px-6 py-4">
                  <Skeleton className="h-4 w-full" />
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
    
    {/* Pagination */}
    <div className="px-6 py-3 border-t border-gray-200">
      <div className="flex items-center justify-between">
        <Skeleton className="h-4 w-32" />
        <div className="flex space-x-2">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </div>
    </div>
  </div>
);

// Admin Form Skeleton
const AdminFormSkeleton: React.FC = () => (
  <div className="bg-white rounded-lg shadow-sm p-6 space-y-6">
    <div className="space-y-2">
      <Skeleton className="h-6 w-32" />
      <Skeleton className="h-4 w-64" />
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="space-y-2">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-10 w-full" />
        </div>
      ))}
    </div>
    
    <div className="flex justify-end space-x-3">
      <Skeleton className="h-10 w-20" />
      <Skeleton className="h-10 w-24" />
    </div>
  </div>
);

// Admin Charts Skeleton
const AdminChartsSkeleton: React.FC = () => (
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    {Array.from({ length: 2 }).map((_, index) => (
      <div key={index} className="bg-white rounded-lg shadow-sm p-6 space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-8 w-20" />
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    ))}
  </div>
);

// Complete Admin Dashboard Skeleton
const AdminDashboardSkeleton: React.FC = () => (
  <div className="space-y-6">
    {/* Page Header */}
    <div className="space-y-2">
      <Skeleton className="h-8 w-48" />
      <Skeleton className="h-4 w-96" />
    </div>
    
    {/* Stats Cards */}
    <AdminStatsSkeleton />
    
    {/* Charts */}
    <AdminChartsSkeleton />
    
    {/* Main Table */}
    <AdminTableSkeleton />
  </div>
);

// Admin Page Skeleton (Generic)
const AdminPageSkeleton: React.FC<{ 
  showStats?: boolean;
  showCharts?: boolean;
  showForm?: boolean;
  tableRows?: number;
}> = ({ 
  showStats = true, 
  showCharts = false, 
  showForm = false,
  tableRows = 10 
}) => (
  <div className="space-y-6">
    {/* Page Header */}
    <div className="space-y-2">
      <Skeleton className="h-8 w-48" />
      <Skeleton className="h-4 w-96" />
    </div>
    
    {/* Stats Cards */}
    {showStats && <AdminStatsSkeleton />}
    
    {/* Charts */}
    {showCharts && <AdminChartsSkeleton />}
    
    {/* Form */}
    {showForm && <AdminFormSkeleton />}
    
    {/* Main Table */}
    <AdminTableSkeleton rows={tableRows} />
  </div>
);

// Investment Management Skeleton
const InvestmentManagementSkeleton: React.FC = () => (
  <AdminPageSkeleton 
    showStats={true}
    showCharts={true}
    showForm={false}
    tableRows={15}
  />
);

// User Management Skeleton
const UserManagementSkeleton: React.FC = () => (
  <AdminPageSkeleton 
    showStats={true}
    showCharts={false}
    showForm={false}
    tableRows={20}
  />
);

// Plan Management Skeleton
const PlanManagementSkeleton: React.FC = () => (
  <AdminPageSkeleton 
    showStats={false}
    showCharts={false}
    showForm={true}
    tableRows={8}
  />
);

export default AdminDashboardSkeleton;
export { 
  AdminStatsSkeleton,
  AdminTableSkeleton,
  AdminFormSkeleton,
  AdminChartsSkeleton,
  AdminPageSkeleton,
  InvestmentManagementSkeleton,
  UserManagementSkeleton,
  PlanManagementSkeleton,
  Skeleton
};
