'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  animate?: boolean;
}

const Skeleton: React.FC<SkeletonProps> = ({ className, animate = true }) => {
  const baseClasses = "bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded";
  
  if (animate) {
    return (
      <motion.div
        className={cn(baseClasses, className)}
        animate={{
          backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
        }}
        transition={{
          duration: 2,
          ease: 'linear',
          repeat: Infinity,
        }}
        style={{
          backgroundSize: '200% 100%',
        }}
      />
    );
  }

  return <div className={cn(baseClasses, className)} />;
};

// Dashboard Stats Skeleton
const DashboardStatsSkeleton: React.FC = () => (
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
    {Array.from({ length: 4 }).map((_, index) => (
      <div key={index} className="bg-white rounded-lg p-6 shadow-sm space-y-3">
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
        <Skeleton className="h-8 w-20" />
        <Skeleton className="h-3 w-32" />
      </div>
    ))}
  </div>
);

// Investment Table Skeleton
const InvestmentTableSkeleton: React.FC = () => (
  <div className="bg-white rounded-lg shadow-sm overflow-hidden">
    {/* Header */}
    <div className="p-6 border-b border-gray-200">
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-10 w-64" />
      </div>
    </div>
    
    {/* Table */}
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {Array.from({ length: 8 }).map((_, index) => (
              <th key={index} className="px-6 py-3">
                <Skeleton className="h-4 w-20" />
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {Array.from({ length: 8 }).map((_, rowIndex) => (
            <tr key={rowIndex}>
              {Array.from({ length: 8 }).map((_, colIndex) => (
                <td key={colIndex} className="px-6 py-4">
                  <Skeleton className="h-4 w-full" />
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
    
    {/* Pagination */}
    <div className="px-6 py-3 border-t border-gray-200">
      <div className="flex items-center justify-between">
        <Skeleton className="h-4 w-32" />
        <div className="flex space-x-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-16" />
        </div>
      </div>
    </div>
  </div>
);

// Payment Schedule Skeleton
const PaymentScheduleSkeleton: React.FC = () => (
  <div className="bg-white rounded-lg shadow-sm p-6 space-y-4">
    <div className="flex items-center justify-between">
      <Skeleton className="h-6 w-40" />
      <Skeleton className="h-8 w-24" />
    </div>
    
    <div className="space-y-3">
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-3 w-24" />
          </div>
          <div className="text-right space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-6 w-16" />
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Referral Network Skeleton
const ReferralNetworkSkeleton: React.FC = () => (
  <div className="bg-white rounded-lg shadow-sm p-6 space-y-6">
    <div className="flex items-center justify-between">
      <Skeleton className="h-6 w-36" />
      <Skeleton className="h-8 w-32" />
    </div>
    
    {/* Referral Tree */}
    <div className="space-y-4">
      {Array.from({ length: 4 }).map((level, levelIndex) => (
        <div key={levelIndex} className="space-y-2">
          <Skeleton className="h-4 w-20" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {Array.from({ length: Math.pow(2, levelIndex) }).map((_, index) => (
              <div key={index} className="border rounded-lg p-3 space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-32" />
                <Skeleton className="h-3 w-16" />
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Profile Section Skeleton
const ProfileSkeleton: React.FC = () => (
  <div className="bg-white rounded-lg shadow-sm p-6 space-y-4">
    <div className="flex items-center space-x-4">
      <Skeleton className="h-16 w-16 rounded-full" />
      <div className="space-y-2">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-4 w-48" />
      </div>
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="space-y-2">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-8 w-full" />
        </div>
      ))}
    </div>
    
    <div className="flex justify-end space-x-3">
      <Skeleton className="h-10 w-20" />
      <Skeleton className="h-10 w-24" />
    </div>
  </div>
);

// Complete Dashboard Skeleton
const DashboardSkeleton: React.FC = () => (
  <div className="space-y-6">
    {/* Welcome Section */}
    <div className="bg-white p-6 rounded-lg shadow-md space-y-2">
      <Skeleton className="h-8 w-64" />
      <Skeleton className="h-4 w-96" />
    </div>
    
    {/* Stats */}
    <DashboardStatsSkeleton />
    
    {/* Main Content */}
    <InvestmentTableSkeleton />
  </div>
);

// Monthly Payment Page Skeleton
const MonthlyPaymentSkeleton: React.FC = () => (
  <div className="space-y-6">
    {/* Header */}
    <div className="bg-white p-6 rounded-lg shadow-md space-y-2">
      <Skeleton className="h-8 w-48" />
      <Skeleton className="h-4 w-80" />
    </div>
    
    {/* Payment Schedule */}
    <PaymentScheduleSkeleton />
  </div>
);

// Referrals Page Skeleton
const ReferralsSkeleton: React.FC = () => (
  <div className="space-y-6">
    {/* Header */}
    <div className="bg-white p-6 rounded-lg shadow-md space-y-2">
      <Skeleton className="h-8 w-40" />
      <Skeleton className="h-4 w-72" />
    </div>
    
    {/* Referral Network */}
    <ReferralNetworkSkeleton />
  </div>
);

// Payout Page Skeleton
const PayoutSkeleton: React.FC = () => (
  <div className="space-y-6">
    {/* Header */}
    <div className="bg-white p-6 rounded-lg shadow-md space-y-2">
      <Skeleton className="h-8 w-32" />
      <Skeleton className="h-4 w-64" />
    </div>
    
    {/* Payout Table */}
    <InvestmentTableSkeleton />
  </div>
);

export default DashboardSkeleton;
export { 
  DashboardStatsSkeleton,
  InvestmentTableSkeleton,
  PaymentScheduleSkeleton,
  ReferralNetworkSkeleton,
  ProfileSkeleton,
  MonthlyPaymentSkeleton,
  ReferralsSkeleton,
  PayoutSkeleton,
  Skeleton
};
