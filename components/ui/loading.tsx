'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

// Loading Spinner Variants
const spinnerVariants = {
  start: {
    opacity: 1,
  },
  end: {
    opacity: 0,
  },
};



// Pulse Animation
const pulseVariants = {
  start: {
    scale: 0,
    opacity: 0.7,
  },
  end: {
    scale: 1,
    opacity: 0,
  },
};

// Dots Animation
const dotsVariants = {
  start: {
    y: "0%",
  },
  end: {
    y: "100%",
  },
};

// Loading Component Props
interface LoadingProps {
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton' | 'page';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'accent' | 'white';
  text?: string;
  fullScreen?: boolean;
  className?: string;
}

// Spinner Loading
const SpinnerLoading: React.FC<{ size: string; color: string }> = ({ size, color }) => (
  <motion.div
    className={cn(
      "relative inline-block",
      size === 'sm' && "w-4 h-4",
      size === 'md' && "w-8 h-8",
      size === 'lg' && "w-12 h-12",
      size === 'xl' && "w-16 h-16"
    )}
    variants={spinnerVariants}
    initial="start"
    animate="end"
    transition={{
      duration: 1,
      ease: "easeInOut",
      times: [0, 1],
      repeat: Infinity,
      repeatDelay: 1
    }}
  >
    <motion.span
      className={cn(
        "absolute inset-0 rounded-full border-2 border-transparent",
        color === 'primary' && "border-t-yellow-500 border-r-yellow-500",
        color === 'secondary' && "border-t-gray-500 border-r-gray-500",
        color === 'accent' && "border-t-blue-500 border-r-blue-500",
        color === 'white' && "border-t-white border-r-white"
      )}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        ease: "linear",
        repeat: Infinity,
      }}
    />
  </motion.div>
);

// Dots Loading
const DotsLoading: React.FC<{ size: string; color: string }> = ({ size, color }) => (
  <div className="flex space-x-1">
    {[0, 1, 2].map((index) => (
      <motion.div
        key={index}
        className={cn(
          "rounded-full",
          size === 'sm' && "w-1 h-1",
          size === 'md' && "w-2 h-2",
          size === 'lg' && "w-3 h-3",
          size === 'xl' && "w-4 h-4",
          color === 'primary' && "bg-yellow-500",
          color === 'secondary' && "bg-gray-500",
          color === 'accent' && "bg-blue-500",
          color === 'white' && "bg-white"
        )}
        variants={dotsVariants}
        initial="start"
        animate="end"
        transition={{
          duration: 0.5,
          ease: "easeInOut",
          repeat: Infinity,
          repeatType: "reverse",
          delay: index * 0.1,
        }}
      />
    ))}
  </div>
);

// Pulse Loading
const PulseLoading: React.FC<{ size: string; color: string }> = ({ size, color }) => (
  <div className="relative flex items-center justify-center">
    {[0, 1, 2].map((index) => (
      <motion.div
        key={index}
        className={cn(
          "absolute rounded-full",
          size === 'sm' && "w-8 h-8",
          size === 'md' && "w-12 h-12",
          size === 'lg' && "w-16 h-16",
          size === 'xl' && "w-20 h-20",
          color === 'primary' && "bg-yellow-500",
          color === 'secondary' && "bg-gray-500",
          color === 'accent' && "bg-blue-500",
          color === 'white' && "bg-white"
        )}
        variants={pulseVariants}
        initial="start"
        animate="end"
        transition={{
          duration: 1.5,
          ease: "easeOut",
          repeat: Infinity,
          delay: index * 0.2,
        }}
      />
    ))}
  </div>
);

// Skeleton Loading
const SkeletonLoading: React.FC<{ size: string }> = ({ size }) => (
  <div className="animate-pulse space-y-3">
    <div className={cn(
      "bg-gray-300 rounded",
      size === 'sm' && "h-4",
      size === 'md' && "h-6",
      size === 'lg' && "h-8",
      size === 'xl' && "h-10"
    )} />
    <div className={cn(
      "bg-gray-300 rounded",
      size === 'sm' && "h-4 w-3/4",
      size === 'md' && "h-6 w-3/4",
      size === 'lg' && "h-8 w-3/4",
      size === 'xl' && "h-10 w-3/4"
    )} />
    <div className={cn(
      "bg-gray-300 rounded",
      size === 'sm' && "h-4 w-1/2",
      size === 'md' && "h-6 w-1/2",
      size === 'lg' && "h-8 w-1/2",
      size === 'xl' && "h-10 w-1/2"
    )} />
  </div>
);

// Page Loading (Full Screen)
const PageLoading: React.FC<{ color: string; text?: string }> = ({ color, text }) => (
  <motion.div
    className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex flex-col items-center justify-center"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
  >
    <div className="text-center space-y-4">
      {/* Golden Miller Logo Animation */}
      <motion.div
        className="w-16 h-16 mx-auto mb-4"
        animate={{ 
          scale: [1, 1.1, 1],
          rotate: [0, 5, -5, 0]
        }}
        transition={{
          duration: 2,
          ease: "easeInOut",
          repeat: Infinity,
        }}
      >
        <div className={cn(
          "w-full h-full rounded-full flex items-center justify-center text-white font-bold text-xl",
          color === 'primary' && "bg-gradient-to-br from-yellow-400 to-yellow-600",
          color === 'secondary' && "bg-gradient-to-br from-gray-400 to-gray-600",
          color === 'accent' && "bg-gradient-to-br from-blue-400 to-blue-600",
          color === 'white' && "bg-gradient-to-br from-gray-100 to-gray-300 text-gray-800"
        )}>
          GM
        </div>
      </motion.div>

      {/* Loading Spinner */}
      <SpinnerLoading size="lg" color={color} />
      
      {/* Loading Text */}
      {text && (
        <motion.p
          className="text-gray-600 font-medium"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{
            duration: 1.5,
            ease: "easeInOut",
            repeat: Infinity,
          }}
        >
          {text}
        </motion.p>
      )}
      
      {/* Loading Dots */}
      <div className="flex space-x-1 justify-center">
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            className={cn(
              "w-2 h-2 rounded-full",
              color === 'primary' && "bg-yellow-500",
              color === 'secondary' && "bg-gray-500",
              color === 'accent' && "bg-blue-500",
              color === 'white' && "bg-gray-400"
            )}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.3, 1, 0.3],
            }}
            transition={{
              duration: 1,
              ease: "easeInOut",
              repeat: Infinity,
              delay: index * 0.2,
            }}
          />
        ))}
      </div>
    </div>
  </motion.div>
);

// Main Loading Component
const Loading: React.FC<LoadingProps> = ({
  variant = 'spinner',
  size = 'md',
  color = 'primary',
  text,
  fullScreen = false,
  className,
}) => {
  const renderLoading = () => {
    switch (variant) {
      case 'dots':
        return <DotsLoading size={size} color={color} />;
      case 'pulse':
        return <PulseLoading size={size} color={color} />;
      case 'skeleton':
        return <SkeletonLoading size={size} />;
      case 'page':
        return <PageLoading color={color} text={text} />;
      default:
        return <SpinnerLoading size={size} color={color} />;
    }
  };

  if (variant === 'page' || fullScreen) {
    return <PageLoading color={color} text={text} />;
  }

  return (
    <div className={cn("flex flex-col items-center justify-center space-y-2", className)}>
      {renderLoading()}
      {text && variant !== 'page' && (
        <motion.p
          className="text-sm text-gray-600 font-medium"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{
            duration: 1.5,
            ease: "easeInOut",
            repeat: Infinity,
          }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );
};

export default Loading;
