'use client';

import React, { ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Loading from './loading';
import { cn } from '@/lib/utils';

interface LoadingWrapperProps {
  isLoading: boolean;
  children: ReactNode;
  loadingVariant?: 'spinner' | 'dots' | 'pulse' | 'skeleton' | 'page';
  loadingSize?: 'sm' | 'md' | 'lg' | 'xl';
  loadingColor?: 'primary' | 'secondary' | 'accent' | 'white';
  loadingText?: string;
  className?: string;
  minHeight?: string;
  fallback?: ReactNode;
  overlay?: boolean;
}

const LoadingWrapper: React.FC<LoadingWrapperProps> = ({
  isLoading,
  children,
  loadingVariant = 'spinner',
  loadingSize = 'md',
  loadingColor = 'primary',
  loadingText,
  className,
  minHeight = 'min-h-[200px]',
  fallback,
  overlay = false,
}) => {
  if (overlay) {
    return (
      <div className={cn("relative", className)}>
        {children}
        <AnimatePresence>
          {isLoading && (
            <motion.div
              className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-10"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <Loading
                variant={loadingVariant}
                size={loadingSize}
                color={loadingColor}
                text={loadingText}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  return (
    <div className={cn(className)}>
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="loading"
            className={cn("flex items-center justify-center", minHeight)}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            {fallback || (
              <Loading
                variant={loadingVariant}
                size={loadingSize}
                color={loadingColor}
                text={loadingText}
              />
            )}
          </motion.div>
        ) : (
          <motion.div
            key="content"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LoadingWrapper;
