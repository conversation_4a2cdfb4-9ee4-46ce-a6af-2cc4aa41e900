'use client';

import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import Loading from './loading';

interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  loadingText?: string;
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  icon?: ReactNode;
  loadingIcon?: ReactNode;
}

const LoadingButton: React.FC<LoadingButtonProps> = ({
  isLoading = false,
  loadingText,
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  icon,
  loadingIcon,
  className,
  disabled,
  ...props
}) => {
  const baseClasses = cn(
    "relative inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",
    // Variants
    variant === 'primary' && "bg-yellow-500 hover:bg-yellow-600 text-white focus:ring-yellow-500",
    variant === 'secondary' && "bg-gray-500 hover:bg-gray-600 text-white focus:ring-gray-500",
    variant === 'outline' && "border-2 border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-white focus:ring-yellow-500",
    variant === 'ghost' && "text-yellow-500 hover:bg-yellow-50 focus:ring-yellow-500",
    variant === 'destructive' && "bg-red-500 hover:bg-red-600 text-white focus:ring-red-500",
    // Sizes
    size === 'sm' && "px-3 py-1.5 text-sm",
    size === 'md' && "px-4 py-2 text-base",
    size === 'lg' && "px-6 py-3 text-lg",
    // Full width
    fullWidth && "w-full",
    className
  );

  const isDisabled = disabled || isLoading;

  return (
    <motion.button
      className={baseClasses}
      disabled={isDisabled}
      whileHover={!isDisabled ? { scale: 1.02 } : {}}
      whileTap={!isDisabled ? { scale: 0.98 } : {}}
      transition={{ duration: 0.1 }}
      {...props}
    >
      {/* Loading State */}
      {isLoading && (
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          <div className="flex items-center space-x-2">
            {loadingIcon || (
              <Loading
                variant="spinner"
                size="sm"
                color={variant === 'outline' || variant === 'ghost' ? 'primary' : 'white'}
              />
            )}
            {loadingText && (
              <span className="text-sm">{loadingText}</span>
            )}
          </div>
        </motion.div>
      )}

      {/* Normal State */}
      <motion.div
        className="flex items-center space-x-2"
        animate={{ opacity: isLoading ? 0 : 1 }}
        transition={{ duration: 0.2 }}
      >
        {icon && !isLoading && (
          <span className="flex-shrink-0">{icon}</span>
        )}
        <span>{children}</span>
      </motion.div>
    </motion.button>
  );
};

export default LoadingButton;
