

'use client';

import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';

const TopNavbar: React.FC = () => {
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
      // Handle logout error (e.g., show an error message to the user)
    }
  };

  return (
    <header className="bg-white shadow-sm flex items-center justify-between px-6 py-4">
      <div>
        {/* Left-side content (e.g., breadcrumbs or page title) */}
      </div>
      <div className="flex items-center space-x-4">
        <span className="text-gray-800 font-medium">Hello, {user?.name}</span>
        <button
          onClick={handleLogout}
          className="px-3 py-1 rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors duration-200"
        >
          Logout
        </button>
      </div>
    </header>
  );
};

export default TopNavbar;

