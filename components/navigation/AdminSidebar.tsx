


// // components/admin/AdminSidebar.tsx
// 'use client';

// import React from 'react';
// import { usePathname, useRouter } from 'next/navigation';
// import { motion } from 'framer-motion';
// import { useAuth } from '@/context/AuthContext';
// import {
//   FaTachometerAlt,
//   FaUsers,
//   FaChartLine,
//   FaUserFriends,
//   FaMoneyCheckAlt,
//   FaFileAlt,
//   FaSignOutAlt,
// } from 'react-icons/fa';

// const sidebarItems = [
//   { name: 'Dashboard', href: '/admin', icon: <FaTachometerAlt /> },
//   { name: 'All Investors', href: '/admin/all-investors', icon: <FaUsers /> },
//   { name: 'All Subscriptions', href: '/admin/all-subscriptions', icon: <FaChartLine /> },
//   { name: 'All Referrals', href: '/admin/all-referrals', icon: <FaUserFriends /> },
//   { name: 'Payouts', href: '/admin/payouts', icon: <FaMoneyCheckAlt /> },
//   {name: 'Plans', href: '/admin/plans', icon: <FaFileAlt /> },
//   { name: 'Pages', href: '/admin/pages', icon: <FaFileAlt /> },
// ];

// const AdminSidebar: React.FC = () => {
//   const router = useRouter();
//   const pathname = usePathname();
//   const { user, logout } = useAuth();

//   const handleNavigation = (href: string) => {
//     router.push(href);
//   };

//   return (
//     <div className="w-64 h-screen bg-white dark:bg-gray-900 shadow-md flex flex-col justify-between transition-colors">
//       {/* Admin Profile Section */}
//       <div className="p-6 border-b border-gray-200 dark:border-gray-700">
//         <div className="flex items-center space-x-4">
//           <img
//             src="/logo1.png"
//             alt="Admin Avatar"
//             className="w-12 h-12 rounded-full object-cover"
//           />
//           <div>
//             <p className="text-lg font-semibold text-gray-800 dark:text-gray-200">{user?.name ?? 'Admin'}</p>
//             <p className="text-sm text-gray-500 dark:text-gray-400">Administrator</p>
//           </div>
//         </div>
//       </div>

//       {/* Navigation Links */}
//       <nav className="mt-4 flex-grow overflow-y-auto">
//         <ul>
//           {sidebarItems.map((item) => (
//             <li key={item.name}>
//               <motion.button
//                 onClick={() => handleNavigation(item.href)}
//                 className={`flex items-center w-full text-left px-6 py-3 mb-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none transition-colors duration-200 ${
//                   pathname === item.href ? 'bg-yellow-500 text-white' : ''
//                 }`}
//                 whileHover={{ scale: 1.02 }}
//                 whileTap={{ scale: 0.98 }}
//               >
//                 <span className="text-xl mr-3">{item.icon}</span>
//                 <span className="font-medium">{item.name}</span>
//               </motion.button>
//             </li>
//           ))}
//         </ul>
//       </nav>

//       {/* Logout Section */}
//       <div className="p-6 border-t border-gray-200 dark:border-gray-700">
//         <button
//           onClick={logout}
//           className="flex items-center text-gray-700 dark:text-gray-300 hover:text-red-600 transition-colors duration-200"
//         >
//           <FaSignOutAlt className="mr-3 text-xl" />
//           <span className="font-medium">Logout</span>
//         </button>
//       </div>
//     </div>
//   );
// };

// export default AdminSidebar;

'use client';

import React from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import {
  LayoutDashboard,
  Users,
  TrendingUp,
  UserCheck,
  CreditCard,
  FileText,
  LogOut,
  X,
  Settings,
  BarChart3,
  Wallet,
  Shield,
  ChevronRight,
} from 'lucide-react';

const sidebarItems = [
  {
    name: 'Dashboard',
    href: '/admin',
    icon: LayoutDashboard,
    description: 'Overview & Analytics',
    badge: null
  },
  {
    name: 'Members',
    href: '/admin/all-investors',
    icon: Users,
    description: 'Manage all members',
    badge: 'new'
  },
  {
    name: 'Investments',
    href: '/admin/all-investments',
    icon: TrendingUp,
    description: 'Investment tracking',
    badge: null
  },
  {
    name: 'Referrals',
    href: '/admin/all-referrals',
    icon: UserCheck,
    description: 'Referral management',
    badge: null
  },
  {
    name: 'Payouts',
    href: '/admin/payouts',
    icon: Wallet,
    description: 'Payment processing',
    badge: null
  },
  {
    name: 'Plans',
    href: '/admin/plans',
    icon: CreditCard,
    description: 'Investment plans',
    badge: null
  },
  {
    name: 'Analytics',
    href: '/admin/analytics',
    icon: BarChart3,
    description: 'Reports & insights',
    badge: null
  },
  {
    name: 'Settings',
    href: '/admin/settings',
    icon: Settings,
    description: 'System configuration',
    badge: null
  },
];

interface AdminSidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({ isOpen, toggleSidebar }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const handleNavigation = (href: string) => {
    router.push(href);
    if (window.innerWidth < 768) {
      toggleSidebar();
    }
  };

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={toggleSidebar}
        />
      )}

      <div
        className={`fixed inset-y-0 left-0 z-40 w-72 bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 shadow-2xl flex flex-col transition-all duration-300 ease-in-out transform ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } md:translate-x-0`}
      >
        {/* Close button for mobile */}
        <button
          className="md:hidden absolute top-6 right-6 text-slate-400 hover:text-white transition-colors z-50"
          onClick={toggleSidebar}
        >
          <X className="h-6 w-6" />
        </button>

        {/* Header Section */}
        <div className="p-6 border-b border-slate-700/50">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-slate-900"></div>
            </div>
            <div>
              <p className="text-lg font-bold text-white">{user?.name ?? 'Admin'}</p>
              <p className="text-sm text-slate-400 flex items-center">
                <Shield className="h-3 w-3 mr-1" />
                Administrator
              </p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="mt-4 grid grid-cols-2 gap-3">
            <div className="bg-slate-800/50 rounded-lg p-3 border border-slate-700/30">
              <div className="text-xs text-slate-400">Active Users</div>
              <div className="text-lg font-bold text-white">1,247</div>
            </div>
            <div className="bg-slate-800/50 rounded-lg p-3 border border-slate-700/30">
              <div className="text-xs text-slate-400">Revenue</div>
              <div className="text-lg font-bold text-green-400">$89.2K</div>
            </div>
          </div>
        </div>

      {/* Navigation Links */}
      <nav className="flex-grow overflow-y-auto px-4 py-6">
        <div className="space-y-2">
          {sidebarItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;

            return (
              <motion.button
                key={item.name}
                onClick={() => handleNavigation(item.href)}
                className={`group relative flex items-center w-full text-left px-4 py-3 rounded-xl transition-all duration-200 ${
                  isActive
                    ? 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white shadow-lg shadow-yellow-500/25'
                    : 'text-slate-300 hover:bg-slate-800/50 hover:text-white'
                }`}
                whileHover={{ scale: 1.02, x: 4 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className={`flex items-center justify-center w-10 h-10 rounded-lg mr-3 transition-colors ${
                  isActive
                    ? 'bg-white/20'
                    : 'bg-slate-700/50 group-hover:bg-slate-600/50'
                }`}>
                  <Icon className="h-5 w-5" />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <span className="font-medium truncate">{item.name}</span>
                    {item.badge && (
                      <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-blue-500 text-white rounded-full">
                        {item.badge}
                      </span>
                    )}
                    {isActive && (
                      <ChevronRight className="h-4 w-4 ml-2 opacity-70" />
                    )}
                  </div>
                  <p className={`text-xs truncate mt-0.5 ${
                    isActive ? 'text-white/80' : 'text-slate-500 group-hover:text-slate-400'
                  }`}>
                    {item.description}
                  </p>
                </div>

                {/* Active indicator */}
                {isActive && (
                  <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-r-full" />
                )}
              </motion.button>
            );
          })}
        </div>
      </nav>

      {/* Footer Section */}
      <div className="p-4 border-t border-slate-700/50">
        <motion.button
          onClick={logout}
          className="group flex items-center w-full text-left px-4 py-3 rounded-xl text-slate-300 hover:bg-red-500/10 hover:text-red-400 transition-all duration-200"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="flex items-center justify-center w-10 h-10 rounded-lg mr-3 bg-slate-700/50 group-hover:bg-red-500/20 transition-colors">
            <LogOut className="h-5 w-5" />
          </div>
          <div>
            <span className="font-medium">Sign Out</span>
            <p className="text-xs text-slate-500 group-hover:text-red-400/70">
              Logout from admin panel
            </p>
          </div>
        </motion.button>

        {/* Version info */}
        <div className="mt-4 px-4 py-2 bg-slate-800/30 rounded-lg border border-slate-700/30">
          <div className="text-xs text-slate-500">Golden Miller Admin</div>
          <div className="text-xs text-slate-400">v2.1.0</div>
        </div>
      </div>
    </div>
    </>
  );
};

export default AdminSidebar;

