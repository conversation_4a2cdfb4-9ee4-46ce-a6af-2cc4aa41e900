

// components/dashboard/Sidebar.tsx


'use client';

import React, { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import {
  FaTachometerAlt,
  FaClipboardList,
  FaHistory,
  FaMoneyBillWave,
  FaWallet,
  FaGift,
  FaUserFriends,
  FaTools,
  FaUserCog,
  FaSignOutAlt,
  FaPlusSquare,
  FaBars,
} from 'react-icons/fa';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const sidebarItems = [
  { name: 'Dashboard', href: '/dashboard', icon: <FaTachometerAlt /> },
  { name: 'Plan', href: '/dashboard/plan', icon: <FaClipboardList /> },
  { name: 'Investment History', href: '/dashboard/investment-history', icon: <FaHistory /> },
  { name: 'Add Funds', href: '/dashboard/add-funds', icon: <FaPlusSquare /> },
  { name: 'Fund History', href: '/dashboard/fund-history', icon: <FaWallet /> },
  { name: 'MonthlyPayment', href: '/dashboard/monthly-payment', icon: <FaMoneyBillWave /> },
  { name: 'Payout', href: '/dashboard/payout', icon: <FaMoneyBillWave /> },
  { name: 'My Referrals', href: '/dashboard/my-referrals', icon: <FaUserFriends /> },
  { name: 'Referral Bonuses', href: '/dashboard/referral-bonuses', icon: <FaGift /> },
  { name: 'Support', href: '/dashboard/support', icon: <FaTools /> },
  { name: 'Profile Settings', href: '/dashboard/profile-settings', icon: <FaUserCog /> },
];

const Sidebar: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const handleNavigation = (href: string) => {
    router.push(href);
    setIsSidebarOpen(false);
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <TooltipProvider>
      <button
        onClick={toggleSidebar}
        className="fixed top-4 left-4 z-50 p-2 bg-yellow-500 text-white rounded-md md:hidden"
        aria-label="Toggle Sidebar"
      >
        <FaBars />
      </button>
      <div
        className={`fixed inset-y-0 left-0 z-40 w-64 md:w-20 lg:w-64 bg-white dark:bg-gray-900 shadow-md flex flex-col justify-between transition-all duration-300 ease-in-out ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
        }`}
      >
        {/* User Profile Section */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-4">
            <img
              src="/default-avatar.png"
              alt="User Avatar"
              className="w-12 h-12 rounded-full object-cover"
            />
            <div className="hidden md:hidden lg:block">
              <p className="text-lg font-semibold text-gray-800 dark:text-gray-200">{user?.name ?? 'User'}</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">Investor</p>
            </div>
          </div>
        </div>

        {/* Navigation Links */}
        <nav className="mt-4 flex-grow overflow-y-auto">
          <ul>
            {sidebarItems.map((item) => (
              <li key={item.name}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.button
                      onClick={() => handleNavigation(item.href)}
                      className={`flex items-center w-full text-left px-6 py-3 mb-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none transition-colors duration-200 ${
                        pathname === item.href ? 'bg-yellow-500 text-white' : ''
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="text-xl mr-3">{item.icon}</span>
                      <span className="font-medium hidden md:hidden lg:inline">{item.name}</span>
                    </motion.button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    {item.name}
                  </TooltipContent>
                </Tooltip>
              </li>
            ))}
          </ul>
        </nav>

        {/* Logout Section */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                onClick={logout}
                className="flex items-center w-full text-gray-700 dark:text-gray-300 hover:text-red-600 transition-colors duration-200"
              >
                <FaSignOutAlt className="mr-3 text-xl" />
                <span className="font-medium hidden md:hidden lg:inline">Logout</span>
              </button>
            </TooltipTrigger>
            <TooltipContent side="right">
              Logout
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default Sidebar;

