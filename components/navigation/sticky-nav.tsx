
// components/navigation/StickyNav.tsx

'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Menu,
  X,
  Home,
  Smartphone,
  Info,
  PiggyBank,
  HelpCircle,
  Mail,
  LogIn,
  UserPlus,
  LogOut,
  LayoutDashboard,
  Shield,
  TrendingUp,
  Sparkles,
  ChevronDown,
  User
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import UserActionsOverlay from '@/components/UserActionsOverlay';
import UserLogin from '@/components/UserLogin';
import UserSignup from '@/components/UserSignup';
import ThemeSwitcher from '@/components/ThemeSwitcher';

export default function StickyNav() {
  const [isSticky, setIsSticky] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [authAction, setAuthAction] = useState<'login' | 'signup' | null>(null);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  const pathname = usePathname();
  const router = useRouter();
  const { user, logout, loading } = useAuth();

  const navItems = [
    { name: 'Home', href: '/', icon: Home, description: 'Welcome to TGM Finance' },
    { name: 'Investment Solutions', href: '/plans', icon: TrendingUp, description: 'Premium investment portfolios' },
    { name: 'How It Works', href: '/how-it-works', icon: HelpCircle, description: 'Simple 4-step process' },
    { name: 'About TGM', href: '/about', icon: Info, description: 'Our story and mission' },
    { name: 'TGM App', href: '/appdownload', icon: Smartphone, description: 'Mobile investment platform' },
    { name: 'Contact', href: '/contact', icon: Mail, description: 'Get in touch with us' },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsSticky(window.scrollY > 0);
    };
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMobileMenuOpen(false);
      }
    };
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [pathname]);

  const openAuthOverlay = (action: 'login' | 'signup') => {
    setAuthAction(action);
    setIsOverlayOpen(true);
  };

  const closeAuthOverlay = () => {
    setIsOverlayOpen(false);
    setAuthAction(null);
  };

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Determine dashboard link based on user role
  const getDashboardLink = () => {
    if (!user) return null;

    const isAdmin = user.role === 'admin';
    return {
      href: isAdmin ? '/admin' : '/dashboard',
      label: isAdmin ? 'Admin Panel' : 'Dashboard',
      icon: isAdmin ? Shield : LayoutDashboard,
      className: isAdmin
        ? 'text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20'
        : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20'
    };
  };

  const dashboardLink = getDashboardLink();

  // Handle dashboard navigation with automatic redirection
  const handleDashboardNavigation = (e: React.MouseEvent) => {
    e.preventDefault();
    if (!user || !dashboardLink) return;

    // Automatically redirect based on user role
    const targetPath = user.role === 'admin' ? '/admin' : '/dashboard';
    router.push(targetPath);

    // Close mobile menu if open
    setIsMobileMenuOpen(false);
  };

  if (loading) {
    return <div className="flex justify-center items-center h-16">Loading...</div>;
  }

  return (
    <>
      <motion.nav
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ease-in-out ${
          isSticky
            ? 'bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl shadow-lg border-b border-white/20'
            : 'bg-transparent'
        }`}
      >
        {/* Premium gradient border */}
        <div className={`absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent transition-opacity duration-500 ${
          isSticky ? 'opacity-100' : 'opacity-0'
        }`}></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-4">
              {/* Mobile Menu Button */}
              <div className="md:hidden">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  aria-expanded={isMobileMenuOpen}
                  aria-controls="mobile-menu"
                  className="relative p-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-slate-700 dark:text-slate-200 hover:bg-white/20 transition-all duration-300 shadow-lg"
                >
                  <span className="sr-only">Open main menu</span>
                  <AnimatePresence mode="wait">
                    {isMobileMenuOpen ? (
                      <motion.div
                        key="close"
                        initial={{ rotate: -90, opacity: 0 }}
                        animate={{ rotate: 0, opacity: 1 }}
                        exit={{ rotate: 90, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <X className="h-6 w-6" />
                      </motion.div>
                    ) : (
                      <motion.div
                        key="menu"
                        initial={{ rotate: 90, opacity: 0 }}
                        animate={{ rotate: 0, opacity: 1 }}
                        exit={{ rotate: -90, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Menu className="h-6 w-6" />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.button>
              </div>

              {/* Logo */}
              <Link href="/" className="flex-shrink-0 group">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl blur-lg opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
                    <div className="relative p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl">
                      <Sparkles className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div className="hidden sm:block">
                    <div className="text-2xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-purple-900 bg-clip-text text-transparent dark:from-white dark:via-blue-100 dark:to-purple-100">
                      TGM Finance
                    </div>
                    <div className="text-xs text-slate-500 dark:text-slate-400 font-medium tracking-wider">
                      PREMIUM INVESTMENTS
                    </div>
                  </div>
                </div>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex lg:items-center lg:space-x-1">
              {navItems.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className={`group relative px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                      pathname === item.href
                        ? 'text-blue-600 dark:text-blue-400'
                        : 'text-slate-700 dark:text-slate-200 hover:text-blue-600 dark:hover:text-blue-400'
                    }`}
                  >
                    <span className="relative z-10 flex items-center space-x-2">
                      <item.icon className="h-4 w-4" />
                      <span>{item.name}</span>
                    </span>

                    {/* Hover background */}
                    <div className="absolute inset-0 bg-white/50 dark:bg-slate-800/50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 backdrop-blur-sm"></div>

                    {/* Active indicator */}
                    {pathname === item.href && (
                      <motion.div
                        layoutId="activeTab"
                        className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-500/20"
                        transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                      />
                    )}
                  </Link>
                </motion.div>
              ))}

              {/* Dashboard/Admin Link for authenticated users */}
              {user && dashboardLink && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  className="ml-4"
                >
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleDashboardNavigation}
                    title={`Go to ${dashboardLink.label}`}
                    className={`group relative px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl ${
                      user.role === 'admin'
                        ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white hover:from-red-600 hover:to-pink-600'
                        : 'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600'
                    }`}
                  >
                    <span className="relative z-10 flex items-center space-x-2">
                      <dashboardLink.icon className="h-4 w-4" />
                      <span>{dashboardLink.label}</span>
                    </span>

                    {/* Glow effect */}
                    <div className={`absolute inset-0 rounded-xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300 ${
                      user.role === 'admin'
                        ? 'bg-gradient-to-r from-red-500 to-pink-500'
                        : 'bg-gradient-to-r from-blue-500 to-purple-500'
                    }`}></div>
                  </motion.button>
                </motion.div>
              )}
            </div>

            {/* Auth Buttons and User Menu */}
            <div className="flex items-center space-x-3">
              <ThemeSwitcher />

              {user ? (
                <div className="flex items-center space-x-3">
                  {/* User Profile */}
                  <div className="hidden md:flex items-center space-x-3 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                    <div className="h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                      {user.name?.charAt(0) || 'U'}
                    </div>
                    <div className="text-sm">
                      <div className="font-medium text-slate-900 dark:text-white">
                        {user.name || 'User'}
                      </div>
                      <div className="text-xs text-slate-500 dark:text-slate-400">
                        {user.role === 'admin' ? 'Administrator' : 'Investor'}
                      </div>
                    </div>
                  </div>

                  {/* Logout Button */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className={`group relative px-4 py-2 rounded-xl text-sm font-medium text-white bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 transition-all duration-300 shadow-lg hover:shadow-xl ${
                      isLoggingOut ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    <span className="relative z-10 flex items-center space-x-2">
                      <LogOut className="h-4 w-4" />
                      <span className="hidden md:inline">
                        {isLoggingOut ? 'Logging out...' : 'Logout'}
                      </span>
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
                  </motion.button>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => openAuthOverlay('login')}
                    className="group relative px-4 py-2 rounded-xl text-sm font-medium text-slate-700 dark:text-slate-200 bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300"
                  >
                    <span className="relative z-10 flex items-center space-x-2">
                      <LogIn className="h-4 w-4" />
                      <span className="hidden md:inline">Login</span>
                    </span>
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => openAuthOverlay('signup')}
                    className="group relative px-4 py-2 rounded-xl text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    <span className="relative z-10 flex items-center space-x-2">
                      <UserPlus className="h-4 w-4" />
                      <span className="hidden md:inline">Get Started</span>
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
                  </motion.button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
                onClick={() => setIsMobileMenuOpen(false)}
              />

              {/* Mobile Menu Panel */}
              <motion.div
                ref={mobileMenuRef}
                initial={{ x: '100%' }}
                animate={{ x: 0 }}
                exit={{ x: '100%' }}
                transition={{ type: "spring", damping: 25, stiffness: 200 }}
                className="fixed inset-y-0 right-0 w-80 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl shadow-2xl z-50 lg:hidden border-l border-white/20"
              >
                <div className="flex flex-col h-full">
                  {/* Header */}
                  <div className="flex items-center justify-between p-6 border-b border-white/10">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg">
                        <Sparkles className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <div className="text-lg font-bold text-slate-900 dark:text-white">TGM Finance</div>
                        <div className="text-xs text-slate-500 dark:text-slate-400">Premium Investments</div>
                      </div>
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setIsMobileMenuOpen(false)}
                      className="p-2 rounded-lg bg-white/10 text-slate-700 dark:text-slate-200 hover:bg-white/20 transition-colors duration-200"
                    >
                      <X className="h-6 w-6" />
                    </motion.button>
                  </div>
                  {/* Navigation Items */}
                  <div className="flex-1 px-6 py-6 space-y-2">
                    {navItems.map((item, index) => (
                      <motion.div
                        key={item.name}
                        initial={{ opacity: 0, x: 50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <Link
                          href={item.href}
                          onClick={() => setIsMobileMenuOpen(false)}
                          className={`group flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-300 ${
                            pathname === item.href
                              ? 'bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 text-blue-600 dark:text-blue-400'
                              : 'text-slate-700 dark:text-slate-200 hover:bg-white/10 hover:text-blue-600 dark:hover:text-blue-400'
                          }`}
                        >
                          <div className={`p-2 rounded-lg transition-colors duration-300 ${
                            pathname === item.href
                              ? 'bg-blue-500/20 text-blue-600 dark:text-blue-400'
                              : 'bg-white/10 text-slate-500 group-hover:bg-blue-500/20 group-hover:text-blue-600'
                          }`}>
                            <item.icon className="h-5 w-5" />
                          </div>
                          <div className="flex-1">
                            <div className="font-medium">{item.name}</div>
                            <div className="text-sm text-slate-500 dark:text-slate-400">{item.description}</div>
                          </div>
                        </Link>
                      </motion.div>
                    ))}

                    {/* Dashboard/Admin Link for authenticated users in mobile */}
                    {user && dashboardLink && (
                      <>
                        <div className="border-t border-white/10 my-6"></div>
                        <motion.div
                          initial={{ opacity: 0, x: 50 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: 0.6 }}
                        >
                          <button
                            onClick={handleDashboardNavigation}
                            className={`group w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-300 ${
                              user.role === 'admin'
                                ? 'bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/20 text-red-600 dark:text-red-400'
                                : 'bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 text-blue-600 dark:text-blue-400'
                            }`}
                          >
                            <div className={`p-2 rounded-lg ${
                              user.role === 'admin'
                                ? 'bg-red-500/20 text-red-600 dark:text-red-400'
                                : 'bg-blue-500/20 text-blue-600 dark:text-blue-400'
                            }`}>
                              <dashboardLink.icon className="h-5 w-5" />
                            </div>
                            <div className="flex-1 text-left">
                              <div className="font-semibold">{dashboardLink.label}</div>
                              <div className="text-sm text-slate-500 dark:text-slate-400">
                                {user.role === 'admin' ? 'Administrative panel' : 'Your investment dashboard'}
                              </div>
                            </div>
                            {user.role === 'admin' && (
                              <span className="text-xs bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 px-2 py-1 rounded-full">
                                Admin
                              </span>
                            )}
                          </button>
                        </motion.div>
                      </>
                    )}
                  </div>

                  {/* Footer */}
                  <div className="border-t border-white/10 p-6 space-y-4">
                    <div className="flex items-center justify-center">
                      <ThemeSwitcher />
                    </div>

                    {user ? (
                      <div className="space-y-3">
                        {/* User Info */}
                        <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-xl">
                          <div className="h-10 w-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                            {user.name?.charAt(0) || 'U'}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-slate-900 dark:text-white">{user.name || 'User'}</div>
                            <div className="text-sm text-slate-500 dark:text-slate-400">
                              {user.role === 'admin' ? 'Administrator' : 'Investor'}
                            </div>
                          </div>
                        </div>

                        {/* Logout Button */}
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={handleLogout}
                          disabled={isLoggingOut}
                          className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-xl text-sm font-medium text-white bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 transition-all duration-300 ${
                            isLoggingOut ? 'opacity-50 cursor-not-allowed' : ''
                          }`}
                        >
                          <LogOut className="h-4 w-4" />
                          <span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
                        </motion.button>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => {
                            openAuthOverlay('login');
                            setIsMobileMenuOpen(false);
                          }}
                          className="w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-xl text-sm font-medium text-slate-700 dark:text-slate-200 bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-300"
                        >
                          <LogIn className="h-4 w-4" />
                          <span>Login</span>
                        </motion.button>

                        <motion.button
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => {
                            openAuthOverlay('signup');
                            setIsMobileMenuOpen(false);
                          }}
                          className="w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-xl text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 transition-all duration-300"
                        >
                          <UserPlus className="h-4 w-4" />
                          <span>Get Started</span>
                        </motion.button>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </motion.nav>

      {/* User Actions Overlay */}
      <UserActionsOverlay isOpen={isOverlayOpen} onClose={closeAuthOverlay}>
        {authAction === 'login' ? (
          <UserLogin onClose={closeAuthOverlay} />
        ) : authAction === 'signup' ? (
          <UserSignup onClose={closeAuthOverlay} />
        ) : null}
      </UserActionsOverlay>
    </>
  );
}

