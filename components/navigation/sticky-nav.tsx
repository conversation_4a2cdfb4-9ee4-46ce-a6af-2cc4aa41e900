
// components/navigation/StickyNav.tsx

'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { Menu, X, Home, Smartphone, Info, PiggyBank, HelpCircle, Mail, LogIn, UserPlus, LogOut, LayoutDashboard, Shield } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import UserActionsOverlay from '@/components/UserActionsOverlay';
import UserLogin from '@/components/UserLogin';
import UserSignup from '@/components/UserSignup';
import ThemeSwitcher from '@/components/ThemeSwitcher';

export default function StickyNav() {
  const [isSticky, setIsSticky] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [authAction, setAuthAction] = useState<'login' | 'signup' | null>(null);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  const pathname = usePathname();
  const router = useRouter();
  const { user, logout, loading } = useAuth();

  const navItems = [
    { name: 'Home', href: '/', icon: Home },
    { name: 'TGM App', href: '/appdownload', icon: Smartphone },
    { name: 'About TGM', href: '/about', icon: Info },
    { name: 'Investment Solutions', href: '/plans', icon: PiggyBank },
    { name: 'How It Works', href: '/how-it-works', icon: HelpCircle },
    { name: 'Contact', href: '/contact', icon: Mail },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsSticky(window.scrollY > 0);
    };
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMobileMenuOpen(false);
      }
    };
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [pathname]);

  const openAuthOverlay = (action: 'login' | 'signup') => {
    setAuthAction(action);
    setIsOverlayOpen(true);
  };

  const closeAuthOverlay = () => {
    setIsOverlayOpen(false);
    setAuthAction(null);
  };

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Determine dashboard link based on user role
  const getDashboardLink = () => {
    if (!user) return null;

    const isAdmin = user.role === 'admin';
    return {
      href: isAdmin ? '/admin' : '/dashboard',
      label: isAdmin ? 'Admin Panel' : 'Dashboard',
      icon: isAdmin ? Shield : LayoutDashboard,
      className: isAdmin
        ? 'text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20'
        : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20'
    };
  };

  const dashboardLink = getDashboardLink();

  // Handle dashboard navigation with automatic redirection
  const handleDashboardNavigation = (e: React.MouseEvent) => {
    e.preventDefault();
    if (!user || !dashboardLink) return;

    // Automatically redirect based on user role
    const targetPath = user.role === 'admin' ? '/admin' : '/dashboard';
    router.push(targetPath);

    // Close mobile menu if open
    setIsMobileMenuOpen(false);
  };

  if (loading) {
    return <div className="flex justify-center items-center h-16">Loading...</div>;
  }

  return (
    <>
      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out ${
          isSticky ? 'bg-white dark:bg-yellow-700 bg-opacity-80 backdrop-blur-md shadow-md' : 'bg-transparent'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              {/* Mobile Menu Button */}
              <div className="md:hidden mr-2">
                <button
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  aria-expanded={isMobileMenuOpen}
                  aria-controls="mobile-menu"
                  className="inline-flex items-center justify-center p-2 rounded-md text-gray-800 dark:text-gray-200 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-200 dark:hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                >
                  <span className="sr-only">Open main menu</span>
                  {isMobileMenuOpen ? (
                    <X className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Menu className="block h-6 w-6" aria-hidden="true" />
                  )}
                </button>
              </div>

              {/* Logo */}
              <Link href="/" className="flex-shrink-0">
                <Image
                  src="/logo.png"
                  alt="Logo"
                  width={64}
                  height={64}
                  className="h-16 w-auto"
                  priority
                />
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex md:items-center md:space-x-4">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-300 ${
                    pathname === item.href
                      ? 'text-amber-500 bg-amber-50 dark:bg-amber-900'
                      : 'text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-yellow-600 hover:text-gray-900 dark:hover:text-gray-100'
                  }`}
                >
                  <span className="hidden md:inline">{item.name}</span>
                  <item.icon className="inline-block md:hidden h-5 w-5" />
                </Link>
              ))}

              {/* Dashboard/Admin Link for authenticated users */}
              {user && dashboardLink && (
                <button
                  onClick={handleDashboardNavigation}
                  title={`Go to ${dashboardLink.label}`}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 border-2 shadow-sm hover:shadow-md ${
                    pathname.startsWith(dashboardLink.href)
                      ? `${dashboardLink.className} border-current shadow-md`
                      : `${dashboardLink.className} border-transparent hover:border-current`
                  }`}
                >
                  <span className="hidden md:inline font-semibold">{dashboardLink.label}</span>
                  <dashboardLink.icon className="inline-block md:hidden h-5 w-5" />
                </button>
              )}
            </div>

            {/* Theme Switcher and Auth Buttons */}
            <div className="flex items-center space-x-4">
              <ThemeSwitcher />
              {user ? (
                <button
                  onClick={handleLogout}
                  disabled={isLoggingOut}
                  className={`px-3 py-2 rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors duration-300 ${
                    isLoggingOut ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <span className="hidden md:inline">{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
                  <LogOut className="inline-block md:hidden h-5 w-5" />
                </button>
              ) : (
                <>
                  <button
                    onClick={() => openAuthOverlay('login')}
                    className="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-yellow-600 transition-colors duration-300"
                  >
                    <span className="hidden md:inline">Login</span>
                    <LogIn className="inline-block md:hidden h-5 w-5" />
                  </button>
                  <button
                    onClick={() => openAuthOverlay('signup')}
                    className="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-yellow-600 transition-colors duration-300"
                  >
                    <span className="hidden md:inline">SignUp</span>
                    <UserPlus className="inline-block md:hidden h-5 w-5" />
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <div
          ref={mobileMenuRef}
          className={`md:hidden fixed inset-y-0 right-0 w-64 bg-white dark:bg-yellow-700 shadow-lg transform transition-transform duration-300 ease-in-out ${
            isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
          }`}
        >
          <div className="flex flex-col h-full">
            <div className="flex justify-end p-4">
              <button
                onClick={() => setIsMobileMenuOpen(false)}
                className="text-gray-800 dark:text-gray-200 hover:text-gray-900 dark:hover:text-gray-100"
              >
                <X className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 flex-grow">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium transition-colors duration-300 ${
                    pathname === item.href
                      ? 'text-amber-500 bg-amber-50 dark:bg-amber-900'
                      : 'text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-yellow-600 hover:text-gray-900 dark:hover:text-gray-100'
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  <span>{item.name}</span>
                </Link>
              ))}

              {/* Dashboard/Admin Link for authenticated users in mobile */}
              {user && dashboardLink && (
                <>
                  <div className="border-t border-gray-200 dark:border-gray-600 my-3"></div>
                  <button
                    onClick={handleDashboardNavigation}
                    className={`flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 border-2 w-full text-left shadow-sm hover:shadow-md ${
                      pathname.startsWith(dashboardLink.href)
                        ? `${dashboardLink.className} border-current bg-opacity-20 shadow-md`
                        : `${dashboardLink.className} border-transparent hover:border-current`
                    }`}
                  >
                    <dashboardLink.icon className="h-6 w-6" />
                    <span className="font-semibold">{dashboardLink.label}</span>
                    {user.role === 'admin' && (
                      <span className="ml-auto text-xs bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 px-2 py-1 rounded-full">
                        Admin
                      </span>
                    )}
                  </button>
                </>
              )}
            </div>

            {/* Theme Switcher for Mobile */}
            <div className="px-2 pb-3 sm:px-3 flex justify-center items-center gap-2">
              <ThemeSwitcher />
              <div className="flex gap-2">
                {user ? (
                  <button
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors duration-300 ${
                      isLoggingOut ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    <LogOut className="h-5 w-5" />
                    <span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
                  </button>
                ) : (
                  <>
                    <button
                      onClick={() => openAuthOverlay('login')}
                      className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-yellow-600 transition-colors duration-300"
                    >
                      <LogIn className="h-5 w-5" />
                      <span>Login</span>
                    </button>
                    <button
                      onClick={() => openAuthOverlay('signup')}
                      className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-yellow-600 transition-colors duration-300"
                    >
                      <UserPlus className="h-5 w-5" />
                      <span>SignUp</span>
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* User Actions Overlay */}
      <UserActionsOverlay isOpen={isOverlayOpen} onClose={closeAuthOverlay}>
        {authAction === 'login' ? (
          <UserLogin onClose={closeAuthOverlay} />
        ) : authAction === 'signup' ? (
          <UserSignup onClose={closeAuthOverlay} />
        ) : null}
      </UserActionsOverlay>
    </>
  );
}

