// 'use client';

// import React, { useState, useEffect } from 'react';
// import ReferralRegistration from '@/components/ReferralRegistration';
// import { useAuth } from '@/context/AuthContext';
// import { useRouter } from 'next/navigation';

// const UserSignupContainer: React.FC = () => {
//   const [name, setName] = useState('');
//   const [email, setEmail] = useState('');
//   const [password, setPassword] = useState('');
//   const [phone, setPhone] = useState('');
//   const [error, setError] = useState<string | null>(null);
//   const [success, setSuccess] = useState(false);
//   const [loading, setLoading] = useState(false);
//   const [referralCode, setReferralCode] = useState<string | null>(null);

//   const { login } = useAuth();
//   const router = useRouter();

//   useEffect(() => {
//     // Get referral code from cookies
//     const storedReferralCode = document.cookie
//       .split('; ')
//       .find((row) => row.startsWith('referralCode='))
//       ?.split('=')[1];

//     if (storedReferralCode) {
//       setReferralCode(storedReferralCode);
//     }
//   }, []);

//   const validateForm = () => {
//     if (!name || !email || !password || !phone) {
//       setError('All fields are required');
//       return false;
//     }
//     return true;
//   };

//   const clearForm = () => {
//     setName('');
//     setEmail('');
//     setPassword('');
//     setPhone('');
//   };

//   const handleSignup = async () => {
//     if (!validateForm()) {
//       return;
//     }

//     setError(null);
//     setLoading(true);

//     try {
//       const response = await fetch('/api/users', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({ name, email, password, phone, referralCode }),
//       });

//       const data = await response.json();

//       if (!response.ok) {
//         setError(data.error || 'Something went wrong');
//         setLoading(false);
//         return;
//       }

//       const { token, refreshToken, user } = data;
//       login({ token, refreshToken, user });
//       setSuccess(true);
//       clearForm();
//       setLoading(false);

//       setTimeout(() => {
//         router.push('/dashboard');
//       }, 2000);
//     } catch (err) {
//       console.error('Error:', err);
//       setError('Something went wrong');
//       setLoading(false);
//     }
//   };

//   const handleChange = (field: string, value: string) => {
//     if (field === 'name') setName(value);
//     if (field === 'email') setEmail(value);
//     if (field === 'password') setPassword(value);
//     if (field === 'phone') setPhone(value);
//   };

//   return (
//     <div className="min-h-screen flex items-center justify-center bg-cover bg-center bg-no-repeat" style={{ backgroundImage: "url('/referallpage.png')" }}>
//       <div className="bg-black/10 backdrop-blur-md p-8 rounded-xl shadow-2xl w-full max-w-md">
//         <ReferralRegistration
//           name={name}
//           email={email}
//           password={password}
//           phone={phone}
//           error={error}
//           loading={loading}
//           success={success}
//           onChange={handleChange}
//           onSubmit={handleSignup}
//         />
//       </div>
//     </div>
//   );
// };

// export default UserSignupContainer;




'use client';

import React, { useState, useEffect } from 'react';
import ReferralRegistration from '@/components/ReferralRegistration';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';

const UserSignupContainer: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [phone, setPhone] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const [referralCode, setReferralCode] = useState<string | null>(null);

  const { login } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Get referral code from cookies
    const storedReferralCode = document.cookie
      .split('; ')
      .find((row) => row.startsWith('referralCode='))
      ?.split('=')[1];

    if (storedReferralCode) {
      setReferralCode(storedReferralCode);
    }
  }, []);

  const validateForm = () => {
    if (!name || !email || !password || !phone) {
      setError('All fields are required');
      return false;
    }
    return true;
  };

  const clearForm = () => {
    setName('');
    setEmail('');
    setPassword('');
    setPhone('');
  };

  const handleSignup = async () => {
    if (!validateForm()) {
      return;
    }

    setError(null);
    setLoading(true);

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, password, phone, referralCode }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || 'Something went wrong');
        setLoading(false);
        return;
      }

      // Instead of calling login directly, we'll use the data to sign up
      // and then immediately log in the user
      await login(email, password, false); // Assuming rememberMe is false by default

      setSuccess(true);
      clearForm();
      setLoading(false);

      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    } catch (err) {
      console.error('Error:', err);
      setError('Something went wrong');
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string) => {
    if (field === 'name') setName(value);
    if (field === 'email') setEmail(value);
    if (field === 'password') setPassword(value);
    if (field === 'phone') setPhone(value);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-cover bg-center bg-no-repeat" style={{ backgroundImage: "url('/referallpage.png')" }}>
      <div className="bg-black/10 backdrop-blur-md p-8 rounded-xl shadow-2xl w-full max-w-md">
        <ReferralRegistration
          name={name}
          email={email}
          password={password}
          phone={phone}
          error={error}
          loading={loading}
          success={success}
          onChange={handleChange}
          onSubmit={handleSignup}
        />
      </div>
    </div>
  );
};

export default UserSignupContainer;

