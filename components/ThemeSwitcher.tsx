// // components/ThemeSwitcher.tsx

// 'use client';
// import { useTheme } from '@/context/ThemeContext';
// import { Sun, Moon } from 'lucide-react';

// export default function ThemeSwitcher() {
//   const { theme, toggleTheme } = useTheme();

//   return (
//     <button
//       onClick={toggleTheme}
//       aria-label="Toggle theme"
//       className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
//     >
//       {theme === 'light' ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
//     </button>
//   );
// }

// 'use client';

// import React, { useEffect, useState } from 'react';

// const ThemeSwitcher: React.FC = () => {
//   const [isDark, setIsDark] = useState<boolean>(false); // Default to `false` for SSR

//   useEffect(() => {
//     // Access localStorage and set the initial theme only after the component mounts
//     const storedTheme = window.localStorage.getItem('theme');
//     if (storedTheme === 'dark') {
//       setIsDark(true);
//       document.documentElement.classList.add('dark');
//     }
//   }, []);

//   useEffect(() => {
//     // Update the document's root class and localStorage when the theme changes
//     const root = document.documentElement;
//     if (isDark) {
//       root.classList.add('dark');
//       localStorage.setItem('theme', 'dark');
//     } else {
//       root.classList.remove('dark');
//       localStorage.setItem('theme', 'light');
//     }
//   }, [isDark]);

//   const toggleTheme = () => setIsDark((prev) => !prev);

//   return (
//     <button
//       onClick={toggleTheme}
//       className="p-2 rounded-md bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-gray-700 transition-colors"
//     >
//       {isDark ? '🌞 Light Mode' : '🌜 Dark Mode'}
//     </button>
//   );
// };

// export default ThemeSwitcher;



// 'use client';

// import React, { useEffect, useState } from 'react';
// import { useTheme, SunIcon, MoonIcon } from '@/context/ThemeContext';

// const ThemeSwitcher: React.FC = () => {
//   const { theme, toggleTheme } = useTheme();
//   const [mounted, setMounted] = useState(false);

//   useEffect(() => {
//     setMounted(true);
//   }, []);

//   if (!mounted) {
//     return null;
//   }

//   return (
//     <button
//       onClick={toggleTheme}
//       className="p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-300"
//       aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
//     >
//       {theme === 'dark' ? <SunIcon className="h-5 w-5" /> : <MoonIcon className="h-5 w-5" />}
//     </button>
//   );
// };

// export default ThemeSwitcher;



'use client';

import React from 'react';
import { useTheme } from '@/context/ThemeContext';

const ThemeSwitcher: React.FC = () => {
  const { theme, toggleTheme, SunIcon, MoonIcon } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={`p-2 rounded-md transition-colors duration-300 ${
        theme === 'dark'
          ? 'text-gray-200 hover:bg-yellow-600'
          : 'text-gray-700 hover:bg-gray-200'
      }`}
      aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      {theme === 'dark' ? <SunIcon className="h-5 w-5" /> : <MoonIcon className="h-5 w-5" />}
    </button>
  );
};

export default ThemeSwitcher;

