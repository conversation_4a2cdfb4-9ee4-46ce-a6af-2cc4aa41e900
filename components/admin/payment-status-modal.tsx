// components/admin/payment-status-modal.tsx
import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { X, Plus, Minus } from 'lucide-react';
import { formatCurrencyDisplay } from '@/lib/currency';

interface PaymentStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  investment: {
    planId: { title: string };
    amount: number;
    paymentDate: string;
    depositSlipUrl?: string | null;
  } | null;
  onApprove: () => void;
  onDecline: () => void;
  onDeclinedWithReason: () => void;
}

export function PaymentStatusModal({
  isOpen,
  onClose,
  investment,
  onApprove,
  onDecline,
  onDeclinedWithReason,
}: PaymentStatusModalProps) {
  const [zoomLevel, setZoomLevel] = useState(100); // Start with 100% zoom

  if (!investment) return null;

  const increaseZoom = () => setZoomLevel((prev) => Math.min(prev + 25, 200)); // Max zoom 200%
  const decreaseZoom = () => setZoomLevel((prev) => Math.max(prev - 25, 50)); // Min zoom 50%

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle>Payment Details</DialogTitle>
          <DialogClose asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
              <X className="h-4 w-4" />
            </Button>
          </DialogClose>
        </DialogHeader>

        <div className="flex flex-col items-center space-y-6 py-6">
          {/* Investment Details */}
          <div className="w-full text-center space-y-2">
            <h3 className="font-semibold text-lg">{investment.planId.title}</h3>
            <p className="text-3xl font-bold">{formatCurrencyDisplay(investment.amount)}</p>
            <p className="text-sm text-muted-foreground">
              Paid on {new Date(investment.paymentDate).toLocaleDateString()}
            </p>
          </div>

          {/* Deposit Slip Image with Zoom Controls */}
          {investment.depositSlipUrl ? (
            <div className="w-full space-y-2 text-center">
              <p className="font-semibold text-sm">Deposit Slip</p>
              <div className="relative w-full overflow-auto border rounded-lg" style={{ height: '300px' }}>
                <img
                  src={`/api/files/${investment.depositSlipUrl}`}
                  alt="Deposit Slip"
                  style={{ width: `${zoomLevel}%`, height: 'auto', cursor: 'grab' }}
                  className="mx-auto"
                />
              </div>
              <div className="flex justify-center space-x-4 mt-2">
                <Button variant="outline" size="icon" onClick={decreaseZoom}>
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="font-medium text-sm">{zoomLevel}%</span>
                <Button variant="outline" size="icon" onClick={increaseZoom}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : (
            <p className="text-sm text-muted-foreground italic">No deposit slip uploaded</p>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-2 mt-2">
          <Button onClick={onApprove} className="w-full bg-green-500">
            Approve Payment
          </Button>
          <Button variant="destructive" onClick={onDeclinedWithReason} className="w-full">
            Decline with Reason
          </Button>
          <Button variant="outline" onClick={onDecline} className="w-full">
            Decline Payment
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}


