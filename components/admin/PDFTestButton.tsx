'use client';

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { generateTestPDF } from '@/lib/testPDF';
import { FileText } from 'lucide-react';

const PDFTestButton: React.FC = () => {
  const handleTestPDF = () => {
    try {
      console.log('Testing PDF generation...');
      generateTestPDF();
      alert('PDF test successful! Check your downloads.');
    } catch (error) {
      console.error('PDF test failed:', error);
      alert('PDF test failed: ' + (error as Error).message);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-white shadow-sm">
      <h3 className="text-lg font-semibold mb-4">PDF Generation Test</h3>
      <Button onClick={handleTestPDF} className="flex items-center space-x-2">
        <FileText className="h-4 w-4" />
        <span>Test PDF Generation</span>
      </Button>
      <p className="text-sm text-gray-600 mt-2">
        Click to test if basic PDF generation is working.
      </p>
    </div>
  );
};

export default PDFTestButton;
