// 'use client';

// import React, { useState, useEffect } from 'react';
// import {
//   Card,
//   CardContent,
//   CardHeader,
//   CardTitle,
// } from '@/components/ui/card';
// import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
// import { Badge } from '@/components/ui/badge';
// import { Skeleton } from '@/components/ui/skeleton';

// interface Referral {
//   _id: string;
//   referrer: { name: string; email: string };
//   referredUser: { _id: string; name: string; email: string } | null;
//   referralId: string;
//   createdAt: string;
//   active: boolean; // Indicates whether the referred user has active investments
// }

// const AdminReferrals: React.FC = () => {
//   const [referrals, setReferrals] = useState<Referral[]>([]);
//   const [loading, setLoading] = useState(true);

//   useEffect(() => {
//     fetchReferrals();
//   }, []);

//   const fetchReferrals = async () => {
//     setLoading(true);
//     try {
//       const response = await fetch('/api/admin/referrals');
//       const data = await response.json();
//       setReferrals(data.referrals);
//     } catch (error) {
//       console.error('Error fetching referrals:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const getBadgeVariant = (isActive: boolean) => (isActive ? 'outline' : 'destructive');

//   const renderSkeletonRow = () => (
//     <TableRow>
//       <TableCell>
//         <Skeleton className="h-4 w-[200px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[200px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[150px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[80px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[120px]" />
//       </TableCell>
//     </TableRow>
//   );

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>Referrals</CardTitle>
//       </CardHeader>
//       <CardContent>
//         <div className="rounded-md border">
//           <Table>
//             <TableHeader>
//               <TableRow>
//                 <TableHead>Referrer</TableHead>
//                 <TableHead>Referred User</TableHead>
//                 <TableHead>Referral Code</TableHead>
//                 <TableHead>Status</TableHead>
//                 <TableHead>Created At</TableHead>
//               </TableRow>
//             </TableHeader>
//             <TableBody>
//               {loading
//                 ? Array.from({ length: 5 }).map((_, index) => (
//                     <TableRow key={index}>{renderSkeletonRow()}</TableRow>
//                   ))
//                 : referrals.length > 0
//                 ? referrals.map((referral) => (
//                     <TableRow key={referral._id}>
//                       <TableCell>
//                         <div className="font-medium">{referral.referrer.name}</div>
//                         <div className="text-sm text-muted-foreground">
//                           {referral.referrer.email}
//                         </div>
//                       </TableCell>
//                       <TableCell>
//                         {referral.referredUser ? (
//                           <>
//                             <div className="font-medium">{referral.referredUser.name}</div>
//                             <div className="text-sm text-muted-foreground">
//                               {referral.referredUser.email}
//                             </div>
//                           </>
//                         ) : (
//                           'N/A'
//                         )}
//                       </TableCell>
//                       <TableCell>{referral.referralId}</TableCell>
//                       <TableCell>
//                         <Badge variant={getBadgeVariant(referral.active)}>
//                           {referral.active ? 'Active' : 'Inactive'}
//                         </Badge>
//                       </TableCell>
//                       <TableCell>
//                         {new Date(referral.createdAt).toLocaleDateString()}
//                       </TableCell>
//                     </TableRow>
//                   ))
//                 : (
//                   <TableRow>
//                     <TableCell colSpan={5} className="text-center text-muted-foreground">
//                       No referrals found
//                     </TableCell>
//                   </TableRow>
//                 )}
//             </TableBody>
//           </Table>
//         </div>
//       </CardContent>
//     </Card>
//   );
// };

// export default AdminReferrals;
'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

interface Referral {
  _id: string;
  referrer: { name: string; email: string };
  referredUser: { _id: string; name: string; email: string } | null;
  referralId: string;
  createdAt: string;
  active: boolean;
}

const AdminReferrals: React.FC = () => {
  const [referrals, setReferrals] = useState<Referral[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAdminReferrals();
  }, []);

  // Fetch referrals data from the API
  const fetchAdminReferrals = async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No token found');
      setError('Authentication token missing.');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/admin/referrals', {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${await response.text()}`);
      }

      const data: { referrals: Referral[] } = await response.json();
      setReferrals(data.referrals || []);
      setError(null);
    } catch (err: unknown) {
      console.error('Error fetching admin referrals:', err);
      setError(
        err instanceof Error
          ? err.message
          : 'An unknown error occurred while fetching referrals.'
      );
    } finally {
      setLoading(false);
    }
  };

  const getBadgeVariant = (isActive: boolean) => (isActive ? 'outline' : 'destructive');

  const renderSkeletonRow = () => (
    <TableRow>
      <TableCell>
        <Skeleton className="h-4 w-[200px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[200px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[150px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[120px]" />
      </TableCell>
    </TableRow>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Admin Referrals</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Referrer</TableHead>
                <TableHead>Referred User</TableHead>
                <TableHead>Referral Code</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created At</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading
                ? Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>{renderSkeletonRow()}</TableRow>
                  ))
                : error
                ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center text-red-500">
                      {error}
                    </TableCell>
                  </TableRow>
                )
                : referrals.length > 0
                ? referrals.map((referral) => (
                    <TableRow key={referral._id}>
                      <TableCell>
                        <div className="font-medium">{referral.referrer.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {referral.referrer.email}
                        </div>
                      </TableCell>
                      <TableCell>
                        {referral.referredUser ? (
                          <>
                            <div className="font-medium">{referral.referredUser.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {referral.referredUser.email}
                            </div>
                          </>
                        ) : (
                          'N/A'
                        )}
                      </TableCell>
                      <TableCell>{referral.referralId}</TableCell>
                      <TableCell>
                        <Badge variant={getBadgeVariant(referral.active)}>
                          {referral.active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(referral.createdAt).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  ))
                : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center text-muted-foreground">
                      No referrals found.
                    </TableCell>
                  </TableRow>
                )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminReferrals;
