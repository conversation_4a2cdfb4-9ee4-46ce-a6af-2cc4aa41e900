// components/referrals/Clipboard.tsx
'use client';

import React from 'react';
import { FaCopy } from 'react-icons/fa';

interface ClipboardProps {
  text: string;
  onCopy?: () => void;
}

const Clipboard: React.FC<ClipboardProps> = ({ text, onCopy }) => {
  const copyToClipboard = () => {
    navigator.clipboard.writeText(text);
    if (onCopy) onCopy();
    alert('Text copied to clipboard!');
  };

  return (
    <button
      onClick={copyToClipboard}
      className="p-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
    >
      <FaCopy className="mr-2" /> Copy
    </button>
  );
};

export default Clipboard;
