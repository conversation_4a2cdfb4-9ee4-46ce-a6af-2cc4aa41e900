// // components/referrals/ReferralTable.tsx
// 'use client';

// import React from 'react';

// interface Referral {
//   user: {
//     name: string;
//     email: string;
//     createdAt: string;
//   };
// }

// interface ReferralTableProps {
//   referrals: Referral[];
// }

// const ReferralTable: React.FC<ReferralTableProps> = ({ referrals }) => {
//   return referrals.length > 0 ? (
//     <table className="w-full border-collapse border">
//       <thead>
//         <tr className="bg-gray-100">
//           <th className="border p-2">Name</th>
//           <th className="border p-2">Email</th>
//           <th className="border p-2">Signup Date</th>
//         </tr>
//       </thead>
//       <tbody>
//         {referrals.map((referral, index) => (
//           <tr key={index}>
//             <td className="border p-2">{referral.user.name}</td>
//             <td className="border p-2">{referral.user.email}</td>
//             <td className="border p-2">
//               {new Date(referral.user.createdAt).toLocaleDateString()}
//             </td>
//           </tr>
//         ))}
//       </tbody>
//     </table>
//   ) : (
//     <p className="text-gray-500">No referrals yet.</p>
//   );
// };

// export default ReferralTable;

// // components/referrals/ReferralTable.tsx

// 'use client';

// import React from 'react';

// interface Referral {
//   user: {
//     name: string;
//     email: string;
//     createdAt: string;
//   };
// }

// interface ReferralTableProps {
//   referrals: Referral[];
// }

// const ReferralTable: React.FC<ReferralTableProps> = ({ referrals }) => {
//   return referrals.length > 0 ? (
//     <table className="w-full border-collapse border">
//       <thead>
//         <tr className="bg-gray-100">
//           <th className="border p-2">Name</th>
//           <th className="border p-2">Email</th>
//           <th className="border p-2">Signup Date</th>
//         </tr>
//       </thead>
//       <tbody>
//         {referrals.map((referral, index) => (
//           <tr key={index}>
//             <td className="border p-2">{referral.user.name}</td>
//             <td className="border p-2">{referral.user.email}</td>
//             <td className="border p-2">
//               {new Date(referral.user.createdAt).toLocaleDateString()}
//             </td>
//           </tr>
//         ))}
//       </tbody>
//     </table>
//   ) : (
//     <p className="text-gray-500">No referrals yet.</p>
//   );
// };

// export default ReferralTable;



'use client';

import React from 'react';

interface Referral {
  user: {
    name: string;
    email: string;
    createdAt: string; // Ensure this matches the ISO format sent from the backend
  };
}

interface ReferralTableProps {
  referrals: Referral[];
}

const ReferralTable: React.FC<ReferralTableProps> = ({ referrals }) => {
  return referrals.length > 0 ? (
    <table className="w-full border-collapse border">
      <thead>
        <tr className="bg-gray-100">
          <th className="border p-2">Name</th>
          <th className="border p-2">Email</th>
          <th className="border p-2">Signup Date</th>
        </tr>
      </thead>
      <tbody>
        {referrals.map((referral, index) => (
          <tr key={index}>
            <td className="border p-2">{referral.user.name}</td>
            <td className="border p-2">{referral.user.email}</td>
            <td className="border p-2">
              {new Date(referral.user.createdAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  ) : (
    <p className="text-gray-500">No referrals yet.</p>
  );
};

export default ReferralTable;
