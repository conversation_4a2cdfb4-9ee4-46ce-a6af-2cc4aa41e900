

// components/admin/referrals/ReferralDashboard.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Clipboard from './Clipboard';
import ShareToSocial from './ShareToSocial';
import ReferralTable from './ReferralTable';
import { fetchUserReferrals, fetchReferralLink } from '@/lib/liferrals/referralApi';

// Define the type for a referral
interface Referral {
  user: {
    name: string;
    email: string;
    createdAt: string;
  };
}

const ReferralDashboard: React.FC = () => {
  const [referrals, setReferrals] = useState<Referral[]>([]); // Explicitly typed
  const [referralLink, setReferralLink] = useState<string>(''); // String type for referral link
  const [loading, setLoading] = useState<boolean>(true); // Boolean type for loading
  const [error, setError] = useState<string | null>(null); // String or null for error state
  const [showModal, setShowModal] = useState<boolean>(false); // Boolean type for modal state

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [fetchedReferrals, fetchedReferralLink] = await Promise.all([
          fetchUserReferrals(),
          fetchReferralLink(),
        ]);

        setReferrals(fetchedReferrals);
        setReferralLink(fetchedReferralLink);
      } catch (err) {
        console.error('Error fetching referrals or referral link:', err);
        setError('Failed to load referral data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const openModal = () => setShowModal(true);
  const closeModal = () => setShowModal(false);

  return (
    <div className="p-6 bg-white shadow rounded">
      <h1 className="text-2xl font-bold mb-4">Your Referrals</h1>

      {/* Referral Link Section */}
      {loading ? (
        <p>Loading referral data...</p>
      ) : error ? (
        <p className="text-red-500">{error}</p>
      ) : (
        <div className="flex items-center space-x-4 mb-6">
          <input
            type="text"
            value={referralLink}
            readOnly
            className="w-full p-2 border rounded bg-gray-100"
          />
          <Clipboard text={referralLink} />
          <button
            onClick={openModal}
            className="p-2 bg-green-600 text-white rounded hover:bg-green-700 flex items-center"
          >
            Share
          </button>
        </div>
      )}

      {/* Referred Users Table */}
      <h2 className="text-xl font-semibold mb-2">Referred Users</h2>
      {loading ? (
        <p>Loading referrals...</p>
      ) : (
        <ReferralTable referrals={referrals} />
      )}

      {/* Share Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded shadow-lg p-6">
            <h2 className="text-lg font-semibold mb-4">Share Your Referral Link</h2>
            <ShareToSocial link={referralLink} />
            <button
              onClick={closeModal}
              className="mt-4 p-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReferralDashboard;

