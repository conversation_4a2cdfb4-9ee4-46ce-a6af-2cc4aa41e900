'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Download, FileText, FileSpreadsheet } from 'lucide-react';

const ExportDropdownTest: React.FC = () => {
  const [exporting, setExporting] = useState(false);

  const handlePDFExport = () => {
    setExporting(true);
    console.log('PDF Export clicked');
    setTimeout(() => {
      setExporting(false);
      alert('PDF Export would happen here');
    }, 2000);
  };

  const handleExcelExport = () => {
    setExporting(true);
    console.log('Excel Export clicked');
    setTimeout(() => {
      setExporting(false);
      alert('Excel Export would happen here');
    }, 2000);
  };

  return (
    <div className="p-4 border rounded-lg bg-white shadow-sm">
      <h3 className="text-lg font-semibold mb-4">Export Dropdown Test</h3>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" disabled={exporting}>
            {exporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 mr-2"></div>
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export Test
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Export Options</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handlePDFExport} disabled={exporting}>
            <FileText className="h-4 w-4 mr-2" />
            Export as PDF
            {exporting && <span className="ml-2 text-xs text-gray-500">(Processing...)</span>}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleExcelExport} disabled={exporting}>
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            Export as Excel
            {exporting && <span className="ml-2 text-xs text-gray-500">(Processing...)</span>}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      
      <div className="mt-4 text-sm text-gray-600">
        <p>This is a test component to verify the dropdown menu is working.</p>
        <p>Click the "Export Test" button to see if the dropdown opens.</p>
      </div>
    </div>
  );
};

export default ExportDropdownTest;
