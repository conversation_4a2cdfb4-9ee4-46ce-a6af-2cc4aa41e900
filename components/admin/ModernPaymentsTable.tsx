'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Search,
  Filter,
  Download,
  FileText,
  FileSpreadsheet,
  CheckCircle,
  Clock,
  AlertCircle,
  MoreHorizontal,
  Eye,
  Trash2,
  RefreshCw,
  Calendar,
  DollarSign,
  Users,
  TrendingUp,
  ArrowUpDown,
} from 'lucide-react';
import { ApprovePaymentModal } from './ApprovePaymentModal';
import { calculateInvestmentSummary } from '@/lib/investmentCalculations';
import { formatCurrencyDisplay } from '@/lib/currency';
import { toast } from 'react-hot-toast';
import {
  exportFilteredPayments,
  preparePaymentExportData,
  generateExportSummary
} from '@/lib/payoutExport';

interface Payment {
  _id: string;
  userId: { name: string; email: string };
  planId: { title: string };
  investmentAmount: number;
  paymentAmount: number;
  investmentDate: string;
  dueDate: string;
  paymentStatus: 'ready' | 'pending' | 'completed';
  investmentId: {
    waitPeriod: number;
  };
}

interface PaymentStats {
  totalPayments: number;
  totalAmount: number;
  pendingCount: number;
  readyCount: number;
  completedCount: number;
  averageAmount: number;
}

const ModernPaymentsTable: React.FC = () => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [filteredPayments, setFilteredPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [bulkProcessing, setBulkProcessing] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [sortBy, setSortBy] = useState<string>('dueDate');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [stats, setStats] = useState<PaymentStats>({
    totalPayments: 0,
    totalAmount: 0,
    pendingCount: 0,
    readyCount: 0,
    completedCount: 0,
    averageAmount: 0,
  });

  useEffect(() => {
    fetchPayments();
  }, []);

  useEffect(() => {
    filterAndSortPayments();
  }, [payments, searchQuery, statusFilter, dateFilter, sortBy, sortOrder]);

  const fetchPayments = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/payments/admin');
      const data = await response.json();
      const updatedPayments = checkPaymentStatus(data.payments);
      setPayments(updatedPayments);
      calculateStats(updatedPayments);
    } catch (error) {
      console.error('Error fetching payments:', error);
      toast.error('Failed to fetch payments');
    } finally {
      setLoading(false);
    }
  };

  const checkPaymentStatus = (payments: Payment[]): Payment[] => {
    return payments.map((payment) => {
      const { totalProfit } = calculateInvestmentSummary(payment.investmentAmount, 95, 20, 4);
      if (payment.investmentId.waitPeriod === 0 && payment.paymentStatus === 'pending') {
        return { ...payment, paymentStatus: 'ready', paymentAmount: totalProfit };
      }
      return { ...payment, paymentAmount: totalProfit };
    });
  };

  const calculateStats = (paymentsData: Payment[]) => {
    const totalPayments = paymentsData.length;
    const totalAmount = paymentsData.reduce((sum, p) => sum + p.paymentAmount, 0);
    const pendingCount = paymentsData.filter(p => p.paymentStatus === 'pending').length;
    const readyCount = paymentsData.filter(p => p.paymentStatus === 'ready').length;
    const completedCount = paymentsData.filter(p => p.paymentStatus === 'completed').length;
    const averageAmount = totalPayments > 0 ? totalAmount / totalPayments : 0;

    setStats({
      totalPayments,
      totalAmount,
      pendingCount,
      readyCount,
      completedCount,
      averageAmount,
    });
  };

  const filterAndSortPayments = () => {
    let filtered = [...payments];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(payment =>
        payment.userId?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        payment.userId?.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        payment.planId?.title?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(payment => payment.paymentStatus === statusFilter);
    }

    // Apply date filter
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          filtered = filtered.filter(payment => 
            new Date(payment.dueDate) >= filterDate && 
            new Date(payment.dueDate) < new Date(filterDate.getTime() + 24 * 60 * 60 * 1000)
          );
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          filtered = filtered.filter(payment => new Date(payment.dueDate) >= filterDate);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          filtered = filtered.filter(payment => new Date(payment.dueDate) >= filterDate);
          break;
        case 'overdue':
          filtered = filtered.filter(payment => 
            new Date(payment.dueDate) < now && payment.paymentStatus !== 'completed'
          );
          break;
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'dueDate':
          aValue = new Date(a.dueDate);
          bValue = new Date(b.dueDate);
          break;
        case 'paymentAmount':
          aValue = a.paymentAmount;
          bValue = b.paymentAmount;
          break;
        case 'investmentAmount':
          aValue = a.investmentAmount;
          bValue = b.investmentAmount;
          break;
        case 'userName':
          aValue = a.userId?.name || '';
          bValue = b.userId?.name || '';
          break;
        case 'status':
          aValue = a.paymentStatus;
          bValue = b.paymentStatus;
          break;
        default:
          aValue = a.dueDate;
          bValue = b.dueDate;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredPayments(filtered);
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const renderSortIcon = (field: string) => {
    if (sortBy !== field) return <ArrowUpDown className="h-4 w-4 ml-1 opacity-50" />;
    return sortOrder === 'asc' ? 
      <ArrowUpDown className="h-4 w-4 ml-1 rotate-180" /> : 
      <ArrowUpDown className="h-4 w-4 ml-1" />;
  };

  // Bulk selection functions
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(filteredPayments.map(payment => payment._id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
    }
  };

  const handleApproveButtonClick = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsModalOpen(true);
  };

  const handleApprovePayment = async () => {
    if (selectedPayment) {
      try {
        const response = await fetch(`/api/payments/${selectedPayment._id}/approve`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ paymentAmount: selectedPayment.paymentAmount }),
        });
        
        if (response.ok) {
          setPayments(prevPayments =>
            prevPayments.map(payment =>
              payment._id === selectedPayment._id
                ? { ...payment, paymentStatus: 'completed' }
                : payment
            )
          );
          setIsModalOpen(false);
          toast.success('Payment approved successfully! 💰');
        } else {
          toast.error('Failed to approve payment');
        }
      } catch (error) {
        console.error('Error approving payment:', error);
        toast.error('Error approving payment');
      }
    }
  };

  // Bulk approve ready payments
  const handleBulkApprove = async () => {
    const readyPayments = filteredPayments.filter(payment => 
      selectedItems.includes(payment._id) && 
      payment.paymentStatus === 'ready'
    );

    if (readyPayments.length === 0) {
      toast.error('No ready payments selected for approval');
      return;
    }

    const confirmMessage = `⚠️ BULK APPROVAL CONFIRMATION\n\nYou are about to approve ${readyPayments.length} payment(s).\n\nTotal amount: ${formatCurrencyDisplay(readyPayments.reduce((sum, p) => sum + p.paymentAmount, 0))}\n\nThis action cannot be undone. Are you sure you want to proceed?`;

    if (confirm(confirmMessage)) {
      setBulkProcessing(true);

      try {
        const approvalPromises = readyPayments.map(payment =>
          fetch(`/api/payments/${payment._id}/approve`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ paymentAmount: payment.paymentAmount }),
          })
        );

        const results = await Promise.allSettled(approvalPromises);
        const successCount = results.filter(result => result.status === 'fulfilled').length;

        if (successCount > 0) {
          await fetchPayments(); // Refresh data
          setSelectedItems([]);
          toast.success(`Successfully approved ${successCount} payment(s)! 🎉`);
        }

        if (successCount < readyPayments.length) {
          toast.error(`${readyPayments.length - successCount} payment(s) failed to approve`);
        }
      } catch (error) {
        console.error('Error in bulk approval:', error);
        toast.error('Failed to process bulk approval');
      } finally {
        setBulkProcessing(false);
      }
    }
  };

  // Export functions
  const handleExport = async (format: 'pdf' | 'excel') => {
    setExporting(true);

    try {
      // Use filtered payments for export
      const exportData = preparePaymentExportData(filteredPayments);
      const summary = generateExportSummary(exportData, format);

      // Show loading toast
      const loadingToast = toast.loading(`Generating ${format.toUpperCase()} export...`);

      // Small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate export
      exportFilteredPayments(exportData, stats, format);

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success(
        `📄 Export completed!\n\n` +
        `Format: ${summary.format}\n` +
        `Records: ${summary.totalRecords}\n` +
        `Total Amount: ${summary.totalAmount}\n` +
        `File: ${summary.fileName}`,
        { duration: 5000 }
      );

    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to generate export. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Payments</p>
                <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{stats.totalPayments}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Total Amount</p>
                <p className="text-2xl font-bold text-green-900 dark:text-green-100">{formatCurrencyDisplay(stats.totalAmount)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 border-amber-200 dark:border-amber-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-amber-600 dark:text-amber-400">Ready to Pay</p>
                <p className="text-2xl font-bold text-amber-900 dark:text-amber-100">{stats.readyCount}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-amber-600 dark:text-amber-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-800">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Average Amount</p>
                <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">{formatCurrencyDisplay(stats.averageAmount)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Table Card */}
      <Card className="bg-white dark:bg-slate-900 shadow-xl border-0">
        <CardHeader className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 border-b border-slate-200 dark:border-slate-600">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div>
              <CardTitle className="text-2xl font-bold text-slate-900 dark:text-white flex items-center gap-2">
                <DollarSign className="h-6 w-6 text-amber-500" />
                Payment Management
              </CardTitle>
              <p className="text-slate-600 dark:text-slate-400 mt-1">
                Manage and process user payments efficiently
              </p>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={fetchPayments}
                disabled={loading}
                className="bg-white dark:bg-slate-800"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {/* Filters and Search */}
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  type="text"
                  placeholder="Search by user name, email, or plan..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40 bg-white dark:bg-slate-800">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="ready">Ready</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>

              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-40 bg-white dark:bg-slate-800">
                  <SelectValue placeholder="Date" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Dates</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" disabled={exporting}>
                    {exporting ? (
                      <div className="w-4 h-4 border-2 border-slate-600 border-t-transparent rounded-full animate-spin mr-2" />
                    ) : (
                      <Download className="h-4 w-4 mr-2" />
                    )}
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Export Options</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleExport('pdf')}>
                    <FileText className="h-4 w-4 mr-2" />
                    Export as PDF
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExport('excel')}>
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    Export as Excel
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedItems.length > 0 && (
            <div className="flex items-center justify-between bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 px-4 py-3 rounded-lg border border-amber-200 dark:border-amber-800 mb-6">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-semibold text-amber-700 dark:text-amber-300">
                    {selectedItems.length} payment{selectedItems.length !== 1 ? 's' : ''} selected
                  </span>
                </div>
                <div className="h-4 w-px bg-amber-300 dark:bg-amber-600"></div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkApprove}
                  disabled={bulkProcessing}
                  className="bg-green-50 hover:bg-green-100 border-green-200 text-green-700 hover:text-green-800"
                >
                  {bulkProcessing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve Selected ({selectedItems.length})
                    </>
                  )}
                </Button>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedItems([])}
                className="text-amber-600 hover:text-amber-700"
              >
                Clear Selection
              </Button>
            </div>
          )}

          {/* Table */}
          <div className="rounded-lg border border-slate-200 dark:border-slate-700 overflow-hidden">
            <Table>
              <TableHeader className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700">
                <TableRow className="border-b border-slate-200 dark:border-slate-600">
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedItems.length === filteredPayments.length && filteredPayments.length > 0}
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all payments"
                    />
                  </TableHead>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort('userName')} className="font-semibold text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100">
                      User
                      {renderSortIcon('userName')}
                    </Button>
                  </TableHead>
                  <TableHead>Plan</TableHead>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort('investmentAmount')} className="font-semibold text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100">
                      Investment
                      {renderSortIcon('investmentAmount')}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort('paymentAmount')} className="font-semibold text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100">
                      Payout
                      {renderSortIcon('paymentAmount')}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort('status')} className="font-semibold text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100">
                      Status
                      {renderSortIcon('status')}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort('dueDate')} className="font-semibold text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100">
                      Due Date
                      {renderSortIcon('dueDate')}
                    </Button>
                  </TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading
                  ? Array.from({ length: 5 }).map((_, index) => (
                      <TableRow key={index} className="hover:bg-slate-50 dark:hover:bg-slate-800/50">
                        <TableCell><Skeleton className="h-4 w-4" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-[200px]" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                      </TableRow>
                    ))
                  : filteredPayments.length > 0
                  ? filteredPayments.map((payment) => {
                      const isApproveEnabled =
                        payment.investmentId.waitPeriod <= 0 &&
                        payment.paymentStatus === 'ready';
                      const isOverdue = new Date(payment.dueDate) < new Date() && payment.paymentStatus !== 'completed';

                      return (
                        <TableRow
                          key={payment._id}
                          className="hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors group"
                        >
                          <TableCell>
                            <Checkbox
                              checked={selectedItems.includes(payment._id)}
                              onCheckedChange={(checked) => handleSelectItem(payment._id, checked as boolean)}
                              aria-label={`Select payment ${payment._id}`}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                {(payment.userId?.name || 'U').charAt(0)}
                              </div>
                              <div>
                                <div className="font-medium text-slate-900 dark:text-white">
                                  {payment.userId?.name || 'N/A'}
                                </div>
                                <div className="text-sm text-slate-500 dark:text-slate-400">
                                  {payment.userId?.email || 'N/A'}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium text-slate-900 dark:text-white">
                              {payment.planId?.title || 'N/A'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-semibold text-slate-900 dark:text-white">
                              {formatCurrencyDisplay(payment.investmentAmount)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-bold text-green-600 dark:text-green-400">
                              {formatCurrencyDisplay(payment.paymentAmount)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                payment.paymentStatus === 'completed' ? 'default' :
                                payment.paymentStatus === 'ready' ? 'secondary' :
                                isOverdue ? 'destructive' : 'outline'
                              }
                              className={
                                payment.paymentStatus === 'completed' ? 'bg-green-100 text-green-800 border-green-200' :
                                payment.paymentStatus === 'ready' ? 'bg-amber-100 text-amber-800 border-amber-200' :
                                isOverdue ? 'bg-red-100 text-red-800 border-red-200' :
                                'bg-slate-100 text-slate-800 border-slate-200'
                              }
                            >
                              {isOverdue && payment.paymentStatus !== 'completed' ? (
                                <>
                                  <AlertCircle className="h-3 w-3 mr-1" />
                                  Overdue
                                </>
                              ) : payment.paymentStatus === 'completed' ? (
                                <>
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Completed
                                </>
                              ) : payment.paymentStatus === 'ready' ? (
                                <>
                                  <Clock className="h-3 w-3 mr-1" />
                                  Ready
                                </>
                              ) : (
                                <>
                                  <Clock className="h-3 w-3 mr-1" />
                                  Pending
                                </>
                              )}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-slate-900 dark:text-white">
                              {new Date(payment.dueDate).toLocaleDateString()}
                            </div>
                            {isOverdue && payment.paymentStatus !== 'completed' && (
                              <div className="text-xs text-red-500 font-medium">
                                {Math.floor((new Date().getTime() - new Date(payment.dueDate).getTime()) / (1000 * 60 * 60 * 24))} days overdue
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end space-x-2">
                              {payment.paymentStatus !== 'completed' && (
                                <Button
                                  size="sm"
                                  onClick={() => handleApproveButtonClick(payment)}
                                  disabled={!isApproveEnabled}
                                  className={
                                    isApproveEnabled
                                      ? 'bg-green-600 hover:bg-green-700 text-white'
                                      : 'bg-slate-200 text-slate-500 cursor-not-allowed'
                                  }
                                >
                                  {isApproveEnabled ? (
                                    <>
                                      <CheckCircle className="h-4 w-4 mr-1" />
                                      Approve
                                    </>
                                  ) : (
                                    <>
                                      <Clock className="h-4 w-4 mr-1" />
                                      Waiting
                                    </>
                                  )}
                                </Button>
                              )}

                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <Eye className="h-4 w-4 mr-2" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-red-600">
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete Payment
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  : (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-3">
                          <div className="w-16 h-16 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center">
                            <DollarSign className="h-8 w-8 text-slate-400" />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">No payments found</h3>
                            <p className="text-slate-500 dark:text-slate-400">
                              {searchQuery || statusFilter !== 'all' || dateFilter !== 'all'
                                ? 'Try adjusting your filters to see more results.'
                                : 'No payments have been created yet.'}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination would go here if needed */}
          {filteredPayments.length > 0 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-slate-600 dark:text-slate-400">
                Showing {filteredPayments.length} of {payments.length} payment{payments.length !== 1 ? 's' : ''}
              </div>
            </div>
          )}
        </CardContent>

        <ApprovePaymentModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onApprove={handleApprovePayment}
          payment={selectedPayment}
        />
      </Card>
    </div>
  );
};

export default ModernPaymentsTable;
