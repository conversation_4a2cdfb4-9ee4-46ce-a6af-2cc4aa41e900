'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart, Responsive<PERSON>ontainer, XAxis, YAxis, <PERSON>ltip, CartesianGrid } from "recharts";
import { useEffect, useState } from "react";
import { DollarSign, TrendingUp, Calendar, Clock } from 'lucide-react';
import { formatCurrencyDisplay } from '@/lib/currency';

interface ChartDataPoint {
  name: string;
  value: number;
}

interface StatsCardProps {
  title: string;
  value: string;
  description: string;
  icon: React.ReactNode;
  chartData?: ChartDataPoint[];
  trend?: string;
}

function StatsCard({ title, value, description, icon, chartData, trend }: StatsCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="flex items-baseline space-x-2">
          <div className="text-2xl font-bold">{value}</div>
          {trend && (
            <span className={`text-xs ${
              trend.startsWith('+') ? 'text-green-500' : 'text-red-500'
            }`}>
              {trend}
            </span>
          )}
        </div>
        <p className="text-xs text-muted-foreground">{description}</p>
        {chartData && (
          <div className="h-[100px] mt-4">
            <ResponsiveContainer width="100%" height="100%">
              {title.includes('Rate') ? (
                <LineChart data={chartData}>
                  <XAxis 
                    dataKey="name" 
                    tick={{ fontSize: 10 }} 
                    interval={'preserveStartEnd'}
                  />
                  <YAxis 
                    tick={{ fontSize: 10 }}
                    tickFormatter={(value) => `${value}%`}
                  />
                  <CartesianGrid strokeDasharray="3 3" />
                  <Tooltip
                    contentStyle={{ background: 'white', border: 'none' }}
                    labelStyle={{ color: 'black' }}
                  />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#FFB547"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    label={{ 
                      position: 'top', 
                      fill: '#FFB547',
                      fontSize: 10,
                      formatter: (value: number) => `${value.toFixed(1)}%`
                    }}
                  />
                </LineChart>
              ) : (
                <BarChart data={chartData}>
                  <XAxis 
                    dataKey="name" 
                    tick={{ fontSize: 10 }} 
                    interval={'preserveStartEnd'}
                  />
                  <YAxis 
                    tick={{ fontSize: 10 }}
                    tickFormatter={(value) => formatCurrencyDisplay(value)}
                  />
                  <CartesianGrid strokeDasharray="3 3" />
                  <Tooltip
                    contentStyle={{ background: 'white', border: 'none' }}
                    labelStyle={{ color: 'black' }}
                  />
                  <Bar
                    dataKey="value"
                    fill="#FFB547"
                    radius={[4, 4, 0, 0]}
                    label={{ 
                      position: 'top', 
                      fill: '#FFB547',
                      fontSize: 10,
                      formatter: (value: number) => formatCurrencyDisplay(value)
                    }}
                  />
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default function PayoutStats() {
  const [stats, setStats] = useState({
    totalPayouts: '0',
    averagePayout: '0',
    pendingPayouts: '0',
    processingTime: '0',
    trends: {
      total: '+0%',
      average: '+0%',
      pending: '+0%',
      processing: '+0%',
    },
    chartData: {
      totalPayouts: [],
      averagePayout: [],
      pendingPayouts: [],
      processingTime: []
    }
  });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPayoutStats = async () => {
      try {
        const response = await fetch('/api/admin/stats/payouts');
        const data = await response.json();

        setStats({
          totalPayouts: `$${data.totalPayouts.toLocaleString()}`,
          averagePayout: `$${data.averagePayout.toLocaleString()}`,
          pendingPayouts: data.pendingPayouts.toLocaleString(),
          processingTime: `${data.processingTime} hours`,
          trends: data.trends,
          chartData: data.chartData
        });
      } catch (error) {
        console.error('Error fetching payout stats:', error);
        // Fallback data remains the same
      } finally {
        setLoading(false);
      }
    };

    fetchPayoutStats();
  }, []);

  if (loading) {
    return <div>Loading payout stats...</div>;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-4">
      <StatsCard
        title="Total Payouts"
        value={stats.totalPayouts}
        description="Total value of all payouts"
        icon={<DollarSign className="h-4 w-4 text-yellow-600" />}
        chartData={stats.chartData.totalPayouts}
        trend={stats.trends.total}
      />
      <StatsCard
        title="Average Payout"
        value={stats.averagePayout}
        description="Average payout amount"
        icon={<TrendingUp className="h-4 w-4 text-yellow-600" />}
        chartData={stats.chartData.averagePayout}
        trend={stats.trends.average}
      />
      <StatsCard
        title="Pending Payouts"
        value={stats.pendingPayouts}
        description="Number of pending payouts"
        icon={<Clock className="h-4 w-4 text-yellow-600" />}
        chartData={stats.chartData.pendingPayouts}
        trend={stats.trends.pending}
      />
      <StatsCard
        title="Avg. Processing Time"
        value={stats.processingTime}
        description="Average time to process payouts"
        icon={<Calendar className="h-4 w-4 text-yellow-600" />}
        chartData={stats.chartData.processingTime}
        trend={stats.trends.processing}
      />
    </div>
  );
}

