'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Line<PERSON>hart, ResponsiveContainer } from "recharts";
import { useEffect, useState } from "react";
import { DollarSign, TrendingUp, <PERSON>, Pie<PERSON>hart } from 'lucide-react';
import { formatCurrencyDisplay } from '@/lib/currency';

interface ChartDataPoint {
    name: string;
    value: number;
  }

interface StatsCardProps {
  title: string;
  value: string;
  description: string;
  icon: React.ReactNode;
//   chartData?: any[];
  chartData?: ChartDataPoint[];
  trend?: string;
}

function StatsCard({ title, value, description, icon, chartData, trend }: StatsCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="flex items-baseline space-x-2">
          <div className="text-2xl font-bold">{value}</div>
          {trend && (
            <span className={`text-xs ${
              trend.startsWith('+') ? 'text-green-500' : 'text-red-500'
            }`}>
              {trend}
            </span>
          )}
        </div>
        <p className="text-xs text-muted-foreground">{description}</p>
        {chartData && (
          <div className="h-[80px] mt-4">
            <ResponsiveContainer width="100%" height="100%">
              {title.includes('Rate') ? (
                <LineChart data={chartData}>
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#FFB547"
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              ) : (
                <BarChart data={chartData}>
                  <Bar
                    dataKey="value"
                    fill="#FFB547"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default function InvestmentStats() {
  const [stats, setStats] = useState({
    totalValue: '0',
    averageSize: '0',
    successRate: '0',
    activeInvestments: '0',
    trends: {
      value: '+0%',
      size: '+0%',
      success: '+0%',
      active: '+0%',
    }
  });

  const [loading, setLoading] = useState(true);

  // Mock chart data - replace with real data from your API
  const mockChartData = [
    { name: 'Jan', value: 400 },
    { name: 'Feb', value: 300 },
    { name: 'Mar', value: 600 },
    { name: 'Apr', value: 500 },
    { name: 'May', value: 700 },
  ];

  useEffect(() => {
    const fetchInvestmentStats = async () => {
      try {
        const response = await fetch('/api/admin/stats/investments/detailed');
        const data = await response.json();

        setStats({
          totalValue: formatCurrencyDisplay(data.totalValue),
          averageSize: formatCurrencyDisplay(data.averageSize),
          successRate: `${data.successRate}%`,
          activeInvestments: data.activeInvestments.toLocaleString(),
          trends: data.trends
        });
      } catch (error) {
        console.error('Error fetching investment stats:', error);
        // Fallback data
        setStats({
          totalValue: 'R2.5M',
          averageSize: 'R25,000',
          successRate: '85',
          activeInvestments: '156',
          trends: {
            value: '+12.5%',
            size: '+5.2%',
            success: '+2.3%',
            active: '+8.7%'
          }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchInvestmentStats();
  }, []);

  if (loading) {
    return <div>Loading investment stats...</div>;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-4">
      <StatsCard
        title="Total Investment Value"
        value={stats.totalValue}
        description="Total value of all investments"
        icon={<DollarSign className="h-4 w-4 text-yellow-600" />}
        chartData={mockChartData}
        trend={stats.trends.value}
      />
      <StatsCard
        title="Average Investment Size"
        value={stats.averageSize}
        description="Average investment amount"
        icon={<PieChart className="h-4 w-4 text-yellow-600" />}
        chartData={mockChartData}
        trend={stats.trends.size}
      />
      <StatsCard
        title="Success Rate"
        value={`${stats.successRate}%`}
        description="Approved investments rate"
        icon={<TrendingUp className="h-4 w-4 text-yellow-600" />}
        chartData={mockChartData}
        trend={stats.trends.success}
      />
      <StatsCard
        title="Active Investments"
        value={stats.activeInvestments}
        description="Currently active investments"
        icon={<Clock className="h-4 w-4 text-yellow-600" />}
        chartData={mockChartData}
        trend={stats.trends.active}
      />
    </div>
  );
}

