

'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON>, <PERSON><PERSON><PERSON>, Line, LineChart, ResponsiveContainer, XAxis, YAxis, Tooltip, CartesianGrid } from "recharts";
import { useEffect, useState } from "react";
import { Users, TrendingUp, DollarSign, Share2 } from 'lucide-react';

interface ChartDataPoint {
  name: string;
  value: number;
}

interface StatsCardProps {
  title: string;
  value: string;
  description: string;
  icon: React.ReactNode;
  chartData?: ChartDataPoint[];
  trend?: string;
}

function StatsCard({ title, value, description, icon, chartData, trend }: StatsCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="flex items-baseline space-x-2">
          <div className="text-2xl font-bold">{value}</div>
          {trend && (
            <span className={`text-xs ${
              trend.startsWith('+') ? 'text-green-500' : 'text-red-500'
            }`}>
              {trend}
            </span>
          )}
        </div>
        <p className="text-xs text-muted-foreground">{description}</p>
        {chartData && (
          <div className="h-[100px] mt-4">
            <ResponsiveContainer width="100%" height="100%">
              {title.includes('Rate') ? (
                <LineChart data={chartData}>
                  <XAxis 
                    dataKey="name" 
                    tick={{ fontSize: 10 }} 
                    interval={'preserveStartEnd'}
                  />
                  <YAxis 
                    tick={{ fontSize: 10 }}
                    tickFormatter={(value) => `${value}%`}
                  />
                  <CartesianGrid strokeDasharray="3 3" />
                  <Tooltip
                    contentStyle={{ background: 'white', border: 'none' }}
                    labelStyle={{ color: 'black' }}
                  />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#FFB547"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    label={{ 
                      position: 'top', 
                      fill: '#FFB547',
                      fontSize: 10,
                      formatter: (value: number) => `${value.toFixed(1)}%`
                    }}
                  />
                </LineChart>
              ) : (
                <BarChart data={chartData}>
                  <XAxis 
                    dataKey="name" 
                    tick={{ fontSize: 10 }} 
                    interval={'preserveStartEnd'}
                  />
                  <YAxis 
                    tick={{ fontSize: 10 }}
                    tickFormatter={(value) => title.includes('Earnings') ? `$${value}` : value}
                  />
                  <CartesianGrid strokeDasharray="3 3" />
                  <Tooltip
                    contentStyle={{ background: 'white', border: 'none' }}
                    labelStyle={{ color: 'black' }}
                  />
                  <Bar
                    dataKey="value"
                    fill="#FFB547"
                    radius={[4, 4, 0, 0]}
                    label={{ 
                      position: 'top', 
                      fill: '#FFB547',
                      fontSize: 10,
                      formatter: (value: number) => title.includes('Earnings') ? `$${value}` : value
                    }}
                  />
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default function ReferralStats() {
  const [stats, setStats] = useState({
    totalReferrals: '0',
    activeReferrers: '0',
    totalEarnings: '0',
    conversionRate: '0',
    trends: {
      total: '+0%',
      active: '+0%',
      earnings: '+0%',
      conversion: '+0%',
    },
    chartData: {
      totalReferrals: [],
      activeReferrers: [],
      totalEarnings: [],
      conversionRate: []
    }
  });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchReferralStats = async () => {
      try {
        const response = await fetch('/api/admin/stats/referrals/detailed');
        const data = await response.json();

        setStats({
          totalReferrals: data.totalReferrals.toLocaleString(),
          activeReferrers: data.activeReferrers.toLocaleString(),
          totalEarnings: `$${data.totalEarnings.toLocaleString()}`,
          conversionRate: `${data.conversionRate.toFixed(1)}%`,
          trends: data.trends,
          chartData: data.chartData
        });
      } catch (error) {
        console.error('Error fetching referral stats:', error);
        // Fallback data remains the same
      } finally {
        setLoading(false);
      }
    };

    fetchReferralStats();
  }, []);

  if (loading) {
    return <div>Loading referral stats...</div>;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-4">
      <StatsCard
        title="Total Referrals"
        value={stats.totalReferrals}
        description="Total referrals to date"
        icon={<Share2 className="h-4 w-4 text-yellow-600" />}
        chartData={stats.chartData.totalReferrals}
        trend={stats.trends.total}
      />
      <StatsCard
        title="Active Referrers"
        value={stats.activeReferrers}
        description="Users with active referrals"
        icon={<Users className="h-4 w-4 text-yellow-600" />}
        chartData={stats.chartData.activeReferrers}
        trend={stats.trends.active}
      />
      <StatsCard
        title="Total Earnings"
        value={stats.totalEarnings}
        description="Total referral earnings"
        icon={<DollarSign className="h-4 w-4 text-yellow-600" />}
        chartData={stats.chartData.totalEarnings}
        trend={stats.trends.earnings}
      />
      <StatsCard
        title="Conversion Rate"
        value={stats.conversionRate}
        description="Referral to investment conversion"
        icon={<TrendingUp className="h-4 w-4 text-yellow-600" />}
        chartData={stats.chartData.conversionRate}
        trend={stats.trends.conversion}
      />
    </div>
  );
}

