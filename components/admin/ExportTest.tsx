'use client';

import React from 'react';
import { downloadUsersPDF } from '@/lib/pdfExport';
import { downloadUsersExcel } from '@/lib/excelExport';
import { Button } from '@/components/ui/button';
import { FileText, FileSpreadsheet } from 'lucide-react';

// Sample test data
const sampleUsers = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+**********',
    role: 'investor',
    status: 'Active',
    totalInvestments: 3,
    totalInvestmentAmount: 15000,
    referralCode: 'JD001',
    bankName: 'First National Bank',
    accountNumber: '**********',
    accountName: '<PERSON>',
    joinDate: '2024-01-15',
    lastActivity: '2024-06-15'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+**********',
    role: 'member',
    status: 'Active',
    totalInvestments: 1,
    totalInvestmentAmount: 5000,
    referralCode: 'JS002',
    bankName: 'Community Bank',
    accountNumber: '**********',
    accountName: '<PERSON>',
    joinDate: '2024-02-20',
    lastActivity: '2024-06-10'
  },
  {
    name: 'Admin User',
    email: '<EMAIL>',
    phone: '+**********',
    role: 'admin',
    status: 'Active',
    totalInvestments: 0,
    totalInvestmentAmount: 0,
    referralCode: 'ADMIN',
    bankName: 'N/A',
    accountNumber: 'N/A',
    accountName: 'N/A',
    joinDate: '2024-01-01',
    lastActivity: '2024-06-18'
  }
];

const ExportTest: React.FC = () => {
  const handleTestPDF = () => {
    try {
      downloadUsersPDF(sampleUsers, {
        search: '',
        role: 'all',
        status: 'all'
      });
      console.log('PDF export test completed');
    } catch (error) {
      console.error('PDF export test failed:', error);
    }
  };

  const handleTestExcel = () => {
    try {
      downloadUsersExcel(sampleUsers, {
        search: '',
        role: 'all',
        status: 'all'
      });
      console.log('Excel export test completed');
    } catch (error) {
      console.error('Excel export test failed:', error);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Export Functionality Test</h2>
      <p className="text-gray-600 mb-6">
        Test the PDF and Excel export functionality with sample data.
      </p>
      
      <div className="flex space-x-4">
        <Button onClick={handleTestPDF} className="flex items-center space-x-2">
          <FileText className="h-4 w-4" />
          <span>Test PDF Export</span>
        </Button>
        
        <Button onClick={handleTestExcel} variant="outline" className="flex items-center space-x-2">
          <FileSpreadsheet className="h-4 w-4" />
          <span>Test Excel Export</span>
        </Button>
      </div>
      
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">Sample Data Preview:</h3>
        <div className="text-sm text-gray-600">
          <p>• {sampleUsers.length} sample users</p>
          <p>• Mixed roles: Admin, Investor, Member</p>
          <p>• Various investment amounts and statuses</p>
          <p>• Complete bank details and referral codes</p>
        </div>
      </div>
    </div>
  );
};

export default ExportTest;
