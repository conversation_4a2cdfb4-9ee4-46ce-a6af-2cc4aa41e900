

// components/admin/InvestmentTable.tsx
'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowUpDown, Search, Trash2, Download, FileText } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { PaymentStatusModal } from './payment-status-modal';
import { toast } from 'react-hot-toast';
import { formatCurrencyDisplay } from '@/lib/currency';

interface Investment {
  _id: string;
  userId: { name: string; email: string };
  planId: { title: string; percentage: number };
  amount: number;
  earningRate: number;
  paymentStatus: string;
  startDate: string;
  endDate: string;
  paymentDate: string;
}

const InvestmentTable: React.FC = () => {
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedInvestment, setSelectedInvestment] = useState<Investment | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [deleting, setDeleting] = useState(false);
  const [exporting, setExporting] = useState(false);

  useEffect(() => {
    fetchInvestments();
  }, [currentPage, searchQuery, sortColumn, sortOrder]);

  const fetchInvestments = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/investments/admin?page=${currentPage}&search=${searchQuery}&sort=${sortColumn}&order=${sortOrder}`
      );
      const data = await response.json();
      setInvestments(data.investments);
      setTotalPages(data.totalPages);
    } catch (error) {
      console.error('Error fetching investments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (column: string) => {
    setSortOrder(sortColumn === column && sortOrder === 'asc' ? 'desc' : 'asc');
    setSortColumn(column);
  };

  const renderSortIcon = () => <ArrowUpDown className="ml-2 h-4 w-4" />;

  const renderTableHeader = (title: string, column: string) => (
    <TableHead>
      <Button variant="ghost" onClick={() => handleSort(column)}>
        {title}
        {renderSortIcon()}
      </Button>
    </TableHead>
  );

  const renderSkeletonRow = () => (
    <TableRow>
      <TableCell>
        <Skeleton className="h-4 w-4" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[250px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[100px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[100px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[60px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
    </TableRow>
  );

  const getBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved':
        return 'outline';
      case 'pending':
        return 'secondary';
      default:
        return 'destructive';
    }
  };

  const handlePaymentStatusClick = (investment: Investment) => {
    setSelectedInvestment(investment);
    setIsModalOpen(true);
  };

  const updateInvestmentStatus = async (newStatus: string) => {
    if (selectedInvestment) {
      try {
        const response = await fetch(
          `/api/investments/${selectedInvestment._id}/status`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ newStatus }),
          }
        );
        if (response.ok) {
          // Update the local state
          setInvestments((prevInvestments) =>
            prevInvestments.map((inv) =>
              inv._id === selectedInvestment._id
                ? { ...inv, paymentStatus: newStatus }
                : inv
            )
          );
          setIsModalOpen(false);
        } else {
          console.error(`Failed to update payment status to ${newStatus}`);
        }
      } catch (error) {
        console.error(`Error updating payment status to ${newStatus}:`, error);
      }
    }
  };



  const handleApprovePayment = () => updateInvestmentStatus('approved');
  const handleDeclinePayment = () => updateInvestmentStatus('declined');
//   const handlePendingWithNotice = () => updateInvestmentStatus('pendingWithNotice');
  const handleDeclinedWithReason = () => updateInvestmentStatus('declinedWithReason');

  // Bulk selection functions
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(investments.map(inv => inv._id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
    }
  };

  // Bulk delete function
  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;

    const confirmMessage = `⚠️ DELETE CONFIRMATION\n\nYou are about to permanently delete ${selectedItems.length} investment(s).\n\nThis action cannot be undone and will remove:\n• Investment records\n• Associated transaction data\n• Payment history\n\nAre you sure you want to proceed?`;

    if (confirm(confirmMessage)) {
      setDeleting(true);

      try {
        console.log('Deleting investments:', selectedItems);

        const response = await fetch('/api/investments/bulk-delete', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ids: selectedItems })
        });

        const data = await response.json();

        if (response.ok) {
          // Remove deleted items from local state immediately for better UX
          setInvestments(prev => prev.filter(inv => !selectedItems.includes(inv._id)));
          setSelectedItems([]);

          // Show success message
          toast.success(`Successfully deleted ${data.deletedCount} investment(s)! 🗑️`);

          // Refresh the data to ensure consistency
          await fetchInvestments();
        } else {
          console.error('Delete failed:', data);
          toast.error('Failed to delete investments. Please try again.');
        }
      } catch (error) {
        console.error('Error deleting investments:', error);
        const networkErrorMessage = `🌐 NETWORK ERROR\n\nFailed to connect to the server.\n\nPlease check your internet connection and try again.`;
        toast.error(networkErrorMessage);
      } finally {
        setDeleting(false);
      }
    }
  };

  return (
    <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-amber-200/50 dark:border-amber-800/30 shadow-xl">
      <CardHeader className="border-b border-amber-200/50 dark:border-amber-800/30">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-bold text-slate-900 dark:text-white">Investment Transactions</CardTitle>
            <p className="text-slate-600 dark:text-slate-400 mt-1">Monitor and manage all investment activities</p>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-slate-600 dark:text-slate-400">Live</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="relative w-80">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-amber-500" />
            <Input
              type="text"
              placeholder="Search by user, plan, or amount..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 border-amber-200 focus:border-amber-500 focus:ring-amber-500/20"
            />
          </div>

          <div className="flex items-center space-x-3">
            {/* Bulk Actions */}
            {selectedItems.length > 0 && (
              <div className="flex items-center space-x-2 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 px-4 py-2 rounded-lg border border-amber-200 dark:border-amber-800 shadow-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-semibold text-amber-700 dark:text-amber-300">
                    {selectedItems.length} investment{selectedItems.length !== 1 ? 's' : ''} selected
                  </span>
                </div>
                <div className="h-4 w-px bg-amber-300 dark:bg-amber-600"></div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkDelete}
                  disabled={deleting || selectedItems.length === 0}
                  className="bg-red-50 hover:bg-red-100 border-red-200 text-red-700 hover:text-red-800"
                >
                  {deleting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin mr-2" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete ({selectedItems.length})
                    </>
                  )}
                </Button>
              </div>
            )}

            <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
              <span>Total: {investments.length} investments</span>
            </div>
          </div>
        </div>

        <div className="rounded-xl border border-amber-200/50 dark:border-amber-800/30 overflow-hidden bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm">
          <Table>
            <TableHeader className="bg-amber-50/50 dark:bg-amber-900/10">
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedItems.length === investments.length && investments.length > 0}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all investments"
                  />
                </TableHead>
                <TableHead>
                  <Button variant="ghost" onClick={() => handleSort('userId')}>
                    User
                    {renderSortIcon()}
                  </Button>
                </TableHead>
                <TableHead>
                  <Button variant="ghost" onClick={() => handleSort('planId')}>
                    Plan
                    {renderSortIcon()}
                  </Button>
                </TableHead>
                <TableHead>
                  <Button variant="ghost" onClick={() => handleSort('amount')}>
                    Amount
                    {renderSortIcon()}
                  </Button>
                </TableHead>
                <TableHead>
                  <Button variant="ghost" onClick={() => handleSort('paymentStatus')}>
                    Payment Status
                    {renderSortIcon()}
                  </Button>
                </TableHead>
                <TableHead>Earning Rate</TableHead>
                <TableHead>
                  <Button variant="ghost" onClick={() => handleSort('startDate')}>
                    Start Date
                    {renderSortIcon()}
                  </Button>
                </TableHead>
                <TableHead>
                  <Button variant="ghost" onClick={() => handleSort('endDate')}>
                    End Date
                    {renderSortIcon()}
                  </Button>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading
                ? Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>{renderSkeletonRow()}</TableRow>
                  ))
                : investments.length > 0
                ? investments.map((investment) => (
                    <TableRow key={investment._id} className="hover:bg-amber-50/50 dark:hover:bg-amber-900/10 transition-colors">
                      <TableCell>
                        <Checkbox
                          checked={selectedItems.includes(investment._id)}
                          onCheckedChange={(checked) => handleSelectItem(investment._id, checked as boolean)}
                          aria-label={`Select investment ${investment._id}`}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-amber-500 to-yellow-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                            {(investment.userId?.name || 'U').charAt(0)}
                          </div>
                          <div>
                            <div className="font-medium text-slate-900 dark:text-white">
                              {investment.userId?.name || 'N/A'}
                            </div>
                            <div className="text-sm text-slate-600 dark:text-slate-400">
                              {investment.userId?.email || 'N/A'}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-slate-700 dark:text-slate-300 font-medium">
                        {investment.planId?.title || 'N/A'}
                      </TableCell>
                      <TableCell className="text-slate-900 dark:text-white">
                        <div className="font-bold text-lg">{formatCurrencyDisplay(investment.amount)}</div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={getBadgeVariant(investment.paymentStatus)}
                          className={`cursor-pointer border-0 font-semibold ${
                            investment.paymentStatus === 'approved'
                              ? 'bg-green-100 text-green-700 hover:bg-green-200'
                              : investment.paymentStatus === 'pending'
                              ? 'bg-amber-100 text-amber-700 hover:bg-amber-200'
                              : 'bg-red-100 text-red-700 hover:bg-red-200'
                          }`}
                          onClick={() => handlePaymentStatusClick(investment)}
                        >
                          {investment.paymentStatus}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-slate-700 dark:text-slate-300 font-semibold">
                        {investment.earningRate}%
                      </TableCell>
                      <TableCell className="text-slate-600 dark:text-slate-400">
                        <div className="text-sm">
                          {new Date(investment.startDate).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell className="text-slate-600 dark:text-slate-400">
                        <div className="text-sm">
                          {new Date(investment.endDate).toLocaleDateString()}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center text-muted-foreground">
                      No investments found
                    </TableCell>
                  </TableRow>
                )}
            </TableBody>
          </Table>
        </div>

        <div className="flex items-center justify-between py-6 border-t border-amber-200/50 dark:border-amber-800/30">
          <div className="text-sm text-slate-600 dark:text-slate-400">
            Showing {investments.length} of {investments.length} investments
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="border-amber-200 hover:bg-amber-50 hover:border-amber-300"
            >
              Previous
            </Button>
            <div className="flex items-center gap-1">
              <span className="text-sm text-slate-600 dark:text-slate-400">Page {currentPage} of {totalPages}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="border-amber-200 hover:bg-amber-50 hover:border-amber-300"
            >
              Next
            </Button>
          </div>
        </div>

        <PaymentStatusModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          investment={selectedInvestment}
          onApprove={handleApprovePayment}
          onDecline={handleDeclinePayment}
        //   onPendingWithNotice={handlePendingWithNotice}
          onDeclinedWithReason={handleDeclinedWithReason}
        />
      </CardContent>
    </Card>
  );
};

export default InvestmentTable;
