

// components/admin/InvestmentTable.tsx
'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowUpDown, Search } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { PaymentStatusModal } from './payment-status-modal';

interface Investment {
  _id: string;
  userId: { name: string; email: string };
  planId: { title: string; percentage: number };
  amount: number;
  earningRate: number;
  paymentStatus: string;
  startDate: string;
  endDate: string;
  paymentDate: string;
}

const InvestmentTable: React.FC = () => {
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedInvestment, setSelectedInvestment] = useState<Investment | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    fetchInvestments();
  }, [currentPage, searchQuery, sortColumn, sortOrder]);

  const fetchInvestments = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/investments/admin?page=${currentPage}&search=${searchQuery}&sort=${sortColumn}&order=${sortOrder}`
      );
      const data = await response.json();
      setInvestments(data.investments);
      setTotalPages(data.totalPages);
    } catch (error) {
      console.error('Error fetching investments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (column: string) => {
    setSortOrder(sortColumn === column && sortOrder === 'asc' ? 'desc' : 'asc');
    setSortColumn(column);
  };

  const renderSortIcon = () => <ArrowUpDown className="ml-2 h-4 w-4" />;

  const renderTableHeader = (title: string, column: string) => (
    <TableHead>
      <Button variant="ghost" onClick={() => handleSort(column)}>
        {title}
        {renderSortIcon()}
      </Button>
    </TableHead>
  );

  const renderSkeletonRow = () => (
    <TableRow>
      <TableCell>
        <Skeleton className="h-4 w-[250px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[100px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[100px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[60px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
    </TableRow>
  );

  const getBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved':
        return 'outline';
      case 'pending':
        return 'secondary';
      default:
        return 'destructive';
    }
  };

  const handlePaymentStatusClick = (investment: Investment) => {
    setSelectedInvestment(investment);
    setIsModalOpen(true);
  };

  const updateInvestmentStatus = async (newStatus: string) => {
    if (selectedInvestment) {
      try {
        const response = await fetch(
          `/api/investments/${selectedInvestment._id}/status`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ newStatus }),
          }
        );
        if (response.ok) {
          // Update the local state
          setInvestments((prevInvestments) =>
            prevInvestments.map((inv) =>
              inv._id === selectedInvestment._id
                ? { ...inv, paymentStatus: newStatus }
                : inv
            )
          );
          setIsModalOpen(false);
        } else {
          console.error(`Failed to update payment status to ${newStatus}`);
        }
      } catch (error) {
        console.error(`Error updating payment status to ${newStatus}:`, error);
      }
    }
  };



  const handleApprovePayment = () => updateInvestmentStatus('approved');
  const handleDeclinePayment = () => updateInvestmentStatus('declined');
//   const handlePendingWithNotice = () => updateInvestmentStatus('pendingWithNotice');
  const handleDeclinedWithReason = () => updateInvestmentStatus('declinedWithReason');

  return (
    <Card>
      <CardHeader>
        <CardTitle>Partner payments</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-4">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search by user"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {renderTableHeader('User', 'userId')}
              {renderTableHeader('Plan', 'planId')}
              {renderTableHeader('Amount', 'amount')}
              {renderTableHeader('Payment Status', 'paymentStatus')}
              <TableHead>Earning Rate</TableHead>
              {renderTableHeader('Start Date', 'startDate')}
              {renderTableHeader('End Date', 'endDate')}
            </TableHeader>
            <TableBody>
              {loading
                ? Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>{renderSkeletonRow()}</TableRow>
                  ))
                : investments.length > 0
                ? investments.map((investment) => (
                    <TableRow key={investment._id}>
                      <TableCell>
                        <div className="font-medium">
                          {investment.userId?.name || 'N/A'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {investment.userId?.email || 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell>{investment.planId?.title || 'N/A'}</TableCell>
                      <TableCell>
                        ${investment.amount.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={getBadgeVariant(investment.paymentStatus)}
                          className="cursor-pointer"
                          onClick={() => handlePaymentStatusClick(investment)}
                        >
                          {investment.paymentStatus}
                        </Badge>
                      </TableCell>
                      <TableCell>{investment.earningRate}%</TableCell>
                      <TableCell>
                        {new Date(investment.startDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {new Date(investment.endDate).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  ))
                : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center text-muted-foreground">
                      No investments found
                    </TableCell>
                  </TableRow>
                )}
            </TableBody>
          </Table>
        </div>

        <div className="flex items-center justify-end space-x-2 py-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>

        <PaymentStatusModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          investment={selectedInvestment}
          onApprove={handleApprovePayment}
          onDecline={handleDeclinePayment}
        //   onPendingWithNotice={handlePendingWithNotice}
          onDeclinedWithReason={handleDeclinedWithReason}
        />
      </CardContent>
    </Card>
  );
};

export default InvestmentTable;
