

// components/admin/InvestmentTable.tsx
'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowUpDown, Search } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { PaymentStatusModal } from './payment-status-modal';

interface Investment {
  _id: string;
  userId: { name: string; email: string };
  planId: { title: string; percentage: number };
  amount: number;
  earningRate: number;
  paymentStatus: string;
  startDate: string;
  endDate: string;
  paymentDate: string;
}

const InvestmentTable: React.FC = () => {
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedInvestment, setSelectedInvestment] = useState<Investment | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    fetchInvestments();
  }, [currentPage, searchQuery, sortColumn, sortOrder]);

  const fetchInvestments = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/investments/admin?page=${currentPage}&search=${searchQuery}&sort=${sortColumn}&order=${sortOrder}`
      );
      const data = await response.json();
      setInvestments(data.investments);
      setTotalPages(data.totalPages);
    } catch (error) {
      console.error('Error fetching investments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (column: string) => {
    setSortOrder(sortColumn === column && sortOrder === 'asc' ? 'desc' : 'asc');
    setSortColumn(column);
  };

  const renderSortIcon = () => <ArrowUpDown className="ml-2 h-4 w-4" />;

  const renderTableHeader = (title: string, column: string) => (
    <TableHead>
      <Button variant="ghost" onClick={() => handleSort(column)}>
        {title}
        {renderSortIcon()}
      </Button>
    </TableHead>
  );

  const renderSkeletonRow = () => (
    <TableRow>
      <TableCell>
        <Skeleton className="h-4 w-[250px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[100px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[100px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[60px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
    </TableRow>
  );

  const getBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved':
        return 'outline';
      case 'pending':
        return 'secondary';
      default:
        return 'destructive';
    }
  };

  const handlePaymentStatusClick = (investment: Investment) => {
    setSelectedInvestment(investment);
    setIsModalOpen(true);
  };

  const updateInvestmentStatus = async (newStatus: string) => {
    if (selectedInvestment) {
      try {
        const response = await fetch(
          `/api/investments/${selectedInvestment._id}/status`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ newStatus }),
          }
        );
        if (response.ok) {
          // Update the local state
          setInvestments((prevInvestments) =>
            prevInvestments.map((inv) =>
              inv._id === selectedInvestment._id
                ? { ...inv, paymentStatus: newStatus }
                : inv
            )
          );
          setIsModalOpen(false);
        } else {
          console.error(`Failed to update payment status to ${newStatus}`);
        }
      } catch (error) {
        console.error(`Error updating payment status to ${newStatus}:`, error);
      }
    }
  };



  const handleApprovePayment = () => updateInvestmentStatus('approved');
  const handleDeclinePayment = () => updateInvestmentStatus('declined');
//   const handlePendingWithNotice = () => updateInvestmentStatus('pendingWithNotice');
  const handleDeclinedWithReason = () => updateInvestmentStatus('declinedWithReason');

  return (
    <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-amber-200/50 dark:border-amber-800/30 shadow-xl">
      <CardHeader className="border-b border-amber-200/50 dark:border-amber-800/30">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-bold text-slate-900 dark:text-white">Investment Transactions</CardTitle>
            <p className="text-slate-600 dark:text-slate-400 mt-1">Monitor and manage all investment activities</p>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-slate-600 dark:text-slate-400">Live</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="relative w-80">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-amber-500" />
            <Input
              type="text"
              placeholder="Search by user, plan, or amount..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 border-amber-200 focus:border-amber-500 focus:ring-amber-500/20"
            />
          </div>
          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
            <span>Total: {investments.length} investments</span>
          </div>
        </div>

        <div className="rounded-xl border border-amber-200/50 dark:border-amber-800/30 overflow-hidden bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm">
          <Table>
            <TableHeader className="bg-amber-50/50 dark:bg-amber-900/10">
              {renderTableHeader('User', 'userId')}
              {renderTableHeader('Plan', 'planId')}
              {renderTableHeader('Amount', 'amount')}
              {renderTableHeader('Payment Status', 'paymentStatus')}
              <TableHead>Earning Rate</TableHead>
              {renderTableHeader('Start Date', 'startDate')}
              {renderTableHeader('End Date', 'endDate')}
            </TableHeader>
            <TableBody>
              {loading
                ? Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>{renderSkeletonRow()}</TableRow>
                  ))
                : investments.length > 0
                ? investments.map((investment) => (
                    <TableRow key={investment._id}>
                      <TableCell>
                        <div className="font-medium">
                          {investment.userId?.name || 'N/A'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {investment.userId?.email || 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell>{investment.planId?.title || 'N/A'}</TableCell>
                      <TableCell>
                        ${investment.amount.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={getBadgeVariant(investment.paymentStatus)}
                          className="cursor-pointer"
                          onClick={() => handlePaymentStatusClick(investment)}
                        >
                          {investment.paymentStatus}
                        </Badge>
                      </TableCell>
                      <TableCell>{investment.earningRate}%</TableCell>
                      <TableCell>
                        {new Date(investment.startDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {new Date(investment.endDate).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  ))
                : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center text-muted-foreground">
                      No investments found
                    </TableCell>
                  </TableRow>
                )}
            </TableBody>
          </Table>
        </div>

        <div className="flex items-center justify-between py-6 border-t border-amber-200/50 dark:border-amber-800/30">
          <div className="text-sm text-slate-600 dark:text-slate-400">
            Showing {investments.length} of {investments.length} investments
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="border-amber-200 hover:bg-amber-50 hover:border-amber-300"
            >
              Previous
            </Button>
            <div className="flex items-center gap-1">
              <span className="text-sm text-slate-600 dark:text-slate-400">Page {currentPage} of {totalPages}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="border-amber-200 hover:bg-amber-50 hover:border-amber-300"
            >
              Next
            </Button>
          </div>
        </div>

        <PaymentStatusModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          investment={selectedInvestment}
          onApprove={handleApprovePayment}
          onDecline={handleDeclinePayment}
        //   onPendingWithNotice={handlePendingWithNotice}
          onDeclinedWithReason={handleDeclinedWithReason}
        />
      </CardContent>
    </Card>
  );
};

export default InvestmentTable;
