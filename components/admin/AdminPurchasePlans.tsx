
// components/admin/AdminPurchasePlans.tsx


'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { Plan } from '@/types/plan';
import { useAuth } from '@/context/AuthContext';
import PlanSubscriptionOverlay from '../home/<USER>';
import UserLogin from '../UserLogin';
import CreatePlan from '../CreatePlan';
import SpinnerLoader from '../SpinnerLoader';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Plus } from 'lucide-react';
import { formatCurrencyDisplay, formatCurrencyRange, parseCurrency } from '@/lib/currency';

/**
 * Submit subscription using cookie-based authentication.
 * We do NOT include any Bearer token in the headers since the
 * server expects to read the accessToken from cookies for `x-client-type: 'web'`.
 */
const submitSubscription = async (
  planId: string,
  amount: number,
  planDetails: {
    planTitle: string;
    priceRange: string;
    percentage: string;
    profit: string;
    capitalReturn: string;
    totalPercentage: string;
    planType: string;
  }
) => {
  try {
    const res = await fetch('/api/plans/subscribe', {
      method: 'POST',
      credentials: 'include',          // <--- Important for sending cookies
      headers: {
        'Content-Type': 'application/json',
        'x-client-type': 'web',        // <--- Tells backend to use cookie-based auth
      },
      body: JSON.stringify({
        planId,
        amount,
        ...planDetails,
      }),
    });

    const data = await res.json();
    if (!res.ok) {
      console.error('Failed to submit subscription:', data);
      return { error: data.error || 'Unknown error occurred' };
    }

    return { success: 'Subscription successful', data };
  } catch (error) {
    console.error('Error submitting subscription:', error);
    return { error: 'Failed to submit subscription' };
  }
};

// Helper function to format price range from USD to ZAR
const formatPriceRange = (priceRange: string): string => {
  // If the price range already contains ZAR symbol (R), return as is
  if (priceRange.includes('R')) {
    return priceRange;
  }

  // If it contains $ symbols, replace with R
  if (priceRange.includes('$')) {
    return priceRange.replace(/\$/g, 'R');
  }

  // If it's a range like "1000 - 5000", format it properly
  const rangeMatch = priceRange.match(/(\d+(?:,\d{3})*)\s*-\s*(\d+(?:,\d{3})*)/);
  if (rangeMatch) {
    const minAmount = parseCurrency(rangeMatch[1]);
    const maxAmount = parseCurrency(rangeMatch[2]);
    return formatCurrencyRange(minAmount, maxAmount);
  }

  // If it's a single number, format it as currency
  const singleAmount = parseCurrency(priceRange);
  if (singleAmount > 0) {
    return formatCurrencyDisplay(singleAmount);
  }

  // Fallback: return the original string
  return priceRange;
};

export default function AdminPurchasePlans() {
  const router = useRouter();

  // State for plans and filter
  const [plans, setPlans] = useState<Plan[]>([]);
  const [filteredPlans, setFilteredPlans] = useState<Plan[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  // Overlays and modal state
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [isLoginOverlayOpen, setIsLoginOverlayOpen] = useState(false);
  const [isSubscriptionOverlayOpen, setIsSubscriptionOverlayOpen] = useState(false);
  const [isCreatePlanOverlayOpen, setIsCreatePlanOverlayOpen] = useState(false);

  // Auth state (only 'user' is needed, as the token is stored via HttpOnly cookie)
  const { user } = useAuth();

  // UI state
  const [loginSuccess, setLoginSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch available plans from the server.
   * Not a protected call, so no special headers beyond JSON are needed here.
   */
  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/plans');
      const data = await response.json();

      if (Array.isArray(data)) {
        setPlans(data);
        setFilteredPlans(data);
      } else {
        console.error('Data is not an array:', data);
        setPlans([]);
        setFilteredPlans([]);
      }
    } catch (error) {
      console.error('Failed to fetch plans:', error);
      setPlans([]);
      setFilteredPlans([]);
    }
  };

  useEffect(() => {
    fetchPlans();
  }, []);

  /**
   * Filter plans by title or planType whenever `searchTerm` or `plans` changes.
   */
  useEffect(() => {
    const filtered = plans.filter(
      (plan) =>
        plan.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plan.planType?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredPlans(filtered);
  }, [searchTerm, plans]);

  /**
   * If user is not logged in, open login overlay before subscription.
   * Otherwise, open the subscription overlay.
   */
  const openOverlay = (plan: Plan) => {
    if (!user) {
      setIsLoginOverlayOpen(true);
      setSelectedPlan(plan);
    } else {
      setSelectedPlan(plan);
      setIsSubscriptionOverlayOpen(true);
    }
  };

  /**
   * Close any overlay or modal.
   */
  const closeOverlay = () => {
    setSelectedPlan(null);
    setIsSubscriptionOverlayOpen(false);
    setIsCreatePlanOverlayOpen(false);
    setSuccessMessage(null);
    setError(null);
  };

  /**
   * After a successful login, open the subscription overlay if a plan was selected.
   */
  const handleLoginSuccess = () => {
    setIsLoginOverlayOpen(false);
    setLoginSuccess(true);
  };

  useEffect(() => {
    if (loginSuccess && selectedPlan) {
      const timer = setTimeout(() => {
        setIsSubscriptionOverlayOpen(true);
        setLoginSuccess(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [loginSuccess, selectedPlan]);

  /**
   * Call the `submitSubscription` function to invest in the selected plan.
   * This call is protected. Cookies will be automatically sent if the user is logged in.
   */
  const handleSubscribe = async (planId: string) => {
    if (!selectedPlan) return;

    // Parse the plan's numeric amount from priceRange, if possible
    const amount = Number(selectedPlan.priceRange) || 0;
    const planDetails = {
      planTitle: selectedPlan.title,
      priceRange: selectedPlan.priceRange,
      percentage: selectedPlan.percentage,
      profit: selectedPlan.profit,
      capitalReturn: selectedPlan.capitalReturn,
      totalPercentage: selectedPlan.totalPercentage,
      planType: selectedPlan.planType || 'normal',
    };

    setLoading(true);

    // Using cookie-based auth, no need to pass token explicitly
    const result = await submitSubscription(planId, amount, planDetails);
    setLoading(false);

    if (result.error) {
      setError(result.error);
    } else if (result.success) {
      setSuccessMessage(result.success);
      closeOverlay();
      router.push('/dashboard');
    }
  };

  /**
   * Open overlay for creating a new plan.
   */
  const openCreatePlanOverlay = () => {
    setIsCreatePlanOverlayOpen(true);
  };

  /**
   * Handler when a new plan is created.
   */
  const handlePlanCreated = (newPlan: Plan) => {
    setPlans((prevPlans) => [...prevPlans, newPlan]);
    setSuccessMessage('New plan created successfully!');
    fetchPlans();
  };

  /**
   * Filter handler when user presses Enter in search input.
   */
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // The actual filtering is handled in the useEffect above.
  };

  return (
    <section className="relative py-16">
      {error && <div className="mb-4 text-red-600 font-semibold">{error}</div>}
      {successMessage && (
        <div className="mb-4 text-green-600 font-semibold">{successMessage}</div>
      )}

      <div className="flex justify-between items-center mb-8">
        <form onSubmit={handleSearch} className="flex gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-amber-500" />
            <Input
              type="text"
              placeholder="Search plans by title or type..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-80 border-amber-200 focus:border-amber-500 focus:ring-amber-500/20"
            />
          </div>
        </form>
        <Button
          onClick={openCreatePlanOverlay}
          className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create New Plan
        </Button>
      </div>

      {loading && <SpinnerLoader />}

      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
        initial="hidden"
        animate="visible"
      >
        {filteredPlans.length > 0 ? (
          filteredPlans.map((plan, index) => (
            <motion.div
              key={plan._id}
              className="group relative bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl p-6 border border-amber-200/50 hover:border-amber-300 transition-all duration-300 hover:shadow-2xl"
              whileHover={{ scale: 1.02, y: -8 }}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              {/* Plan Icon */}
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-amber-500 to-yellow-500 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300">
                  {plan.title.charAt(0)}
                </div>
                {plan.planType === 'promotional' && (
                  <div className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
                    HOT
                  </div>
                )}
              </div>

              {/* Plan Title & Price */}
              <div className="mb-6">
                <h3 className="text-xl font-bold text-slate-900 mb-2 group-hover:text-amber-600 transition-colors duration-300">
                  {plan.title}
                </h3>
                <div className="text-3xl font-bold bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent">
                  {formatPriceRange(plan.priceRange)}
                </div>
              </div>

              {/* Return Percentage */}
              <div className="bg-gradient-to-r from-green-100 to-emerald-100 border border-green-200 text-green-700 font-bold py-3 px-4 rounded-xl text-center mb-6 shadow-sm">
                <div className="text-lg">{plan.percentage}</div>
                <div className="text-xs opacity-75">Expected Return</div>
              </div>

              {/* Plan Details */}
              <div className="space-y-3 mb-6">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-slate-600">Profit:</span>
                  <span className="font-semibold text-slate-900">{plan.profit}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-slate-600">Capital Return:</span>
                  <span className={`font-semibold ${plan.capitalReturn === 'Yes' ? 'text-green-600' : 'text-red-600'}`}>
                    {plan.capitalReturn}
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-slate-600">Total Return:</span>
                  <span className="font-bold text-slate-900">{plan.totalPercentage}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-slate-600">Plan Type:</span>
                  <span className={`font-semibold px-2 py-1 rounded-full text-xs ${
                    plan.planType === 'promotional'
                      ? 'bg-amber-100 text-amber-700'
                      : 'bg-slate-100 text-slate-700'
                  }`}>
                    {plan.planType || 'Normal'}
                  </span>
                </div>
              </div>

              {/* Action Button */}
              <button
                onClick={() => openOverlay(plan)}
                className="w-full py-3 bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                Manage Plan
              </button>
            </motion.div>
          ))
        ) : (
          <div className="col-span-full text-center py-12">
            <div className="w-24 h-24 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-12 h-12 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-slate-900 mb-2">No Plans Found</h3>
            <p className="text-slate-600 mb-6">No investment plans match your search criteria.</p>
            <Button
              onClick={openCreatePlanOverlay}
              className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Plan
            </Button>
          </div>
        )}
      </motion.div>

      {/* Subscription Overlay */}
      {isSubscriptionOverlayOpen && selectedPlan && (
        <PlanSubscriptionOverlay
          plan={selectedPlan}
          onClose={closeOverlay}
          onSubscribe={handleSubscribe}
          loading={loading}
          error={error}
        />
      )}

      {/* Login Overlay */}
      {isLoginOverlayOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-30 flex items-center justify-center p-4">
          <motion.div
            className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 w-full max-w-lg relative border border-amber-200/50"
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3 }}
          >
            <button
              onClick={() => setIsLoginOverlayOpen(false)}
              className="absolute top-4 right-4 w-8 h-8 bg-slate-100 hover:bg-slate-200 rounded-full flex items-center justify-center text-slate-600 hover:text-slate-800 transition-colors"
            >
              &#10005;
            </button>
            <UserLogin onClose={handleLoginSuccess} />
          </motion.div>
        </div>
      )}

      {/* Create Plan Overlay */}
      {isCreatePlanOverlayOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 w-full max-w-4xl relative border border-amber-200/50 overflow-y-auto max-h-[90vh]"
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3 }}
          >
            <button
              onClick={() => setIsCreatePlanOverlayOpen(false)}
              className="absolute top-4 right-4 w-8 h-8 bg-slate-100 hover:bg-slate-200 rounded-full flex items-center justify-center text-slate-600 hover:text-slate-800 transition-colors z-10"
            >
              &#10005;
            </button>
            <CreatePlan onPlanCreated={handlePlanCreated} onClose={closeOverlay} />
          </motion.div>
        </div>
      )}
    </section>
  );
}
