
// export default UserActionsOverlay;

'use client';

import React, { ReactElement } from 'react';
import { X } from 'lucide-react';

interface UserActionsOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactElement | React.ReactElement[] | null;
}

const UserActionsOverlay: React.FC<UserActionsOverlayProps> = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;

  // Clone children to pass onClose prop only if child has the onClose prop
  const childrenWithProps = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      // Ensure that the child has the onClose prop in its props type
      return React.cloneElement(child as ReactElement<{ onClose: () => void }>, { onClose });
    }
    return child;
  });

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative bg-white rounded-lg shadow-lg max-w-lg w-full p-6">
        <button
          className="absolute top-4 right-4 text-gray-600 hover:text-gray-800"
          onClick={onClose}
        >
          <X className="h-6 w-6" />
        </button>
        {childrenWithProps}
      </div>
    </div>
  );
};

export default UserActionsOverlay;
