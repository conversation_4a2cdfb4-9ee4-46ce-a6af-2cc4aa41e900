// import Image from "next/image"

export default function AboutSection() {
  // const partners = [
  //   { name: "Partner 1", logo: "/placeholder.svg?height=30&width=120" },
  //   { name: "Partner 2", logo: "/placeholder.svg?height=30&width=120" },
  //   { name: "Partner 3", logo: "/placeholder.svg?height=30&width=120" },
  //   { name: "Partner 4", logo: "/placeholder.svg?height=30&width=120" },
  //   { name: "Partner 5", logo: "/placeholder.svg?height=30&width=120" },
  //   { name: "Partner 6", logo: "/placeholder.svg?height=30&width=120" },
  //   { name: "Partner 7", logo: "/placeholder.svg?height=30&width=120" },
  // ]

  return (
    <section className="py-16 px-4 md:py-24">
      <div className="container mx-auto">
        {/* Partners logos */}
        {/* <div className="flex flex-wrap justify-center items-center gap-8 mb-16 opacity-60">
          {partners.map((partner, index) => (
            <Image
              key={index}
              src={partner.logo}
              alt={partner.name}
              width={120}
              height={30}
              className="h-6 w-auto grayscale"
            />
          ))}
        </div> */}

        {/* About section */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-2xl font-semibold mb-6">About The Golden Miller</h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            We&ldquo;re dedicated to revolutionizing agricultural investments. Our app helps you explore
            investment opportunities, participate effortlessly, and enjoy seamless returns.
            Whether you&ldquo;re planning long-term investments or quick returns, we make it
            easy, reliable, and profitable.
          </p>
        </div>

        {/* Stats */}
        <div className="flex flex-wrap justify-center gap-12 text-center">
          <div className="flex-1 min-w-[200px]">
            <div className="text-3xl font-bold text-amber-600">35K+</div>
            <div className="text-sm text-gray-600 mt-1">Registered investors</div>
          </div>
          <div className="flex-1 min-w-[200px]">
            <div className="text-3xl font-bold text-amber-600">4.9/5</div>
            <div className="text-sm text-gray-600 mt-1">App store Rating</div>
          </div>
          <div className="flex-1 min-w-[200px]">
            <div className="text-3xl font-bold text-amber-600">5+</div>
            <div className="text-sm text-gray-600 mt-1">Years in agricultural investment</div>
          </div>
        </div>
      </div>
    </section>
  )
}

