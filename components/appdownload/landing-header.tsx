


'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

export default function LandingHeader() {
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = () => {
    setIsLoading(true); // Show loading indicator
    try {
      // Redirect the user directly
      window.location.href = '/api/downloads';
    } catch (error) {
      console.error('Error during download:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setIsLoading(false); // Hide loading indicator
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-amber-50 to-amber-100/50">
      <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                The Golden Miller,
                <span className="text-amber-600"> One Smart Move at a Time</span>
              </h1>
              <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl">
                Your path to financial growth starts here—earn up to 35% returns through agricultural market investments
                and our robust referral program.
              </p>
            </div>
            <div className="flex gap-4">
              {/* Apple Store Button */}
              <Link
                href="https://apps.apple.com"
                className="inline-flex items-center justify-center w-[140px] h-[48px] bg-black rounded-md"
              >
                <Image
                  src="/applestore.png"
                  alt="Download on the App Store"
                  width={120}
                  height={40}
                  className="h-[40px] w-auto"
                />
              </Link>

              {/* Android Download Button */}
              <button
                onClick={handleDownload}
                className="inline-flex items-center justify-center w-[140px] h-[48px] bg-black rounded-md"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="loader border-2 border-t-2 border-white rounded-full w-6 h-6 animate-spin"></div>
                ) : (
                  <Image
                    src="/playstore.png"
                    alt="Get it on Google Play"
                    width={120}
                    height={40}
                    className="h-[40px] w-auto"
                  />
                )}
              </button>
            </div>
            <div className="mt-12 relative w-full max-w-[1000px]">
              <Image
                src="/appscreens/landin.png"
                alt="The Golden Miller App Screenshot"
                width={800}
                height={1000}
                className="rounded-xl mx-auto"
              />
            </div>
            <p className="mt-8 text-sm text-gray-600">
              Join 1,000+ investors already growing their wealth
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
