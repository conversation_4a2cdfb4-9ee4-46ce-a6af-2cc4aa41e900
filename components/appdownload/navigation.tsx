import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Coins } from 'lucide-react'

export default function Navigation() {
  return (
    <header className="px-4 lg:px-6 h-16 flex items-center bg-white shadow-sm">
      <div className="container mx-auto flex items-center justify-between">
        <Link className="flex items-center justify-center" href="/">
          <Coins className="h-6 w-6 text-amber-600" />
          <span className="ml-2 text-xl font-bold text-amber-900">The Golden Miller</span>
        </Link>
        <nav className="hidden md:flex space-x-6">
          <Link className="text-sm font-medium text-amber-800 hover:text-amber-900" href="#">
            Home
          </Link>
          <Link className="text-sm font-medium text-amber-800 hover:text-amber-900" href="#">
            Events
          </Link>
          <Link className="text-sm font-medium text-amber-800 hover:text-amber-900" href="#">
            Company
          </Link>
          <Link className="text-sm font-medium text-amber-800 hover:text-amber-900" href="#">
            Testimonial
          </Link>
          <Link className="text-sm font-medium text-amber-800 hover:text-amber-900" href="#">
            FAQ
          </Link>
        </nav>
        <Button className="bg-amber-600 hover:bg-amber-700 text-white" variant="default">
          Get Started
        </Button>
      </div>
    </header>
  )
}

