// import Image from "next/image"
// import Link from "next/link"

// export default function DownloadBanner() {
//   return (
//     <section className="bg-amber-600 overflow-hidden relative">
//       <div className="container mx-auto px-4 py-16 md:py-20 relative z-10">
//         <div className="max-w-xl">
//           <h2 className="text-3xl font-bold text-white mb-4">
//             Start your investment journey now
//           </h2>
//           <p className="text-amber-50 text-lg mb-8">
//             download our app and start earning effortlessly!
//           </p>
//           <div className="flex gap-4">
//             <Link href="https://apps.apple.com">
//               <Image
//                 src="/applestore.png"
//                 alt="Download on the App Store"
//                 width={140}
//                 height={42}
//                 className="h-[42px] w-auto"
//               />
//             </Link>
//             <Link href="https://play.google.com">
//               <Image
//                 src="/playstore.png"
//                 alt="Get it on Google Play"
//                 width={140}
//                 height={42}
//                 className="h-[42px] w-auto"
//               />
//             </Link>
//           </div>
//         </div>

//         {/* Floating App Screenshots */}
//         <div className="absolute top-1/2 right-0 transform -translate-y-1/2 hidden md:block">
//           <div className="relative w-[1000px] h-[800px]">
//             <Image
//               src="/bannerdownload.png"
//               alt="App Screenshot 1"
//               width={800}
//               height={600}
//               className="top-0 right-48 transform rotate-12 shadow-xl rounded-2xl"
//             />
//             {/* <Image
//               src="/placeholder.svg?height=400&width=200"
//               alt="App Screenshot 2"
//               width={200}
//               height={400}
//               className="absolute top-10 right-20 transform rotate-12 shadow-xl rounded-2xl"
//             />
//             <Image
//               src="/placeholder.svg?height=400&width=200"
//               alt="App Screenshot 3"
//               width={200}
//               height={400}
//               className="absolute top-20 right-72 transform -rotate-6 shadow-xl rounded-2xl"
//             /> */}
//           </div>
//         </div>
//       </div>
//     </section>
//   )
// }




'use client';

import { useState } from 'react';
import Image from 'next/image';

export default function DownloadBanner() {
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = async () => {
    setIsLoading(true); // Show loading indicator
    try {
      const response = await fetch('/api/downloads', {
        method: 'GET',
      });

      if (response.redirected) {
        window.location.href = response.url; // Redirect to the download link
      } else {
        alert('Failed to download the app. Please try again later.');
      }
    } catch (error) {
      console.error('Error during download:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setIsLoading(false); // Hide loading indicator
    }
  };

  return (
    <section className="bg-amber-600 overflow-hidden relative">
      <div className="container mx-auto px-4 py-16 md:py-20 relative z-10">
        <div className="max-w-xl">
          <h2 className="text-3xl font-bold text-white mb-4">
            Start your investment journey now
          </h2>
          <p className="text-amber-50 text-lg mb-8">
            Download our app and start earning effortlessly!
          </p>
          <div className="flex gap-4">
            {/* Apple Store Button */}
            <a
              href="https://apps.apple.com"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Image
                src="/applestore.png"
                alt="Download on the App Store"
                width={140}
                height={42}
                className="h-[42px] w-auto"
              />
            </a>

            {/* Android Download Button */}
            <button
              onClick={handleDownload}
              className="flex items-center justify-center w-auto h-[42px] px-4 bg-black rounded"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="loader border-2 border-t-2 border-white rounded-full w-6 h-6 animate-spin"></div>
              ) : (
                <Image
                  src="/playstore.png"
                  alt="Get it on Google Play"
                  width={140}
                  height={42}
                  className="h-[42px] w-auto"
                />
              )}
            </button>
          </div>
        </div>

        {/* Floating App Screenshots */}
        <div className="absolute top-1/2 right-0 transform -translate-y-1/2 hidden md:block">
          <div className="relative w-[1000px] h-[800px]">
            <Image
              src="/bannerdownload.png"
              alt="App Screenshot 1"
              width={800}
              height={600}
              className="top-0 right-48 transform rotate-12 shadow-xl rounded-2xl"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
