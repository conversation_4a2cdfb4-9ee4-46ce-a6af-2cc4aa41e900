

// // components/PlanSubscriptionOverlay.tsx

// import { motion } from 'framer-motion';
// import { Plan } from '@/types/plan';

// interface PlanSubscriptionOverlayProps {
//   plan: Plan;
//   onClose: () => void;
//   onSubscribe: (planId: string) => Promise<void>;
//   loading: boolean;
//   error: string | null;
// }

// const PlanSubscriptionOverlay: React.FC<PlanSubscriptionOverlayProps> = ({
//   plan,
//   onClose,
//   onSubscribe,
//   loading,
//   error,
// }) => {
//   const handleSubscribeClick = async () => {
//     try {
//       await onSubscribe(plan._id); // Use 'plan._id' instead of 'plan.id'
//     } catch (err) {
//       console.error('Error subscribing to plan:', err);
//       // Error handling is managed in the parent component
//     }
//   };

//   return (
//     <div className="fixed inset-0 bg-black bg-opacity-60 z-30 flex items-center justify-center">
//       <motion.div
//         className="bg-white bg-opacity-90 rounded-lg shadow-lg p-8 w-full max-w-lg relative z-40 backdrop-blur-md"
//         initial={{ opacity: 0, y: -50 }}
//         animate={{ opacity: 1, y: 0 }}
//         exit={{ opacity: 0, y: 50 }}
//       >
//         <button
//           onClick={onClose}
//           className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 focus:outline-none"
//         >
//           &#10005;
//         </button>

//         {error && (
//           <p className="text-red-600 mb-4 text-center">{error}</p>
//         )}

//         <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">{plan.title}</h2>

//         {/* Plan Details */}
//         <div className="grid gap-4 mb-6">
//           <p className="text-gray-700">
//             <span className="font-semibold">Price Range:</span> {plan.priceRange}
//           </p>
//           <p className="text-gray-700">
//             <span className="font-semibold">Percentage:</span> {plan.percentage}
//           </p>
//           <p className="text-gray-700">
//             <span className="font-semibold">Profit:</span> {plan.profit}
//           </p>
//           <p className="text-gray-700">
//             <span className="font-semibold">Capital Return:</span> {plan.capitalReturn}
//           </p>
//           <p className="text-gray-700">
//             <span className="font-semibold">Total Percentage:</span> {plan.totalPercentage}
//           </p>
//         </div>

//         <button
//           onClick={handleSubscribeClick}
//           className="w-full bg-yellow-700 text-white py-3 rounded-lg font-semibold hover:bg-yellow-600 transition-colors"
//           disabled={loading}
//         >
//           {loading ? 'Subscribing...' : 'Subscribe to Plan'}
//         </button>
//       </motion.div>
//     </div>
//   );
// };

// export default PlanSubscriptionOverlay;




import { motion } from 'framer-motion';
import { Plan } from '@/types/plan';

interface PlanSubscriptionOverlayProps {
  plan: Plan;
  onClose: () => void;
  onSubscribe: (planId: string) => Promise<void>;
  loading: boolean;
  error: string | null;
}

const PlanSubscriptionOverlay: React.FC<PlanSubscriptionOverlayProps> = ({
  plan,
  onClose,
  onSubscribe,
  loading,
  error,
}) => {
  const handleSubscribeClick = async () => {
    try {
      await onSubscribe(plan._id);
    } catch (err) {
      console.error('Error subscribing to plan:', err);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 z-30 flex items-center justify-center">
      <motion.div
        className="bg-white bg-opacity-90 rounded-lg shadow-lg p-8 w-full max-w-lg relative z-40 backdrop-blur-md"
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 50 }}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 focus:outline-none"
        >
          &#10005;
        </button>

        {error && (
          <p className="text-red-600 mb-4 text-center">{error}</p>
        )}

        <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">{plan.title}</h2>

        {/* Plan Details */}
        <div className="grid gap-4 mb-6">
          <p className="text-gray-700">
            <span className="font-semibold">Price Range:</span> {plan.priceRange}
          </p>
          <p className="text-gray-700">
            <span className="font-semibold">Percentage:</span> {plan.percentage}
          </p>
          <p className="text-gray-700">
            <span className="font-semibold">Profit:</span> {plan.profit}
          </p>
          <p className="text-gray-700">
            <span className="font-semibold">Capital Return:</span> {plan.capitalReturn}
          </p>
          <p className="text-gray-700">
            <span className="font-semibold">Total Percentage:</span> {plan.totalPercentage}
          </p>
          <p className="text-gray-700">
            <span className="font-semibold">Plan Type:</span> {plan.planType || 'Normal'}
          </p>
        </div>

        <button
          onClick={handleSubscribeClick}
          className="w-full bg-yellow-700 text-white py-3 rounded-lg font-semibold hover:bg-yellow-600 transition-colors"
          disabled={loading}
        >
          {loading ? 'Subscribing...' : 'Subscribe to Plan'}
        </button>
      </motion.div>
    </div>
  );
};

export default PlanSubscriptionOverlay;

