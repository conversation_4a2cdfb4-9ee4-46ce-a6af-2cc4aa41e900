

// // components/PlanSubscriptionOverlay.tsx

// import { motion } from 'framer-motion';
// import { Plan } from '@/types/plan';

// interface PlanSubscriptionOverlayProps {
//   plan: Plan;
//   onClose: () => void;
//   onSubscribe: (planId: string) => Promise<void>;
//   loading: boolean;
//   error: string | null;
// }

// const PlanSubscriptionOverlay: React.FC<PlanSubscriptionOverlayProps> = ({
//   plan,
//   onClose,
//   onSubscribe,
//   loading,
//   error,
// }) => {
//   const handleSubscribeClick = async () => {
//     try {
//       await onSubscribe(plan._id); // Use 'plan._id' instead of 'plan.id'
//     } catch (err) {
//       console.error('Error subscribing to plan:', err);
//       // Error handling is managed in the parent component
//     }
//   };

//   return (
//     <div className="fixed inset-0 bg-black bg-opacity-60 z-30 flex items-center justify-center">
//       <motion.div
//         className="bg-white bg-opacity-90 rounded-lg shadow-lg p-8 w-full max-w-lg relative z-40 backdrop-blur-md"
//         initial={{ opacity: 0, y: -50 }}
//         animate={{ opacity: 1, y: 0 }}
//         exit={{ opacity: 0, y: 50 }}
//       >
//         <button
//           onClick={onClose}
//           className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 focus:outline-none"
//         >
//           &#10005;
//         </button>

//         {error && (
//           <p className="text-red-600 mb-4 text-center">{error}</p>
//         )}

//         <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">{plan.title}</h2>

//         {/* Plan Details */}
//         <div className="grid gap-4 mb-6">
//           <p className="text-gray-700">
//             <span className="font-semibold">Price Range:</span> {plan.priceRange}
//           </p>
//           <p className="text-gray-700">
//             <span className="font-semibold">Percentage:</span> {plan.percentage}
//           </p>
//           <p className="text-gray-700">
//             <span className="font-semibold">Profit:</span> {plan.profit}
//           </p>
//           <p className="text-gray-700">
//             <span className="font-semibold">Capital Return:</span> {plan.capitalReturn}
//           </p>
//           <p className="text-gray-700">
//             <span className="font-semibold">Total Percentage:</span> {plan.totalPercentage}
//           </p>
//         </div>

//         <button
//           onClick={handleSubscribeClick}
//           className="w-full bg-yellow-700 text-white py-3 rounded-lg font-semibold hover:bg-yellow-600 transition-colors"
//           disabled={loading}
//         >
//           {loading ? 'Subscribing...' : 'Subscribe to Plan'}
//         </button>
//       </motion.div>
//     </div>
//   );
// };

// export default PlanSubscriptionOverlay;




'use client';

import { motion } from 'framer-motion';
import { Plan } from '@/types/plan';
import { Button } from '@/components/ui/button';
import { formatCurrencyDisplay, formatCurrencyRange, parseCurrency } from '@/lib/currency';
import {
  X,
  DollarSign,
  Percent,
  TrendingUp,
  Calendar,
  Tag,
  Activity,
  CreditCard,
  AlertCircle,
  Sparkles
} from 'lucide-react';

interface PlanSubscriptionOverlayProps {
  plan: Plan;
  onClose: () => void;
  onSubscribe: (planId: string) => Promise<void>;
  loading: boolean;
  error: string | null;
}

// Helper function to format price range from USD to ZAR
const formatPriceRange = (priceRange: string): string => {
  // If the price range already contains ZAR symbol (R), return as is
  if (priceRange.includes('R')) {
    return priceRange;
  }

  // If it contains $ symbols, replace with R
  if (priceRange.includes('$')) {
    return priceRange.replace(/\$/g, 'R');
  }

  // If it's a range like "1000 - 5000", format it properly
  const rangeMatch = priceRange.match(/(\d+(?:,\d{3})*)\s*-\s*(\d+(?:,\d{3})*)/);
  if (rangeMatch) {
    const minAmount = parseCurrency(rangeMatch[1]);
    const maxAmount = parseCurrency(rangeMatch[2]);
    return formatCurrencyRange(minAmount, maxAmount);
  }

  // If it's a single number, format it as currency
  const singleAmount = parseCurrency(priceRange);
  if (singleAmount > 0) {
    return formatCurrencyDisplay(singleAmount);
  }

  // Fallback: return the original string
  return priceRange;
};

const PlanSubscriptionOverlay: React.FC<PlanSubscriptionOverlayProps> = ({
  plan,
  onClose,
  onSubscribe,
  loading,
  error,
}) => {
  const handleSubscribeClick = async () => {
    try {
      await onSubscribe(plan._id);
    } catch (err) {
      console.error('Error subscribing to plan:', err);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-30 flex items-center justify-center p-4">
      <motion.div
        className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 w-full max-w-2xl relative z-40 border border-white/20 max-h-[90vh] overflow-y-auto"
        initial={{ opacity: 0, scale: 0.9, y: 50 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 50 }}
        transition={{ duration: 0.3 }}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-full transition-colors focus:outline-none"
        >
          <X className="w-5 h-5" />
        </button>

        {/* Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-amber-500/20 to-yellow-500/20 backdrop-blur-sm border border-amber-400/30 rounded-full text-amber-600 text-sm font-medium mb-4">
            <Sparkles className="w-4 h-4" />
            Investment Plan
          </div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-900 via-amber-900 to-yellow-900 bg-clip-text text-transparent mb-2">
            {plan.title}
          </h2>
          <p className="text-slate-600">Review the plan details and confirm your investment</p>
        </motion.div>

        {error && (
          <motion.div
            className="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-xl mb-6"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </motion.div>
        )}

        {/* Plan Details */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <div className="p-4 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-xl border border-amber-200">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="w-5 h-5 text-amber-600" />
              <span className="font-semibold text-slate-700">Investment Range</span>
            </div>
            <p className="text-2xl font-bold text-amber-600">{formatPriceRange(plan.priceRange)}</p>
          </div>

          <div className="p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <Percent className="w-5 h-5 text-green-600" />
              <span className="font-semibold text-slate-700">Return Rate</span>
            </div>
            <p className="text-2xl font-bold text-green-600">{plan.percentage}</p>
          </div>

          <div className="p-4 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              <span className="font-semibold text-slate-700">Expected Profit</span>
            </div>
            <p className="text-2xl font-bold text-blue-600">{plan.profit}</p>
          </div>

          <div className="p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-200">
            <div className="flex items-center gap-2 mb-2">
              <Activity className="w-5 h-5 text-purple-600" />
              <span className="font-semibold text-slate-700">Capital Return</span>
            </div>
            <p className="text-2xl font-bold text-purple-600">{plan.capitalReturn}</p>
          </div>

          <div className="p-4 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl border border-orange-200">
            <div className="flex items-center gap-2 mb-2">
              <Percent className="w-5 h-5 text-orange-600" />
              <span className="font-semibold text-slate-700">Total Return</span>
            </div>
            <p className="text-2xl font-bold text-orange-600">{plan.totalPercentage}</p>
          </div>

          <div className="p-4 bg-gradient-to-br from-slate-50 to-gray-50 rounded-xl border border-slate-200">
            <div className="flex items-center gap-2 mb-2">
              <Tag className="w-5 h-5 text-slate-600" />
              <span className="font-semibold text-slate-700">Plan Type</span>
            </div>
            <p className="text-2xl font-bold text-slate-600 capitalize">{plan.planType || 'Normal'}</p>
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <Button
            onClick={onClose}
            variant="outline"
            className="flex-1"
            size="lg"
          >
            Cancel
          </Button>

          <Button
            onClick={handleSubscribeClick}
            disabled={loading}
            className="flex-1"
            size="lg"
          >
            {loading ? (
              <>
                <motion.div
                  className="w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-2"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                Processing...
              </>
            ) : (
              <>
                <CreditCard className="w-5 h-5 mr-2" />
                Invest Now
              </>
            )}
          </Button>
        </motion.div>

        {/* Security Notice */}
        <motion.div
          className="mt-6 p-4 bg-slate-50 rounded-xl border border-slate-200"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.7 }}
        >
          <p className="text-sm text-slate-600 text-center">
            🔒 Your investment is secured with bank-grade encryption and protected by our comprehensive insurance policy.
          </p>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default PlanSubscriptionOverlay;

