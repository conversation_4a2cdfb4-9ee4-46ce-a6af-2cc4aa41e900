'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { Building2, Users, TrendingUp, Shield, Award, Globe, ArrowRight } from 'lucide-react'

export default function Welcome() {
  const stats = [
    { label: 'Assets Under Management', value: '$2.5B+', icon: TrendingUp },
    { label: 'Global Clients', value: '50,000+', icon: Users },
    { label: 'Years of Excellence', value: '15+', icon: Award },
    { label: 'Countries Served', value: '25+', icon: Globe },
  ]

  const features = [
    {
      title: 'Who We Are',
      description: 'TGM Finance is a premier financial technology company delivering institutional-grade investment solutions to sophisticated investors worldwide.',
      icon: Building2,
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      title: 'Our Mission',
      description: 'We democratize access to premium investment opportunities through cutting-edge technology, expert advisory, and personalized wealth management strategies.',
      icon: TrendingUp,
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      title: 'How We Excel',
      description: 'Our platform combines advanced analytics, AI-driven insights, and human expertise to deliver superior investment outcomes and exceptional client experiences.',
      icon: Shield,
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      title: 'Why Choose TGM',
      description: 'Join thousands of successful investors who trust TGM Finance for transparent, secure, and profitable investment solutions backed by industry-leading technology.',
      icon: Award,
      gradient: 'from-orange-500 to-red-500'
    }
  ]

  return (
    <section className="relative bg-gradient-to-br from-slate-50 via-white to-blue-50 overflow-hidden py-24 sm:py-32">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-100/50 to-purple-100/50 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-purple-100/50 to-pink-100/50 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-purple-900 bg-clip-text text-transparent mb-6">
            Welcome to TGM Finance
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Where sophisticated investment strategies meet cutting-edge technology to create exceptional wealth-building opportunities.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20"
              whileHover={{ scale: 1.05, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl mb-4">
                <stat.icon className="w-6 h-6 text-white" />
              </div>
              <div className="text-3xl font-bold text-slate-900 mb-2">{stat.value}</div>
              <div className="text-sm text-slate-600">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Main Content Grid */}
        <div className="lg:grid lg:grid-cols-2 lg:gap-20 items-center">
          {/* Image Section */}
          <motion.div
            className="relative mb-12 lg:mb-0"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="relative">
              {/* Decorative Elements */}
              <div className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-blue-400/30 to-purple-400/30 rounded-full blur-xl"></div>
              <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-br from-purple-400/30 to-pink-400/30 rounded-full blur-xl"></div>
              
              {/* Main Image Container */}
              <div className="relative bg-gradient-to-br from-white to-blue-50 p-8 rounded-3xl shadow-2xl border border-white/20">
                <div className="aspect-square bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 rounded-2xl p-8 flex items-center justify-center">
                  <div className="text-center text-white">
                    <Building2 className="w-24 h-24 mx-auto mb-4 opacity-90" />
                    <h3 className="text-2xl font-bold mb-2">TGM Finance</h3>
                    <p className="text-blue-100">Premium Investment Platform</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                className="group p-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.02, y: -2 }}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex items-start space-x-4">
                  <div className={`flex-shrink-0 w-12 h-12 bg-gradient-to-br ${feature.gradient} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-slate-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                      {feature.title}
                    </h3>
                    <p className="text-slate-600 leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}

            {/* CTA Button */}
            <motion.div
              className="pt-8"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              viewport={{ once: true }}
            >
              <motion.button
                className="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Discover Our Platform
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </motion.button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
