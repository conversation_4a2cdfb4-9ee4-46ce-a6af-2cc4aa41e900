

// components/home/<USER>
'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import PlanSubscriptionOverlay from './PlanSubscriptionOverlay';
import UserLogin from '../UserLogin';
import CreatePlan from '../CreatePlan';
import { Plan } from '@/types/plan';
import { useAuth } from '@/context/AuthContext';
import SpinnerLoader from '../SpinnerLoader';
import { useRouter } from 'next/navigation';

/**
 * Submit subscription using cookie-based authentication.
 * Removed "token" param and Bearer header usage.
 */
const submitSubscription = async (
  planId: string,
  amount: number,
  planDetails: {
    planTitle: string;
    priceRange: string;
    percentage: string;
    profit: string;
    capitalReturn: string;
    totalPercentage: string;
  }
) => {
  try {
    const res = await fetch('/api/plans/subscribe', {
      method: 'POST',
      credentials: 'include', // <-- Important for sending and receiving cookies
      headers: {
        'Content-Type': 'application/json',
        'x-client-type': 'web', // Tells server we expect cookie-based auth
      },
      body: JSON.stringify({
        planId,
        amount,
        ...planDetails,
      }),
    });

    const data = await res.json();
    if (!res.ok) {
      console.error('Failed to submit subscription:', data);
      return { error: data.error || 'Unknown error occurred' };
    }

    return { success: 'Subscription successful', data };
  } catch (error) {
    console.error('Error submitting subscription:', error);
    return { error: 'Failed to submit subscription' };
  }
};

export default function PurchasePlans() {
  const router = useRouter();
  const [plans, setPlans] = useState<Plan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [isLoginOverlayOpen, setIsLoginOverlayOpen] = useState(false);
  const [isSubscriptionOverlayOpen, setIsSubscriptionOverlayOpen] = useState(false);
  const [isCreatePlanOverlayOpen, setIsCreatePlanOverlayOpen] = useState(false);

  // AuthContext no longer exposes "token"
  const { user } = useAuth();

  const [loginSuccess, setLoginSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch all plans from your backend (unprotected route).
   */
  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/plans');
      const data = await response.json();

      if (Array.isArray(data)) {
        setPlans(data);
      } else {
        console.error('Data is not an array:', data);
        setPlans([]);
      }
    } catch (error) {
      console.error('Failed to fetch plans:', error);
      setPlans([]);
    }
  };

  useEffect(() => {
    fetchPlans();
  }, []);

  /**
   * Open subscription overlay if user is logged in,
   * otherwise open login overlay.
   */
  const openOverlay = (plan: Plan) => {
    if (!user) {
      setIsLoginOverlayOpen(true);
      setSelectedPlan(plan);
    } else {
      setSelectedPlan(plan);
      setIsSubscriptionOverlayOpen(true);
    }
  };

  /**
   * Close all overlays.
   */
  const closeOverlay = () => {
    setSelectedPlan(null);
    setIsSubscriptionOverlayOpen(false);
    setIsCreatePlanOverlayOpen(false);
    setSuccessMessage(null);
    setError(null);
  };

  /**
   * Handle login success by closing login overlay
   * and triggering the subscription overlay if a plan was selected.
   */
  const handleLoginSuccess = () => {
    setIsLoginOverlayOpen(false);
    setLoginSuccess(true);
  };

  // After a successful login, open subscription overlay for the previously selected plan
  useEffect(() => {
    if (loginSuccess && selectedPlan) {
      const timer = setTimeout(() => {
        setIsSubscriptionOverlayOpen(true);
        setLoginSuccess(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [loginSuccess, selectedPlan]);

  /**
   * Subscribe to the selected plan using cookie-based auth.
   */
  const handleSubscribe = async (planId: string) => {
    // If no plan is selected, just return
    if (!selectedPlan) return;

    const amount = Number(selectedPlan.priceRange) || 0;
    const planDetails = {
      planTitle: selectedPlan.title,
      priceRange: selectedPlan.priceRange,
      percentage: selectedPlan.percentage,
      profit: selectedPlan.profit,
      capitalReturn: selectedPlan.capitalReturn,
      totalPercentage: selectedPlan.totalPercentage,
    };

    setLoading(true);
    const result = await submitSubscription(planId, amount, planDetails);
    setLoading(false);

    if (result.error) {
      setError(result.error);
    } else if (result.success) {
      setSuccessMessage(result.success);
      closeOverlay();
      router.push('/dashboard'); // Optionally navigate to the dashboard
    }
  };

  /**
   * Handle newly created plan (if relevant).
   */
  const handlePlanCreated = (newPlan: Plan) => {
    setPlans((prevPlans) => [...prevPlans, newPlan]);
    setSuccessMessage('New plan created successfully!');
    fetchPlans(); // Refresh the plan list
  };

  return (
    <>
      <section className="relative py-24 bg-gradient-to-br from-slate-50 via-white to-amber-50 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-amber-100/50 to-yellow-100/50 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-yellow-100/50 to-orange-100/50 rounded-full blur-3xl"></div>
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-amber-500/20 to-yellow-500/20 backdrop-blur-sm border border-amber-400/30 rounded-full text-amber-600 text-sm font-medium mb-6"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            >
              💎
            </motion.div>
            Premium Investment Plans
          </motion.div>

          <h2 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-slate-900 via-amber-900 to-yellow-900 bg-clip-text text-transparent mb-6">
            Choose Your Investment Strategy
          </h2>

          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Discover our carefully crafted investment plans designed to maximize your returns while minimizing risk.
          </p>
        </motion.div>

        {/* Error and Success Messages */}
        {error && (
          <motion.div
            className="mb-8 p-4 bg-red-50 border border-red-200 rounded-xl text-red-600 font-semibold text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            {error}
          </motion.div>
        )}
        {successMessage && (
          <motion.div
            className="mb-8 p-4 bg-green-50 border border-green-200 rounded-xl text-green-600 font-semibold text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            {successMessage}
          </motion.div>
        )}

        {/* Loading Spinner */}
        {loading && <SpinnerLoader />}

        {/* Plans Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
          initial="hidden"
          animate="visible"
        >
        {plans.length > 0 ? (
          plans.map((plan, index) => (
            <motion.div
              key={plan._id}
              className="group relative"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -8 }}
            >
              {/* Popular Badge */}
              {index === 1 && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-amber-500 to-yellow-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                    Most Popular
                  </div>
                </div>
              )}

              {/* Card */}
              <div className={`relative h-full p-8 bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border transition-all duration-500 group-hover:shadow-2xl ${
                index === 1
                  ? 'border-amber-200 ring-2 ring-amber-500/20'
                  : 'border-white/20 hover:border-amber-200'
              }`}>
                {/* Gradient Background on Hover */}
                <div className="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-yellow-500/5 opacity-0 group-hover:opacity-100 rounded-2xl transition-opacity duration-500"></div>

                {/* Plan Icon */}
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-amber-500 to-yellow-500 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300">
                    {plan.title.charAt(0)}
                  </div>
                </div>

                {/* Plan Title */}
                <div className="relative mb-6">
                  <h3 className="text-2xl font-bold text-slate-900 mb-2 group-hover:text-amber-600 transition-colors duration-300">
                    {plan.title}
                  </h3>
                  <div className="text-3xl font-bold bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent">
                    {plan.priceRange}
                  </div>
                </div>

                {/* Plan Features */}
                <div className="relative space-y-4 mb-8">
                  {plan.percentage && (
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-xl border border-green-200">
                      <span className="text-sm text-slate-600">Return Rate</span>
                      <span className="font-semibold text-green-600">{plan.percentage}</span>
                    </div>
                  )}

                  {plan.capitalReturn && (
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-xl border border-blue-200">
                      <span className="text-sm text-slate-600">Capital Return</span>
                      <span className="font-semibold text-blue-600">{plan.capitalReturn}</span>
                    </div>
                  )}

                  {plan.totalPercentage && (
                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded-xl border border-purple-200">
                      <span className="text-sm text-slate-600">Total Return</span>
                      <span className="font-semibold text-purple-600">{plan.totalPercentage}</span>
                    </div>
                  )}
                </div>

                {/* CTA Button */}
                <motion.button
                  onClick={() => openOverlay(plan)}
                  className="relative w-full py-4 bg-gradient-to-r from-amber-500 to-yellow-500 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:from-amber-600 group-hover:to-yellow-600"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="relative z-10">Invest Now</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
                </motion.button>
              </div>
            </motion.div>
          ))
        ) : (
          <motion.div
            className="col-span-full text-center py-16"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <div className="text-6xl mb-4">📊</div>
            <h3 className="text-2xl font-semibold text-slate-900 mb-2">No Plans Available</h3>
            <p className="text-slate-600">Check back soon for new investment opportunities.</p>
          </motion.div>
        )}
        </motion.div>
      </div>
      </section>

      {/* Overlays */}
      {isSubscriptionOverlayOpen && selectedPlan && (
        <PlanSubscriptionOverlay
          plan={selectedPlan}
          onClose={closeOverlay}
          onSubscribe={handleSubscribe}
          loading={loading}
          error={error}
        />
      )}

      {/* Login Overlay */}
      {isLoginOverlayOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-60 z-30 flex items-center justify-center">
          <motion.div
            className="bg-white rounded-lg shadow-lg p-6 w-full max-w-lg relative z-40"
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
          >
            <button
              onClick={() => setIsLoginOverlayOpen(false)}
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              &#10005;
            </button>
            <UserLogin onClose={handleLoginSuccess} />
          </motion.div>
        </div>
      )}

      {/* CreatePlan Overlay (if needed) */}
      {isCreatePlanOverlayOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center">
          <motion.div
            className="bg-white rounded-lg shadow-lg p-6 w-full max-w-lg relative z-60 overflow-y-auto max-h-full"
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
          >
            <button
              onClick={() => setIsCreatePlanOverlayOpen(false)}
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              &#10005;
            </button>
            <CreatePlan onPlanCreated={handlePlanCreated} onClose={closeOverlay} />
          </motion.div>
        </div>
      )}
    </>
  );
}
