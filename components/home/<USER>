// 'use client'

// import { motion } from 'framer-motion'
// import { FaLeaf, FaHandshake, FaChartLine, FaGlobe } from 'react-icons/fa'

// export default function KeyBenefits() {
//   return (
//     <section className="py-16 bg-gray-100">
//       <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
//         {/* Section Title */}
//         <div className="text-center">
//           <motion.h2
//             className="text-3xl md:text-4xl font-bold text-gray-900"
//             initial={{ opacity: 0, y: 50 }}
//             animate={{ opacity: 1, y: 0 }}
//             transition={{ duration: 0.8, ease: 'easeOut' }}
//           >
//             Why Choose <PERSON>?
//           </motion.h2>
//           <motion.p
//             className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto"
//             initial={{ opacity: 0, y: 50 }}
//             animate={{ opacity: 1, y: 0 }}
//             transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
//           >
//             Join us to empower local communities, grow your wealth, and create a sustainable future.
//           </motion.p>
//         </div>

//         {/* Key Benefits Grid */}
//         <div className="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
//           {/* Benefit 1 */}
//           <motion.div
//             className="flex flex-col items-center text-center p-6 bg-white rounded-lg shadow-md"
//             initial={{ opacity: 0, y: 50 }}
//             whileInView={{ opacity: 1, y: 0 }}
//             viewport={{ once: true }}
//             transition={{ duration: 0.8, ease: 'easeOut' }}
//           >
//             <FaLeaf className="text-amber-500 text-4xl" />
//             <h3 className="mt-4 text-xl font-semibold text-gray-900">Sustainable Agriculture</h3>
//             <p className="mt-2 text-gray-600">
//               Invest in initiatives that promote sustainable agricultural practices, ensuring a greener future.
//             </p>
//           </motion.div>

//           {/* Benefit 2 */}
//           <motion.div
//             className="flex flex-col items-center text-center p-6 bg-white rounded-lg shadow-md"
//             initial={{ opacity: 0, y: 50 }}
//             whileInView={{ opacity: 1, y: 0 }}
//             viewport={{ once: true }}
//             transition={{ duration: 0.8, ease: 'easeOut', delay: 0.2 }}
//           >
//             <FaHandshake className="text-amber-500 text-4xl" />
//             <h3 className="mt-4 text-xl font-semibold text-gray-900">Community-Driven</h3>
//             <p className="mt-2 text-gray-600">
//               Support local farmers and entrepreneurs, creating a cycle of prosperity for everyone involved.
//             </p>
//           </motion.div>

//           {/* Benefit 3 */}
//           <motion.div
//             className="flex flex-col items-center text-center p-6 bg-white rounded-lg shadow-md"
//             initial={{ opacity: 0, y: 50 }}
//             whileInView={{ opacity: 1, y: 0 }}
//             viewport={{ once: true }}
//             transition={{ duration: 0.8, ease: 'easeOut', delay: 0.4 }}
//           >
//             <FaChartLine className="text-amber-500 text-4xl" />
//             <h3 className="mt-4 text-xl font-semibold text-gray-900">High Returns</h3>
//             <p className="mt-2 text-gray-600">
//               Enjoy impressive monthly returns on your investments, up to 35%, ensuring financial stability.
//             </p>
//           </motion.div>

//           {/* Benefit 4 */}
//           <motion.div
//             className="flex flex-col items-center text-center p-6 bg-white rounded-lg shadow-md"
//             initial={{ opacity: 0, y: 50 }}
//             whileInView={{ opacity: 1, y: 0 }}
//             viewport={{ once: true }}
//             transition={{ duration: 0.8, ease: 'easeOut', delay: 0.6 }}
//           >
//             <FaGlobe className="text-amber-500 text-4xl" />
//             <h3 className="mt-4 text-xl font-semibold text-gray-900">Global Impact</h3>
//             <p className="mt-2 text-gray-600">
//               Be part of a global movement that ensures food security and community growth worldwide.
//             </p>
//           </motion.div>
//         </div>
//       </div>
//     </section>
//   )
// }



'use client'

import { motion } from 'framer-motion'
import { FaLeaf, FaHandshake, FaChartLine, FaGlobe } from 'react-icons/fa'

export default function KeyBenefits() {
  return (
    <section className="py-16 bg-gray-100">
      <div className="max-w-7xl mx-auto mb-16 px-4 sm:px-6 lg:px-8">
        {/* Section Title */}
        <div className="text-center">
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-gray-900"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          >
            Why Choose Golden Miller?
          </motion.h2>
          <motion.p
            className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
          >
            Join us to empower local communities, grow your wealth, and create a sustainable future.
          </motion.p>
        </div>

        {/* Key Benefits Grid */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Benefit 1 (Top-left Animation) */}
          <motion.div
            className="flex flex-col items-center text-center p-6 bg-white rounded-lg shadow-md"
            initial={{ opacity: 0, x: -50, y: -50 }}
            whileInView={{ opacity: 1, x: 0, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: 'easeOut', delay: 0.1 }}
          >
            <FaLeaf className="text-amber-500 text-4xl" />
            <h3 className="mt-4 text-xl font-semibold text-gray-900">Sustainable Agriculture</h3>
            <p className="mt-2 text-gray-600">
              Invest in initiatives that promote sustainable agricultural practices, ensuring a greener future.
            </p>
          </motion.div>

          {/* Benefit 2 (Top-left Animation) */}
          <motion.div
            className="flex flex-col items-center text-center p-6 bg-white rounded-lg shadow-md"
            initial={{ opacity: 0, x: -50, y: -50 }}
            whileInView={{ opacity: 1, x: 0, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: 'easeOut', delay: 0.2 }}
          >
            <FaHandshake className="text-amber-500 text-4xl" />
            <h3 className="mt-4 text-xl font-semibold text-gray-900">Community-Driven</h3>
            <p className="mt-2 text-gray-600">
              Support local farmers and entrepreneurs, creating a cycle of prosperity for everyone involved.
            </p>
          </motion.div>

          {/* Benefit 3 (Top-right Animation) */}
          <motion.div
            className="flex flex-col items-center text-center p-6 bg-white rounded-lg shadow-md"
            initial={{ opacity: 0, x: 50, y: -50 }}
            whileInView={{ opacity: 1, x: 0, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: 'easeOut', delay: 0.3 }}
          >
            <FaChartLine className="text-amber-500 text-4xl" />
            <h3 className="mt-4 text-xl font-semibold text-gray-900">High Returns</h3>
            <p className="mt-2 text-gray-600">
              Enjoy impressive monthly returns on your investments, up to 35%, ensuring financial stability.
            </p>
          </motion.div>

          {/* Benefit 4 (Top-right Animation) */}
          <motion.div
            className="flex flex-col items-center text-center p-6 bg-white rounded-lg shadow-md"
            initial={{ opacity: 0, x: 50, y: -50 }}
            whileInView={{ opacity: 1, x: 0, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: 'easeOut', delay: 0.4 }}
          >
            <FaGlobe className="text-amber-500 text-4xl" />
            <h3 className="mt-4 text-xl font-semibold text-gray-900">Global Impact</h3>
            <p className="mt-2 text-gray-600">
              Be part of a global movement that ensures food security and community growth worldwide.
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
