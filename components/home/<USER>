



'use client'

import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON>, Star, TrendingUp, Award, Users } from 'lucide-react'
import Image from 'next/image'

// Define a type for the testimonial object
interface Testimonial {
  name: string
  role: string
  company: string
  photo: string
  quote: string
  rating: number
  investment: string
  returns: string
}

// Array of testimonials
const testimonials: Testimonial[] = [
  {
    name: '<PERSON>',
    role: 'Portfolio Manager',
    company: 'Meridian Capital',
    photo: '/sis.png',
    quote: 'TGM Finance has revolutionized our investment strategy. The platform\'s sophisticated analytics and consistent returns have exceeded our expectations. Our clients are seeing remarkable portfolio growth.',
    rating: 5,
    investment: '$2.5M',
    returns: '+24.7%'
  },
  {
    name: '<PERSON>',
    role: 'Chief Investment Officer',
    company: 'Apex Wealth Management',
    photo: '/Business Headshots.jfif',
    quote: 'The level of transparency and professional service at TGM Finance is unmatched. Their expert advisory team has helped us navigate complex market conditions with confidence.',
    rating: 5,
    investment: '$5.2M',
    returns: '+31.2%'
  },
  {
    name: 'Dr. <PERSON>',
    role: 'Private Investor',
    company: 'Independent',
    photo: '/guy.jfif',
    quote: 'As a high-net-worth individual, I demand excellence in wealth management. TGM Finance delivers institutional-grade solutions with personalized attention that\'s simply outstanding.',
    rating: 5,
    investment: '$1.8M',
    returns: '+28.9%'
  },
]

// TestimonialCard component with types
const TestimonialCard = ({ testimonial, index }: { testimonial: Testimonial; index: number }) => {
  const { name, role, company, photo, quote, rating, investment, returns } = testimonial

  return (
    <motion.div
      className="group relative p-8 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-500"
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8, delay: index * 0.2 }}
      whileHover={{ scale: 1.02, y: -5 }}
    >
      {/* Quote Icon */}
      <div className="absolute -top-4 left-8">
        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
          <Quote className="w-6 h-6 text-white" />
        </div>
      </div>

      {/* Rating */}
      <div className="flex items-center gap-1 mb-6 pt-4">
        {[...Array(rating)].map((_, i) => (
          <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
        ))}
      </div>

      {/* Quote */}
      <blockquote className="text-white text-lg leading-relaxed mb-8 italic">
        "{quote}"
      </blockquote>

      {/* Stats */}
      <div className="flex items-center gap-6 mb-6 p-4 bg-white/10 rounded-xl">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-400">{returns}</div>
          <div className="text-sm text-slate-300">Returns</div>
        </div>
        <div className="w-px h-12 bg-white/20"></div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-400">{investment}</div>
          <div className="text-sm text-slate-300">Investment</div>
        </div>
      </div>

      {/* Author */}
      <div className="flex items-center gap-4">
        <div className="relative">
          <Image
            src={photo}
            alt={name}
            width={60}
            height={60}
            className="rounded-full border-2 border-white/20"
          />
          <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
            <TrendingUp className="w-3 h-3 text-white" />
          </div>
        </div>
        <div>
          <div className="text-white font-semibold text-lg">{name}</div>
          <div className="text-slate-300 text-sm">{role}</div>
          <div className="text-blue-400 text-sm font-medium">{company}</div>
        </div>
      </div>
    </motion.div>
  )
}

export default function Testimonials() {
  return (
    <section className="relative py-24 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"></div>
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-blue-400/30 rounded-full text-blue-200 text-sm font-medium mb-6"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Users className="w-4 h-4" />
            Client Success Stories
          </motion.div>

          <h2 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-6">
            Trusted by Investors Worldwide
          </h2>

          <p className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
            Join thousands of successful investors who have transformed their financial future with TGM Finance.
          </p>
        </motion.div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard key={index} testimonial={testimonial} index={index} />
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 pt-16 border-t border-white/10"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="text-center">
            <div className="text-4xl font-bold text-white mb-2">50,000+</div>
            <div className="text-slate-300">Active Investors</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-white mb-2">$2.5B+</div>
            <div className="text-slate-300">Assets Managed</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-white mb-2">98.7%</div>
            <div className="text-slate-300">Client Satisfaction</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-white mb-2">25+</div>
            <div className="text-slate-300">Countries Served</div>
          </div>
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.button
            className="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Join Our Success Stories
            <Award className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}


