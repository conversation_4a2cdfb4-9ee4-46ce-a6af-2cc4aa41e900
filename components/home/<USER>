'use client'

import { motion } from 'framer-motion'
import { Shield, TrendingUp, Users, Zap, Award, Globe, Lock, BarChart3 } from 'lucide-react'

export default function KeyBenefits() {
  const benefits = [
    {
      icon: Shield,
      title: 'Bank-Grade Security',
      description: 'Your investments are protected by military-grade encryption and multi-layer security protocols.',
      gradient: 'from-amber-500 to-yellow-500',
      delay: 0.1
    },
    {
      icon: TrendingUp,
      title: 'Superior Returns',
      description: 'Access institutional-grade investment opportunities with potential for exceptional returns.',
      gradient: 'from-yellow-500 to-orange-500',
      delay: 0.2
    },
    {
      icon: BarChart3,
      title: 'Advanced Analytics',
      description: 'Make informed decisions with AI-powered insights and real-time market analysis.',
      gradient: 'from-orange-500 to-amber-500',
      delay: 0.3
    },
    {
      icon: Users,
      title: 'Expert Advisory',
      description: 'Get personalized guidance from certified financial advisors and investment specialists.',
      gradient: 'from-amber-600 to-yellow-600',
      delay: 0.4
    },
    {
      icon: Zap,
      title: 'Instant Execution',
      description: 'Lightning-fast trade execution and real-time portfolio management at your fingertips.',
      gradient: 'from-yellow-600 to-orange-600',
      delay: 0.5
    },
    {
      icon: Globe,
      title: 'Global Markets',
      description: 'Access diverse investment opportunities across international markets and asset classes.',
      gradient: 'from-orange-600 to-amber-600',
      delay: 0.6
    }
  ]

  return (
    <section className="relative py-24 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"></div>
        <div className="absolute inset-0 opacity-40">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Title */}
        <div className="text-center mb-20">
          <motion.div
            className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-blue-400/30 rounded-full text-blue-200 text-sm font-medium mb-6"
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Award className="w-4 h-4" />
            Premium Investment Platform
          </motion.div>

          <motion.h2
            className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-white via-amber-100 to-yellow-100 bg-clip-text text-transparent mb-6"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Why Choose TGM Finance?
          </motion.h2>

          <motion.p
            className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Experience the future of wealth management with our cutting-edge platform designed for sophisticated investors.
          </motion.p>
        </div>

        {/* Key Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <motion.div
              key={benefit.title}
              className="group relative p-8 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-500"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: benefit.delay }}
              whileHover={{ scale: 1.05, y: -10 }}
            >
              {/* Gradient Background */}
              <div className={`absolute inset-0 bg-gradient-to-br ${benefit.gradient} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-500`}></div>

              {/* Icon */}
              <div className={`relative inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${benefit.gradient} rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <benefit.icon className="w-8 h-8 text-white" />
              </div>

              {/* Content */}
              <div className="relative">
                <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-amber-200 transition-colors duration-300">
                  {benefit.title}
                </h3>
                <p className="text-slate-300 leading-relaxed group-hover:text-slate-200 transition-colors duration-300">
                  {benefit.description}
                </p>
              </div>

              {/* Hover Effect Border */}
              <div className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-white/30 transition-all duration-300"></div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.button
            className="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-amber-600 to-yellow-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Start Your Investment Journey
            <TrendingUp className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}
