'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { <PERSON>R<PERSON>, <PERSON>rk<PERSON>, TrendingUp, Shield, Award, Users } from 'lucide-react'

export default function CTASection() {
  const features = [
    { icon: Shield, text: 'Bank-Grade Security' },
    { icon: TrendingUp, text: 'Superior Returns' },
    { icon: Award, text: 'Expert Advisory' },
    { icon: Users, text: '50,000+ Investors' },
  ]

  return (
    <section className="relative py-32 bg-gradient-to-br from-slate-900 via-amber-900 to-yellow-900 overflow-hidden">
      {/* Background Image */}
      <Image
        src="/63ac0ff74380e1672220663.png"
        alt="CTA Background"
        fill
        className="object-cover object-center opacity-20"
      />

      {/* Enhanced Background Overlays */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-amber-900/60 to-yellow-900/80"></div>

      {/* Floating Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-amber-400/20 to-yellow-400/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-amber-500/10 to-yellow-500/10 rounded-full blur-3xl"></div>
      </div>

      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Premium Badge */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-amber-500/20 to-yellow-500/20 backdrop-blur-sm border border-amber-400/30 rounded-full text-amber-200 text-sm font-medium mb-8"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Sparkles className="w-5 h-5" />
            Transform Your Financial Future
            <Sparkles className="w-5 h-5" />
          </motion.div>
        </motion.div>

        {/* Main Content */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-white via-amber-100 to-yellow-100 bg-clip-text text-transparent mb-8 leading-tight"
          >
            Ready to Transform Your
            <br />
            <span className="bg-gradient-to-r from-amber-400 via-yellow-400 to-orange-400 bg-clip-text text-transparent">
              Investments?
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
            className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed mb-12"
          >
            Join TGM Finance and unlock premium investment opportunities with institutional-grade solutions,
            expert advisory, and cutting-edge technology designed for sophisticated investors.
          </motion.p>

          {/* Feature Pills */}
          <motion.div
            className="flex flex-wrap justify-center gap-4 mb-12"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            viewport={{ once: true }}
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.text}
                className="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 text-white text-sm"
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05 }}
              >
                <feature.icon className="w-4 h-4 text-amber-400" />
                {feature.text}
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Enhanced CTA Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.9 }}
          viewport={{ once: true }}
        >
          {/* Primary CTA */}
          <motion.a
            href="/register"
            className="group relative px-10 py-5 bg-gradient-to-r from-amber-500 to-yellow-500 text-white font-bold text-lg rounded-2xl shadow-2xl hover:shadow-amber-500/25 transition-all duration-300 overflow-hidden"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-amber-400 to-yellow-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative flex items-center gap-3">
              <span>Start Investing Today</span>
              <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
            </div>
            {/* Glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-2xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
          </motion.a>

          {/* Secondary CTA */}
          <motion.a
            href="/contact"
            className="group relative px-10 py-5 bg-white/10 backdrop-blur-sm border-2 border-amber-400/50 text-white font-bold text-lg rounded-2xl hover:bg-white/20 hover:border-amber-400 transition-all duration-300"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="relative flex items-center gap-3">
              <span>Learn More</span>
              <TrendingUp className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
            </div>
          </motion.a>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          className="mt-16 pt-12 border-t border-white/10"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.1 }}
          viewport={{ once: true }}
        >
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-white mb-2">$2.5B+</div>
              <div className="text-sm text-slate-300">Assets Managed</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-white mb-2">50,000+</div>
              <div className="text-sm text-slate-300">Active Investors</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-white mb-2">98.7%</div>
              <div className="text-sm text-slate-300">Satisfaction Rate</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-white mb-2">25+</div>
              <div className="text-sm text-slate-300">Countries Served</div>
            </div>
          </div>
        </motion.div>

        {/* Bottom Message */}
        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.3 }}
          viewport={{ once: true }}
        >
          <p className="text-amber-200 text-sm">
            🔒 Your investments are protected by bank-grade security • 📈 Start with as little as $100 • ⚡ Get started in under 5 minutes
          </p>
        </motion.div>
      </div>
    </section>
  )
}
