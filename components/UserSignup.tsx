// components/UserSignup.tsx


'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import LoadingButton from '@/components/ui/LoadingButton';
import Loading from '@/components/ui/loading';
import { FaUserPlus } from 'react-icons/fa';

import SuccessMessage from './SuccessMessage';
import ErrorMessage from './ErrorMessage';

interface UserSignupProps {
  onClose: () => void;
}

const UserSignup: React.FC<UserSignupProps> = ({ onClose }) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [phone, setPhone] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const validateForm = () => {
    if (!name || !email || !password || !phone) {
      setError('All fields are required');
      return false;
    }
    // Add more validation as needed (e.g., email format, password strength)
    return true;
  };

  const clearForm = () => {
    setName('');
    setEmail('');
    setPassword('');
    setPhone('');
  };

  const handleSignup = async () => {
    if (!validateForm()) {
      return;
    }

    setError(null);
    setLoading(true);

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, password, phone }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Something went wrong');
      }

      setSuccess(true);
      clearForm();

      // Redirect to login page after a short delay
      setTimeout(() => {
        onClose();
        router.push('/login');
      }, 2000);
    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : 'Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4 text-gray-800 dark:text-gray-200">
      <h2 className="text-lg font-semibold">Create Account</h2>
      {loading && (
        <div className="flex justify-center">
          <Loading variant="spinner" size="md" color="primary" text="Creating account..." />
        </div>
      )}
      {error && <ErrorMessage message={error} />}
      {success && <SuccessMessage message="Account created successfully! Redirecting to login..." />}

      {!loading && !success && (
        <form onSubmit={(e) => { e.preventDefault(); handleSignup(); }} className="space-y-4">
          <input
            type="text"
            placeholder="Name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full p-2 border rounded text-gray-800"
            required
          />
          <input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full p-2 border rounded text-gray-800"
            required
          />
          <input
            type="password"
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full p-2 border rounded text-gray-800"
            required
          />
          <input
            type="tel"
            placeholder="Phone Number"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            className="w-full p-2 border rounded text-gray-800"
            required
          />
          <LoadingButton
            type="submit"
            isLoading={loading}
            loadingText="Creating account..."
            variant="primary"
            size="md"
            fullWidth
            icon={<FaUserPlus />}
          >
            Sign Up
          </LoadingButton>
        </form>
      )}
    </div>
  );
};

export default UserSignup;

