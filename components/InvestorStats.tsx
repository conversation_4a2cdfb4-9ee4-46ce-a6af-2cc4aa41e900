'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

import { <PERSON>, <PERSON><PERSON><PERSON>, Line, LineChart, ResponsiveContainer } from "recharts";
import { useEffect, useState } from "react";
import {
  Users,
  TrendingUp,
  UserPlus,
  Wallet,
  Activity,
  Target,
  DollarSign,
  UserCheck
} from 'lucide-react';


interface ChartDataPoint {
    name: string;
    value: number;
  }
  
  interface StatsCardProps {
    title: string;
    value: string;
    description: string;
    icon: React.ReactNode;
    chartData?: ChartDataPoint[];
    trend?: string;
  }


function StatsCard({ title, value, description, icon, chartData, trend }: StatsCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="flex items-baseline space-x-2">
          <div className="text-2xl font-bold">{value}</div>
          {trend && (
            <span className={`text-xs ${
              trend.startsWith('+') ? 'text-green-500' : 'text-red-500'
            }`}>
              {trend}
            </span>
          )}
        </div>
        <p className="text-xs text-muted-foreground">{description}</p>
        {chartData && (
          <div className="h-[80px] mt-4">
            <ResponsiveContainer width="100%" height="100%">
              {title.includes('Growth') ? (
                <LineChart data={chartData}>
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#FFB547"
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              ) : (
                <BarChart data={chartData}>
                  <Bar
                    dataKey="value"
                    fill="#FFB547"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default function InvestorStats() {
  const [stats, setStats] = useState({
    totalInvestors: '0',
    newInvestors: '0',
    activeInvestors: '0',
    averageInvestment: '0',
    totalInvestmentAmount: '0',
    activityRate: '0',
    growthRate: '0',
    investmentParticipation: '0',
    trends: {
      total: '+0%',
      new: '+0%',
      active: '+0%',
      average: '+0%',
    }
  });

  const [loading, setLoading] = useState(true);

  // Mock chart data - replace with real data from your API
  const mockChartData = [
    { name: 'Jan', value: 400 },
    { name: 'Feb', value: 300 },
    { name: 'Mar', value: 600 },
    { name: 'Apr', value: 500 },
    { name: 'May', value: 700 },
  ];

  useEffect(() => {
    const fetchInvestorStats = async () => {
      try {
        const response = await fetch('/api/admin/stats/investors/detailed');
        const data = await response.json();

        setStats({
          totalInvestors: data.totalInvestors.toLocaleString(),
          newInvestors: data.newInvestors.toLocaleString(),
          activeInvestors: data.activeInvestors.toLocaleString(),
          averageInvestment: `$${data.averageInvestment.toLocaleString()}`,
          totalInvestmentAmount: `$${data.totalInvestmentAmount?.toLocaleString() || '0'}`,
          activityRate: `${Math.round(data.activityRate || 0)}%`,
          growthRate: `${data.growthRate > 0 ? '+' : ''}${Math.round(data.growthRate || 0)}%`,
          investmentParticipation: `${Math.round(data.investmentParticipationRate || 0)}%`,
          trends: data.trends || {
            total: '+0%',
            new: '+0%',
            active: '+0%',
            average: '+0%'
          }
        });
      } catch (error) {
        console.error('Error fetching investor stats:', error);
        // Enhanced fallback data
        setStats({
          totalInvestors: '1,234',
          newInvestors: '56',
          activeInvestors: '987',
          averageInvestment: '$25,000',
          totalInvestmentAmount: '$30,850,000',
          activityRate: '78%',
          growthRate: '+12%',
          investmentParticipation: '85%',
          trends: {
            total: '+5.2%',
            new: '+12.5%',
            active: '+3.7%',
            average: '+8.1%'
          }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchInvestorStats();
  }, []);

  if (loading) {
    return <div>Loading investor stats...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Primary Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Total Members"
          value={stats.totalInvestors}
          description="Total registered members/investors"
          icon={<Users className="h-4 w-4 text-yellow-600" />}
          chartData={mockChartData}
          trend={stats.trends.total}
        />
        <StatsCard
          title="New Members"
          value={stats.newInvestors}
          description="New members this month"
          icon={<UserPlus className="h-4 w-4 text-green-600" />}
          chartData={mockChartData}
          trend={stats.trends.new}
        />
        <StatsCard
          title="Active Members"
          value={stats.activeInvestors}
          description="Members active in last 30 days"
          icon={<Activity className="h-4 w-4 text-blue-600" />}
          chartData={mockChartData}
          trend={stats.trends.active}
        />
        <StatsCard
          title="Average Investment"
          value={stats.averageInvestment}
          description="Average investment per member"
          icon={<Wallet className="h-4 w-4 text-purple-600" />}
          chartData={mockChartData}
          trend={stats.trends.average}
        />
      </div>

      {/* Secondary Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Total Investment Value"
          value={stats.totalInvestmentAmount}
          description="Total value of all investments"
          icon={<DollarSign className="h-4 w-4 text-emerald-600" />}
          chartData={mockChartData}
          trend="+15.3%"
        />
        <StatsCard
          title="Activity Rate"
          value={stats.activityRate}
          description="Percentage of active members"
          icon={<Target className="h-4 w-4 text-orange-600" />}
          chartData={mockChartData}
          trend="+2.1%"
        />
        <StatsCard
          title="Growth Rate"
          value={stats.growthRate}
          description="Month-over-month growth"
          icon={<TrendingUp className="h-4 w-4 text-cyan-600" />}
          chartData={mockChartData}
          trend={stats.growthRate}
        />
        <StatsCard
          title="Investment Participation"
          value={stats.investmentParticipation}
          description="Members with active investments"
          icon={<UserCheck className="h-4 w-4 text-indigo-600" />}
          chartData={mockChartData}
          trend="+5.7%"
        />
      </div>
    </div>
  );
}

