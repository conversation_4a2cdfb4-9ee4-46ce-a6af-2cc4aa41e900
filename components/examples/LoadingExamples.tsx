'use client';

import React, { useState } from 'react';
import Loading from '@/components/ui/loading';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import LoadingButton from '@/components/ui/LoadingButton';
import { useLoading } from '@/context/LoadingContext';
import { FaDownload, FaSave, FaUpload } from 'react-icons/fa';

const LoadingExamples: React.FC = () => {
  const [componentLoading, setComponentLoading] = useState(false);
  const [buttonLoading, setButtonLoading] = useState(false);
  const [overlayLoading, setOverlayLoading] = useState(false);
  const { showLoading, hideLoading } = useLoading();

  const handleComponentLoading = () => {
    setComponentLoading(true);
    setTimeout(() => setComponentLoading(false), 3000);
  };

  const handleButtonLoading = () => {
    setButtonLoading(true);
    setTimeout(() => setButtonLoading(false), 2000);
  };

  const handleOverlayLoading = () => {
    setOverlayLoading(true);
    setTimeout(() => setOverlayLoading(false), 3000);
  };

  const handleGlobalLoading = () => {
    showLoading('Processing your request...');
    setTimeout(() => hideLoading(), 3000);
  };

  return (
    <div className="p-8 space-y-12 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">Loading Components Examples</h1>

        {/* Basic Loading Variants */}
        <section className="bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Basic Loading Variants</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center space-y-2">
              <h3 className="font-medium">Spinner</h3>
              <Loading variant="spinner" size="lg" color="primary" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-medium">Dots</h3>
              <Loading variant="dots" size="lg" color="primary" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-medium">Pulse</h3>
              <Loading variant="pulse" size="lg" color="primary" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-medium">Skeleton</h3>
              <Loading variant="skeleton" size="lg" />
            </div>
          </div>
        </section>

        {/* Loading Sizes */}
        <section className="bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Loading Sizes</h2>
          <div className="flex items-center justify-around">
            <div className="text-center space-y-2">
              <h3 className="font-medium">Small</h3>
              <Loading variant="spinner" size="sm" color="primary" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-medium">Medium</h3>
              <Loading variant="spinner" size="md" color="primary" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-medium">Large</h3>
              <Loading variant="spinner" size="lg" color="primary" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-medium">Extra Large</h3>
              <Loading variant="spinner" size="xl" color="primary" />
            </div>
          </div>
        </section>

        {/* Loading Colors */}
        <section className="bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Loading Colors</h2>
          <div className="flex items-center justify-around">
            <div className="text-center space-y-2">
              <h3 className="font-medium">Primary</h3>
              <Loading variant="spinner" size="lg" color="primary" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-medium">Secondary</h3>
              <Loading variant="spinner" size="lg" color="secondary" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-medium">Accent</h3>
              <Loading variant="spinner" size="lg" color="accent" />
            </div>
            <div className="text-center space-y-2 bg-gray-800 p-4 rounded">
              <h3 className="font-medium text-white">White</h3>
              <Loading variant="spinner" size="lg" color="white" />
            </div>
          </div>
        </section>

        {/* Loading Wrapper Examples */}
        <section className="bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Loading Wrapper</h2>
          <div className="space-y-4">
            <button
              onClick={handleComponentLoading}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Toggle Component Loading
            </button>
            
            <LoadingWrapper
              isLoading={componentLoading}
              loadingText="Loading content..."
              className="border rounded-lg p-4"
            >
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Sample Content</h3>
                <p>This content will be replaced with a loading state when the button is clicked.</p>
                <div className="grid grid-cols-3 gap-4">
                  <div className="h-20 bg-gray-100 rounded"></div>
                  <div className="h-20 bg-gray-100 rounded"></div>
                  <div className="h-20 bg-gray-100 rounded"></div>
                </div>
              </div>
            </LoadingWrapper>
          </div>
        </section>

        {/* Overlay Loading */}
        <section className="bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Overlay Loading</h2>
          <div className="space-y-4">
            <button
              onClick={handleOverlayLoading}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Toggle Overlay Loading
            </button>
            
            <LoadingWrapper
              isLoading={overlayLoading}
              loadingText="Processing..."
              overlay={true}
              className="border rounded-lg p-4 h-64"
            >
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Content with Overlay</h3>
                <p>This content remains visible but gets an overlay when loading.</p>
                <div className="grid grid-cols-2 gap-4">
                  <div className="h-20 bg-gray-100 rounded"></div>
                  <div className="h-20 bg-gray-100 rounded"></div>
                </div>
              </div>
            </LoadingWrapper>
          </div>
        </section>

        {/* Loading Buttons */}
        <section className="bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Loading Buttons</h2>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <LoadingButton
                isLoading={buttonLoading}
                loadingText="Saving..."
                onClick={handleButtonLoading}
                icon={<FaSave />}
              >
                Save Document
              </LoadingButton>
              
              <LoadingButton
                isLoading={buttonLoading}
                loadingText="Downloading..."
                onClick={handleButtonLoading}
                variant="secondary"
                icon={<FaDownload />}
              >
                Download File
              </LoadingButton>
              
              <LoadingButton
                isLoading={buttonLoading}
                loadingText="Uploading..."
                onClick={handleButtonLoading}
                variant="outline"
                icon={<FaUpload />}
              >
                Upload Image
              </LoadingButton>
            </div>
          </div>
        </section>

        {/* Global Loading */}
        <section className="bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Global Loading</h2>
          <div className="space-y-4">
            <button
              onClick={handleGlobalLoading}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
            >
              Show Global Loading
            </button>
            <p className="text-sm text-gray-600">
              This will show a full-screen loading overlay using the global loading context.
            </p>
          </div>
        </section>
      </div>
    </div>
  );
};

export default LoadingExamples;
