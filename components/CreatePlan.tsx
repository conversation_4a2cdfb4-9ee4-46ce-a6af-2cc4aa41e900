'use client';

import React, { useState } from 'react';
import SpinnerLoader from './SpinnerLoader';
import SuccessMessage from './SuccessMessage';
import ErrorMessage from './ErrorMessage';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Plan } from '@/types/plan';
import {
  DollarSign,
  Percent,
  TrendingUp,
  Calendar,
  Tag,
  Activity,
  Plus,
  AlertCircle,
  CheckCircle,
  Sparkles
} from 'lucide-react';

interface CreatePlanProps {
  onClose: () => void;
  onPlanCreated: (newPlan: Plan) => void;
}

const CreatePlan: React.FC<CreatePlanProps> = ({ onClose, onPlanCreated }) => {
  const [title, setTitle] = useState('');
  const [priceRange, setPriceRange] = useState('');
  const [percentage, setPercentage] = useState('');
  const [profit, setProfit] = useState('');
  const [capitalReturn, setCapitalReturn] = useState('No');
  const [totalPercentage, setTotalPercentage] = useState('');
  const [duration, setDuration] = useState<number>(30);
  const [planType, setPlanType] = useState<'normal' | 'promotional'>('normal');
  const [status, setStatus] = useState<'active' | 'inactive' | 'archived'>('active');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);

  const clearForm = () => {
    setTitle('');
    setPriceRange('');
    setPercentage('');
    setProfit('');
    setCapitalReturn('No');
    setTotalPercentage('');
    setDuration(30);
    setPlanType('normal');
    setStatus('active');
  };

  const validateForm = () => {
    if (
      !title ||
      !priceRange ||
      !percentage ||
      !profit ||
      !capitalReturn ||
      !totalPercentage ||
      !duration ||
      !planType ||
      !status
    ) {
      setError('All fields are required');
      return false;
    }
    return true;
  };

  const handleCreatePlan = async () => {
    if (!validateForm()) return;

    setError(null);
    setLoading(true);

    try {
      const response = await fetch('/api/plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          priceRange,
          percentage,
          profit,
          capitalReturn,
          totalPercentage,
          duration,
          planType,
          status,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || 'Something went wrong');
        setLoading(false);
        return;
      }

      setSuccess(true);
      setLoading(false);

      onPlanCreated(data);

      clearForm();

      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      console.error('Error:', err);
      setError('Something went wrong');
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 z-50">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-amber-500/20 to-yellow-500/20 backdrop-blur-sm border border-amber-400/30 rounded-full text-amber-600 text-sm font-medium mb-4">
          <Sparkles className="w-4 h-4" />
          Admin Panel
        </div>
        <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-900 via-amber-900 to-yellow-900 bg-clip-text text-transparent">
          Create New Investment Plan
        </h2>
        <p className="text-slate-600 mt-2">Configure a new investment plan for your clients</p>
      </div>

      {loading && (
        <div className="flex justify-center py-8">
          <div className="flex flex-col items-center gap-4">
            <div className="w-12 h-12 border-4 border-amber-500 border-t-transparent rounded-full animate-spin" />
            <p className="text-amber-600 font-medium">Creating plan...</p>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-xl">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-500 text-green-700 p-4 rounded-xl">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5" />
            <span>Plan created successfully!</span>
          </div>
        </div>
      )}

      {!loading && !success && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                <Tag className="w-4 h-4 inline mr-2" />
                Plan Title
              </label>
              <Input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter plan title"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                <DollarSign className="w-4 h-4 inline mr-2" />
                Price Range
              </label>
              <Input
                type="text"
                value={priceRange}
                onChange={(e) => setPriceRange(e.target.value)}
                placeholder="e.g., $1000 - $5000"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                <Percent className="w-4 h-4 inline mr-2" />
                Return Percentage
              </label>
              <Input
                type="text"
                value={percentage}
                onChange={(e) => setPercentage(e.target.value)}
                placeholder="e.g., 15%"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                <TrendingUp className="w-4 h-4 inline mr-2" />
                Expected Profit
              </label>
              <Input
                type="text"
                value={profit}
                onChange={(e) => setProfit(e.target.value)}
                placeholder="e.g., $150"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                <Activity className="w-4 h-4 inline mr-2" />
                Capital Return
              </label>
              <select
                value={capitalReturn}
                onChange={(e) => setCapitalReturn(e.target.value)}
                className="w-full h-12 px-4 py-3 rounded-xl border-2 border-slate-200 bg-white/80 backdrop-blur-sm text-base shadow-sm transition-all duration-300 hover:border-amber-300 focus:border-amber-500 focus:bg-white focus:shadow-lg focus:shadow-amber-500/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-amber-500/20"
              >
                <option value="No">No</option>
                <option value="Yes">Yes</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                <Percent className="w-4 h-4 inline mr-2" />
                Total Percentage
              </label>
              <Input
                type="text"
                value={totalPercentage}
                onChange={(e) => setTotalPercentage(e.target.value)}
                placeholder="e.g., 115%"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                <Calendar className="w-4 h-4 inline mr-2" />
                Duration (Days)
              </label>
              <Input
                type="number"
                value={duration}
                onChange={(e) => setDuration(Number(e.target.value))}
                placeholder="e.g., 30"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                <Tag className="w-4 h-4 inline mr-2" />
                Plan Type
              </label>
              <select
                value={planType}
                onChange={(e) => setPlanType(e.target.value as 'normal' | 'promotional')}
                className="w-full h-12 px-4 py-3 rounded-xl border-2 border-slate-200 bg-white/80 backdrop-blur-sm text-base shadow-sm transition-all duration-300 hover:border-amber-300 focus:border-amber-500 focus:bg-white focus:shadow-lg focus:shadow-amber-500/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-amber-500/20"
              >
                <option value="normal">Normal</option>
                <option value="promotional">Promotional</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                <Activity className="w-4 h-4 inline mr-2" />
                Status
              </label>
              <select
                value={status}
                onChange={(e) => setStatus(e.target.value as 'active' | 'inactive' | 'archived')}
                className="w-full h-12 px-4 py-3 rounded-xl border-2 border-slate-200 bg-white/80 backdrop-blur-sm text-base shadow-sm transition-all duration-300 hover:border-amber-300 focus:border-amber-500 focus:bg-white focus:shadow-lg focus:shadow-amber-500/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-amber-500/20"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="archived">Archived</option>
              </select>
            </div>
          </div>

          <div className="mt-8">
            <Button
              onClick={handleCreatePlan}
              disabled={loading}
              className="w-full"
              size="lg"
            >
              <Plus className="w-5 h-5 mr-2" />
              Create Investment Plan
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default CreatePlan;
