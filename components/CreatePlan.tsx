

// // components/CreatePlan.tsx
// 'use client';

// import React, { useState } from 'react';
// import SpinnerLoader from './SpinnerLoader';
// import SuccessMessage from './SuccessMessage';
// import ErrorMessage from './ErrorMessage';
// import { Plan } from '@/types/plan';

// interface CreatePlanProps {
//   onClose: () => void;
//   onPlanCreated: (newPlan: Plan) => void;
// }

// const CreatePlan: React.FC<CreatePlanProps> = ({ onClose, onPlanCreated }) => {
//   const [title, setTitle] = useState('');
//   const [priceRange, setPriceRange] = useState('');
//   const [percentage, setPercentage] = useState('');
//   const [profit, setProfit] = useState('');
//   const [capitalReturn, setCapitalReturn] = useState('No'); // Default to 'No'
//   const [totalPercentage, setTotalPercentage] = useState('');
//   const [duration, setDuration] = useState<number>(30); // Default duration
//   const [error, setError] = useState<string | null>(null);
//   const [success, setSuccess] = useState(false);
//   const [loading, setLoading] = useState(false);

//   // Clear form inputs
//   const clearForm = () => {
//     setTitle('');
//     setPriceRange('');
//     setPercentage('');
//     setProfit('');
//     setCapitalReturn('No');
//     setTotalPercentage('');
//     setDuration(30);
//   };

//   const validateForm = () => {
//     if (
//       !title ||
//       !priceRange ||
//       !percentage ||
//       !profit ||
//       !capitalReturn ||
//       !totalPercentage ||
//       !duration
//     ) {
//       setError('All fields are required');
//       return false;
//     }
//     return true;
//   };

//   const handleCreatePlan = async () => {
//     if (!validateForm()) return;

//     setError(null);
//     setLoading(true);

//     try {
//       const response = await fetch('/api/plans', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           title,
//           priceRange,
//           percentage,
//           profit,
//           capitalReturn,
//           totalPercentage,
//           duration,
//         }),
//       });

//       const data = await response.json();

//       if (!response.ok) {
//         setError(data.error || 'Something went wrong');
//         setLoading(false);
//         return;
//       }

//       setSuccess(true);
//       setLoading(false);

//       // Notify parent component of the new plan
//       onPlanCreated(data);

//       clearForm();

//       setTimeout(() => {
//         onClose();
//       }, 2000);
//     } catch (err) {
//       console.error('Error:', err);
//       setError('Something went wrong');
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="space-y-4 z-50">
//       <h2 className="text-lg font-semibold">Create New Plan</h2>

//       {/* Display messages based on state */}
//       {loading && <SpinnerLoader />} {/* Show loader during creation */}
//       {error && <ErrorMessage message={error} />} {/* Show error message */}
//       {success && <SuccessMessage message="Plan created successfully!" />} {/* Show success message */}

//       {/* Form Fields */}
//       <div>
//         <label className="block font-medium">Plan Title</label>
//         <input
//           type="text"
//           placeholder="Plan Title"
//           value={title}
//           onChange={(e) => setTitle(e.target.value)}
//           className="w-full p-2 border rounded"
//         />
//       </div>

//       <div>
//         <label className="block font-medium">Price Range</label>
//         <input
//           type="text"
//           placeholder="Price Range"
//           value={priceRange}
//           onChange={(e) => setPriceRange(e.target.value)}
//           className="w-full p-2 border rounded"
//         />
//       </div>

//       <div>
//         <label className="block font-medium">Percentage</label>
//         <input
//           type="text"
//           placeholder="Percentage"
//           value={percentage}
//           onChange={(e) => setPercentage(e.target.value)}
//           className="w-full p-2 border rounded"
//         />
//       </div>

//       <div>
//         <label className="block font-medium">Profit</label>
//         <input
//           type="text"
//           placeholder="Profit"
//           value={profit}
//           onChange={(e) => setProfit(e.target.value)}
//           className="w-full p-2 border rounded"
//         />
//       </div>

//       <div>
//         <label className="block font-medium">Capital Return</label>
//         <select
//           value={capitalReturn}
//           onChange={(e) => setCapitalReturn(e.target.value)}
//           className="w-full p-2 border rounded"
//         >
//           <option value="No">No</option>
//           <option value="Yes">Yes</option>
//         </select>
//       </div>

//       <div>
//         <label className="block font-medium">Total Percentage</label>
//         <input
//           type="text"
//           placeholder="Total Percentage"
//           value={totalPercentage}
//           onChange={(e) => setTotalPercentage(e.target.value)}
//           className="w-full p-2 border rounded"
//         />
//       </div>

//       <div>
//         <label className="block font-medium">Duration (in days)</label>
//         <input
//           type="number"
//           placeholder="Duration in days"
//           value={duration}
//           onChange={(e) => setDuration(Number(e.target.value))}
//           className="w-full p-2 border rounded"
//         />
//       </div>

//       <button
//         onClick={handleCreatePlan}
//         className="w-full bg-yellow-700 text-white p-2 rounded"
//         disabled={loading}
//       >
//         {loading ? 'Creating...' : 'Create Plan'}
//       </button>
//     </div>
//   );
// };

// export default CreatePlan;



// 'use client';

// import React, { useState } from 'react';
// import SpinnerLoader from './SpinnerLoader';
// import SuccessMessage from './SuccessMessage';
// import ErrorMessage from './ErrorMessage';
// import { Plan } from '@/types/plan';

// interface CreatePlanProps {
//   onClose: () => void;
//   onPlanCreated: (newPlan: Plan) => void;
// }

// const CreatePlan: React.FC<CreatePlanProps> = ({ onClose, onPlanCreated }) => {
//   const [title, setTitle] = useState('');
//   const [priceRange, setPriceRange] = useState('');
//   const [percentage, setPercentage] = useState('');
//   const [profit, setProfit] = useState('');
//   const [capitalReturn, setCapitalReturn] = useState('No');
//   const [totalPercentage, setTotalPercentage] = useState('');
//   const [duration, setDuration] = useState<number>(30);
//   const [planType, setPlanType] = useState<'normal' | 'promotional'>('normal');
//   const [error, setError] = useState<string | null>(null);
//   const [success, setSuccess] = useState(false);
//   const [loading, setLoading] = useState(false);

//   const clearForm = () => {
//     setTitle('');
//     setPriceRange('');
//     setPercentage('');
//     setProfit('');
//     setCapitalReturn('No');
//     setTotalPercentage('');
//     setDuration(30);
//     setPlanType('normal');
//   };

//   const validateForm = () => {
//     if (
//       !title ||
//       !priceRange ||
//       !percentage ||
//       !profit ||
//       !capitalReturn ||
//       !totalPercentage ||
//       !duration ||
//       !planType
//     ) {
//       setError('All fields are required');
//       return false;
//     }
//     return true;
//   };

//   const handleCreatePlan = async () => {
//     if (!validateForm()) return;

//     setError(null);
//     setLoading(true);

//     try {
//       const response = await fetch('/api/plans', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           title,
//           priceRange,
//           percentage,
//           profit,
//           capitalReturn,
//           totalPercentage,
//           duration,
//           planType,
//         }),
//       });

//       const data = await response.json();

//       if (!response.ok) {
//         setError(data.error || 'Something went wrong');
//         setLoading(false);
//         return;
//       }

//       setSuccess(true);
//       setLoading(false);

//       onPlanCreated(data);

//       clearForm();

//       setTimeout(() => {
//         onClose();
//       }, 2000);
//     } catch (err) {
//       console.error('Error:', err);
//       setError('Something went wrong');
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="space-y-4 z-50">
//       <h2 className="text-lg font-semibold">Create New Plan</h2>

//       {loading && <SpinnerLoader />}
//       {error && <ErrorMessage message={error} />}
//       {success && <SuccessMessage message="Plan created successfully!" />}

//       {/* ... (previous form fields remain the same) */}

//       <div>
//         <label className="block font-medium">Plan Type</label>
//         <select
//           value={planType}
//           onChange={(e) => setPlanType(e.target.value as 'normal' | 'promotional')}
//           className="w-full p-2 border rounded"
//         >
//           <option value="normal">Normal</option>
//           <option value="promotional">Promotional</option>
//         </select>
//       </div>

//       <button
//         onClick={handleCreatePlan}
//         className="w-full bg-yellow-700 text-white p-2 rounded"
//         disabled={loading}
//       >
//         {loading ? 'Creating...' : 'Create Plan'}
//       </button>
//     </div>
//   );
// };

// export default CreatePlan;




'use client';

import React, { useState } from 'react';
import SpinnerLoader from './SpinnerLoader';
import SuccessMessage from './SuccessMessage';
import ErrorMessage from './ErrorMessage';
import { Plan } from '@/types/plan';

interface CreatePlanProps {
  onClose: () => void;
  onPlanCreated: (newPlan: Plan) => void;
}

const CreatePlan: React.FC<CreatePlanProps> = ({ onClose, onPlanCreated }) => {
  const [title, setTitle] = useState('');
  const [priceRange, setPriceRange] = useState('');
  const [percentage, setPercentage] = useState('');
  const [profit, setProfit] = useState('');
  const [capitalReturn, setCapitalReturn] = useState('No');
  const [totalPercentage, setTotalPercentage] = useState('');
  const [duration, setDuration] = useState<number>(30);
  const [planType, setPlanType] = useState<'normal' | 'promotional'>('normal');
  const [status, setStatus] = useState<'active' | 'inactive' | 'archived'>('active');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);

  const clearForm = () => {
    setTitle('');
    setPriceRange('');
    setPercentage('');
    setProfit('');
    setCapitalReturn('No');
    setTotalPercentage('');
    setDuration(30);
    setPlanType('normal');
    setStatus('active');
  };

  const validateForm = () => {
    if (
      !title ||
      !priceRange ||
      !percentage ||
      !profit ||
      !capitalReturn ||
      !totalPercentage ||
      !duration ||
      !planType ||
      !status
    ) {
      setError('All fields are required');
      return false;
    }
    return true;
  };

  const handleCreatePlan = async () => {
    if (!validateForm()) return;

    setError(null);
    setLoading(true);

    try {
      const response = await fetch('/api/plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          priceRange,
          percentage,
          profit,
          capitalReturn,
          totalPercentage,
          duration,
          planType,
          status,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || 'Something went wrong');
        setLoading(false);
        return;
      }

      setSuccess(true);
      setLoading(false);

      onPlanCreated(data);

      clearForm();

      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      console.error('Error:', err);
      setError('Something went wrong');
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4 z-50">
      <h2 className="text-lg font-semibold">Create New Plan</h2>

      {loading && <SpinnerLoader />}
      {error && <ErrorMessage message={error} />}
      {success && <SuccessMessage message="Plan created successfully!" />}

      <div>
        <label className="block font-medium">Title</label>
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full p-2 border rounded"
          placeholder="Enter plan title"
        />
      </div>

      <div>
        <label className="block font-medium">Price Range</label>
        <input
          type="text"
          value={priceRange}
          onChange={(e) => setPriceRange(e.target.value)}
          className="w-full p-2 border rounded"
          placeholder="Enter price range"
        />
      </div>

      <div>
        <label className="block font-medium">Percentage</label>
        <input
          type="text"
          value={percentage}
          onChange={(e) => setPercentage(e.target.value)}
          className="w-full p-2 border rounded"
          placeholder="Enter percentage"
        />
      </div>

      <div>
        <label className="block font-medium">Profit</label>
        <input
          type="text"
          value={profit}
          onChange={(e) => setProfit(e.target.value)}
          className="w-full p-2 border rounded"
          placeholder="Enter profit"
        />
      </div>

      <div>
        <label className="block font-medium">Capital Return</label>
        <select
          value={capitalReturn}
          onChange={(e) => setCapitalReturn(e.target.value)}
          className="w-full p-2 border rounded"
        >
          <option value="No">No</option>
          <option value="Yes">Yes</option>
        </select>
      </div>

      <div>
        <label className="block font-medium">Total Percentage</label>
        <input
          type="text"
          value={totalPercentage}
          onChange={(e) => setTotalPercentage(e.target.value)}
          className="w-full p-2 border rounded"
          placeholder="Enter total percentage"
        />
      </div>

      <div>
        <label className="block font-medium">Duration (in days)</label>
        <input
          type="number"
          value={duration}
          onChange={(e) => setDuration(Number(e.target.value))}
          className="w-full p-2 border rounded"
          placeholder="Enter duration in days"
        />
      </div>

      <div>
        <label className="block font-medium">Plan Type</label>
        <select
          value={planType}
          onChange={(e) => setPlanType(e.target.value as 'normal' | 'promotional')}
          className="w-full p-2 border rounded"
        >
          <option value="normal">Normal</option>
          <option value="promotional">Promotional</option>
        </select>
      </div>

      <div>
        <label className="block font-medium">Status</label>
        <select
          value={status}
          onChange={(e) => setStatus(e.target.value as 'active' | 'inactive' | 'archived')}
          className="w-full p-2 border rounded"
        >
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="archived">Archived</option>
        </select>
      </div>

      <button
        onClick={handleCreatePlan}
        className="w-full bg-yellow-700 text-white p-2 rounded"
        disabled={loading}
      >
        {loading ? 'Creating...' : 'Create Plan'}
      </button>
    </div>
  );
};

export default CreatePlan;

