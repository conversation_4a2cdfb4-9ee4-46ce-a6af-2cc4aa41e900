'use client';

import React from 'react';
import { Dialog, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { formatCurrencyDisplay } from '@/lib/currency';

interface InvestmentDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  investment: {
    userId: { name: string; email: string };
    planId: { title: string; percentage: number };
    amount: number;
    earningRate: number;
    paymentStatus: string;
    startDate: string;
    endDate: string;
  };
}

const InvestmentDetailsModal: React.FC<InvestmentDetailsModalProps> = ({
  isOpen,
  onClose,
  investment,
}) => {
  if (!investment) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Investment Details</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <p><strong>User:</strong> {investment.userId.name} ({investment.userId.email})</p>
          <p><strong>Plan:</strong> {investment.planId.title}</p>
          <p><strong>Amount:</strong> {formatCurrencyDisplay(investment.amount)}</p>
          <p><strong>Earning Rate:</strong> {investment.earningRate}%</p>
          <p><strong>Start Date:</strong> {new Date(investment.startDate).toLocaleDateString()}</p>
          <p><strong>End Date:</strong> {new Date(investment.endDate).toLocaleDateString()}</p>
          <p><strong>Status:</strong> {investment.paymentStatus}</p>
          <Button variant="outline" onClick={onClose}>Close</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default InvestmentDetailsModal;
