// 'use client';

// import React, { useState, useEffect } from 'react';
// import {
//   Card,
//   CardContent,
//   CardHeader,
//   CardTitle,
// } from '@/components/ui/card';
// import { Input } from '@/components/ui/input';
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from '@/components/ui/table';
// import { Badge } from '@/components/ui/badge';
// // import { Button } from '@/components/ui/button';
// import { Skeleton } from '@/components/ui/skeleton';
// import { Search } from 'lucide-react';

// interface Referral {
//   _id: string;
//   level: number; // Referral level (1 to 4)
//   userId: { name: string; email: string };
//   investmentTotal: number; // Total investments by the referral
//   status: string; // Active or Inactive based on investment activity
// }

// const ReferralPyramidTable: React.FC = () => {
//   const [referrals, setReferrals] = useState<Referral[]>([]);
//   const [loading, setLoading] = useState(true);
//   const [searchQuery, setSearchQuery] = useState('');

//   useEffect(() => {
//     fetchReferralPyramid();
//   }, [searchQuery]);

//   const fetchReferralPyramid = async () => {
//     setLoading(true);
//     try {
//       const response = await fetch(`/api/referrals/pyramid?search=${searchQuery}`, {
//         method: 'GET',
//         headers: {
//           Authorization: `Bearer ${localStorage.getItem('token')}`,
//         },
//       });
//       const data = await response.json();
//       setReferrals(data.referrals);
//     } catch (error) {
//       console.error('Error fetching referral pyramid:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const getBadgeVariant = (status: string): 'secondary' | 'default' | 'destructive' | 'outline' => {
//     switch (status) {
//       case 'Active':
//         return 'default';
//       case 'Inactive':
//         return 'destructive';
//       default:
//         return 'secondary';
//     }
//   };

//   const renderSkeletonRow = () => (
//     <TableRow>
//       <TableCell>
//         <Skeleton className="h-4 w-[150px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[150px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[100px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[100px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[80px]" />
//       </TableCell>
//     </TableRow>
//   );

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>Referral Pyramid</CardTitle>
//       </CardHeader>
//       <CardContent>
//         <div className="flex justify-between items-center mb-4">
//           <div className="relative w-64">
//             <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
//             <Input
//               type="text"
//               placeholder="Search referrals"
//               value={searchQuery}
//               onChange={(e) => setSearchQuery(e.target.value)}
//               className="pl-8"
//             />
//           </div>
//         </div>

//         <div className="rounded-md border">
//           <Table>
//             <TableHeader>
//               <TableRow>
//                 <TableHead>Level</TableHead>
//                 <TableHead>User</TableHead>
//                 <TableHead>Email</TableHead>
//                 <TableHead>Total Investment</TableHead>
//                 <TableHead>Status</TableHead>
//               </TableRow>
//             </TableHeader>
//             <TableBody>
//               {loading
//                 ? Array.from({ length: 5 }).map((_, index) => (
//                     <TableRow key={index}>{renderSkeletonRow()}</TableRow>
//                   ))
//                 : referrals.length > 0
//                 ? referrals.map((referral) => (
//                     <TableRow key={referral._id}>
//                       <TableCell>Level {referral.level}</TableCell>
//                       <TableCell>{referral.userId.name}</TableCell>
//                       <TableCell>{referral.userId.email}</TableCell>
//                       <TableCell>${referral.investmentTotal.toLocaleString()}</TableCell>
//                       <TableCell>
//                         <Badge variant={getBadgeVariant(referral.status)}>
//                           {referral.status}
//                         </Badge>
//                       </TableCell>
//                     </TableRow>
//                   ))
//                 : (
//                   <TableRow>
//                     <TableCell colSpan={5} className="text-center text-muted-foreground">
//                       No referrals found
//                     </TableCell>
//                   </TableRow>
//                 )}
//             </TableBody>
//           </Table>
//         </div>
//       </CardContent>
//     </Card>
//   );
// };

// export default ReferralPyramidTable;


// 'use client';

// import React, { useState, useEffect } from 'react';
// import {
//   Card,
//   CardContent,
//   CardHeader,
//   CardTitle,
// } from '@/components/ui/card';
// import { Input } from '@/components/ui/input';
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from '@/components/ui/table';
// import { Badge } from '@/components/ui/badge';
// import { Skeleton } from '@/components/ui/skeleton';
// import { Search } from 'lucide-react';

// interface Referral {
//   _id: string;
//   level: number; // Referral level (1 to 4)
//   userId: { name: string; email: string };
//   investmentTotal: number; // Total investments by the referral
//   status: string; // Active or Inactive based on investment activity
// }

// const ReferralPyramidTable: React.FC = () => {
//   const [referrals, setReferrals] = useState<Referral[]>([]);
//   const [loading, setLoading] = useState(true);
//   const [searchQuery, setSearchQuery] = useState('');
//   const userId = localStorage.getItem('userId'); // Assumes userId is stored in localStorage.

//   useEffect(() => {
//     if (userId) fetchReferralPyramid();
//   }, [userId, searchQuery]);

//   /**
//    * Fetch referral pyramid data for the logged-in user.
//    */
//   const fetchReferralPyramid = async () => {
//     if (!userId) {
//       console.error('User ID is not available.');
//       setReferrals([]);
//       setLoading(false);
//       return;
//     }

//     setLoading(true);
//     try {
//       const response = await fetch(
//         `/api/referrals/pyramid/${userId}?search=${encodeURIComponent(searchQuery)}`,
//         {
//           method: 'GET',
//           headers: {
//             Authorization: `Bearer ${localStorage.getItem('token')}`,
//           },
//         }
//       );

//       if (!response.ok) {
//         throw new Error(`Failed to fetch referrals: ${response.statusText}`);
//       }

//       const data = await response.json();
//       setReferrals(flattenPyramid(data.pyramid));
//     } catch (error) {
//       console.error('Error fetching referral pyramid:', error);
//       setReferrals([]);
//     } finally {
//       setLoading(false);
//     }
//   };

//   /**
//    * Flattens the referral pyramid into a single array for easier rendering.
//    * Adds the referral level to each entry.
//    */
//   const flattenPyramid = (pyramid: { [key: string]: any[] }) => {
//     const levels = Object.entries(pyramid);
//     return levels.flatMap(([levelKey, referrals]) =>
//       referrals.map((referral) => ({
//         ...referral,
//         level: parseInt(levelKey.replace('level', ''), 10), // Extract level number from key
//       }))
//     );
//   };

//   /**
//    * Get a variant for the Badge component based on the referral status.
//    */
//   const getBadgeVariant = (status: string): 'secondary' | 'default' | 'destructive' | 'outline' => {
//     switch (status) {
//       case 'Active':
//         return 'default';
//       case 'Inactive':
//         return 'destructive';
//       default:
//         return 'secondary';
//     }
//   };

//   /**
//    * Render a skeleton row for loading state.
//    */
//   const renderSkeletonRow = () => (
//     <TableRow>
//       {[...Array(5)].map((_, index) => (
//         <TableCell key={index}>
//           <Skeleton className="h-4 w-full" />
//         </TableCell>
//       ))}
//     </TableRow>
//   );

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>Referral Pyramid</CardTitle>
//       </CardHeader>
//       <CardContent>
//         <div className="flex justify-between items-center mb-4">
//           <div className="relative w-64">
//             <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
//             <Input
//               type="text"
//               placeholder="Search referrals"
//               value={searchQuery}
//               onChange={(e) => setSearchQuery(e.target.value)}
//               className="pl-8"
//             />
//           </div>
//         </div>

//         <div className="rounded-md border">
//           <Table>
//             <TableHeader>
//               <TableRow>
//                 <TableHead>Level</TableHead>
//                 <TableHead>User</TableHead>
//                 <TableHead>Email</TableHead>
//                 <TableHead>Total Investment</TableHead>
//                 <TableHead>Status</TableHead>
//               </TableRow>
//             </TableHeader>
//             <TableBody>
//               {loading
//                 ? Array.from({ length: 5 }, (_, index) => (
//                     <TableRow key={index}>{renderSkeletonRow()}</TableRow>
//                   ))
//                 : referrals.length > 0
//                 ? referrals.map((referral) => (
//                     <TableRow key={referral._id}>
//                       <TableCell>Level {referral.level}</TableCell>
//                       <TableCell>{referral.userId.name}</TableCell>
//                       <TableCell>{referral.userId.email}</TableCell>
//                       <TableCell>${referral.investmentTotal.toLocaleString()}</TableCell>
//                       <TableCell>
//                         <Badge variant={getBadgeVariant(referral.status)}>
//                           {referral.status}
//                         </Badge>
//                       </TableCell>
//                     </TableRow>
//                   ))
//                 : (
//                   <TableRow>
//                     <TableCell colSpan={5} className="text-center text-muted-foreground">
//                       No referrals found
//                     </TableCell>
//                   </TableRow>
//                 )}
//             </TableBody>
//           </Table>
//         </div>
//       </CardContent>
//     </Card>
//   );
// };

// export default ReferralPyramidTable;

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Search } from 'lucide-react';

interface Referral {
  _id: string;
  level: number; // Referral level (1 to 4)
  userId: { name: string; email: string };
  investmentTotal: number; // Total investments by the referral
  status: string; // Active or Inactive based on investment activity
}

interface PyramidData {
  level1: Referral[];
  level2: Referral[];
  level3: Referral[];
  level4: Referral[];
}

const ReferralPyramidTable: React.FC = () => {
  const [referrals, setReferrals] = useState<Referral[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState(searchQuery);
  const [userId, setUserId] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(null);

  // Fetch userId and token on component mount
  useEffect(() => {
    setUserId(localStorage.getItem('userId'));
    setToken(localStorage.getItem('token'));
  }, []);

  // Debounce search input changes
  useEffect(() => {
    const timeout = setTimeout(() => setDebouncedSearch(searchQuery), 300);
    return () => clearTimeout(timeout);
  }, [searchQuery]);

  // Fetch referral pyramid
  const fetchReferralPyramid = useCallback(async () => {
    if (!userId || !token) {
      console.error('User ID or token is not available.');
      setReferrals([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(
        `/api/referrals/pyramid/${userId}?search=${encodeURIComponent(debouncedSearch)}`,
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch referrals: ${response.statusText}`);
      }

      const data: { pyramid: PyramidData } = await response.json();
      setReferrals(flattenPyramid(data.pyramid));
    } catch (error) {
      console.error('Error fetching referral pyramid:', error);
      setReferrals([]);
    } finally {
      setLoading(false);
    }
  }, [userId, token, debouncedSearch]);

  // Fetch referrals on mount or search query change
  useEffect(() => {
    fetchReferralPyramid();
  }, [fetchReferralPyramid]);

  /**
   * Flattens the referral pyramid into a single array for easier rendering.
   */
//   const flattenPyramid = (pyramid: PyramidData): Referral[] => {
//     return Object.entries(pyramid).flatMap(([levelKey, referrals]) =>
//       referrals.map((referral) => ({
//         ...referral,
//         level: parseInt(levelKey.replace('level', ''), 10),
//       }))
//     );
//   };


const flattenPyramid = (pyramid: PyramidData): Referral[] => {
    return Object.entries(pyramid).flatMap(([levelKey, referrals]: [string, Referral[]]) =>
      referrals.map((referral: Referral) => ({
        ...referral,
        level: parseInt(levelKey.replace('level', ''), 10), // Extract level number from key
      }))
    );
  };
  
  /**
   * Get a variant for the Badge component based on the referral status.
   */
  const getBadgeVariant = (status: string): 'secondary' | 'default' | 'destructive' | 'outline' => {
    return status === 'Active' ? 'default' : 'destructive';
  };

  /**
   * Render a skeleton row for loading state.
   */
  const renderSkeletonRow = () => (
    <TableRow>
      {[...Array(5)].map((_, index) => (
        <TableCell key={index}>
          <Skeleton className="h-4 w-full" />
        </TableCell>
      ))}
    </TableRow>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Referral Pyramid</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-4">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search referrals"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Level</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Total Investment</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading
                ? Array.from({ length: 5 }, (_, index) => (
                    <TableRow key={index}>{renderSkeletonRow()}</TableRow>
                  ))
                : referrals.length > 0
                ? referrals.map((referral) => (
                    <TableRow key={referral._id}>
                      <TableCell>Level {referral.level}</TableCell>
                      <TableCell>{referral.userId.name}</TableCell>
                      <TableCell>{referral.userId.email}</TableCell>
                      <TableCell>${referral.investmentTotal.toLocaleString()}</TableCell>
                      <TableCell>
                        <Badge variant={getBadgeVariant(referral.status)}>
                          {referral.status}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))
                : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center text-muted-foreground">
                      No referrals found
                    </TableCell>
                  </TableRow>
                )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReferralPyramidTable;
