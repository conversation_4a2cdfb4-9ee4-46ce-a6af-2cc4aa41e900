'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

interface MonthlyPayment {
  _id: string;
  planId: { title: string };
  paymentAmount: number;
  dueDate: string;
  paymentStatus: 'ready' | 'pending' | 'completed';
}

const MonthlyPaymentsTable: React.FC = () => {
  const [payments, setPayments] = useState<MonthlyPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchMonthlyPayments();
  }, [searchQuery]);

  const fetchMonthlyPayments = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(
        `/api/payments/monthly-payments?search=${encodeURIComponent(searchQuery)}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch monthly payments: ${response.statusText}`);
      }

      const data = await response.json();
      setPayments(data.payments);
    } catch (error) {
      console.error('Error fetching monthly payments:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderSkeletonRow = () => (
    <TableRow>
      {[...Array(4)].map((_, index) => (
        <TableCell key={index}>
          <Skeleton className="h-4 w-full" />
        </TableCell>
      ))}
    </TableRow>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Payments</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-4">
          <Input
            type="text"
            placeholder="Search monthly payments"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Plan</TableHead>
                <TableHead>Payment Amount</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading
                ? Array.from({ length: 5 }, () => renderSkeletonRow())
                : payments.length > 0
                ? payments.map((payment) => (
                    <TableRow key={payment._id}>
                      <TableCell>{payment.planId?.title || 'N/A'}</TableCell>
                      <TableCell>${payment.paymentAmount.toLocaleString()}</TableCell>
                      <TableCell>{new Date(payment.dueDate).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            payment.paymentStatus === 'completed' ? 'default' : 'secondary'
                          }
                        >
                          {payment.paymentStatus}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))
                : (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center text-muted-foreground">
                      No monthly payments found
                    </TableCell>
                  </TableRow>
                )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default MonthlyPaymentsTable;
