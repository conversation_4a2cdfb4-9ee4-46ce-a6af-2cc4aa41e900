'use client';

import React, { useState, useEffect } from 'react';

interface ReferralLevel {
  level: number;
  users: { name: string; email: string }[];
}

const UserReferralPyramids: React.FC = () => {
  const [pyramid, setPyramid] = useState<ReferralLevel[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPyramid = async () => {
      try {
        const response = await fetch('/api/user/referral-pyramid');
        const data = await response.json();
        setPyramid(data.pyramid);
      } catch (error) {
        console.error('Error fetching referral pyramid:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPyramid();
  }, []);

  return (
    <div className="p-4 bg-white rounded-md shadow">
      <h3 className="text-lg font-semibold mb-4">Referral Pyramid</h3>
      {loading ? (
        <p>Loading...</p>
      ) : (
        pyramid.map((level, index) => (
          <div key={index} className="mb-4">
            <h4 className="font-semibold">Level {level.level}</h4>
            <ul>
              {level.users.map((user, i) => (
                <li key={i}>{user.name} ({user.email})</li>
              ))}
            </ul>
          </div>
        ))
      )}
    </div>
  );
};

export default UserReferralPyramids;
