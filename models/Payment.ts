// // models/Payment.ts

// import mongoose, { Schema, Document, model, Model } from 'mongoose';
// import { IInvestment } from './Investment';

// export type PaymentStatus = 'ready' | 'pending' | 'completed';

// export interface IPayment extends Document {
//   userId: mongoose.Types.ObjectId;
//   investmentId: mongoose.Types.ObjectId | IInvestment;
//   planId: mongoose.Types.ObjectId;
//   investmentAmount: number;
//   paymentAmount: number;
//   investmentDate: Date;
//   dueDate: Date;
//   paymentStatus: PaymentStatus;
//   createdAt: Date;
//   updatedAt: Date;
// }

// const PaymentSchema: Schema<IPayment> = new Schema<IPayment>(
//   {
//     userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
//     investmentId: { type: Schema.Types.ObjectId, ref: 'Investment', required: true },
//     planId: { type: Schema.Types.ObjectId, ref: 'Plan', required: true },
//     investmentAmount: { type: Number, required: true },
//     paymentAmount: { type: Number, required: true },
//     investmentDate: { type: Date, required: true },
//     dueDate: { type: Date, required: true },
//     paymentStatus: {
//       type: String,
//       enum: ['ready', 'pending', 'completed'],
//       default: 'pending',
//     },
//   },
//   {
//     timestamps: true,
//   }
// );

// export const Payment: Model<IPayment> = mongoose.models.Payment || model<IPayment>('Payment', PaymentSchema);



// models/Payment.ts
import mongoose, { Schema, Document, model, Model } from 'mongoose';
import { IInvestment } from './Investment';

export type PaymentStatus = 'ready' | 'pending' | 'completed';

export interface IPayment extends Document {
  userId: mongoose.Types.ObjectId;
  investmentId: mongoose.Types.ObjectId | IInvestment;
  planId: mongoose.Types.ObjectId;
  investmentAmount: number;
  paymentAmount: number;
  investmentDate: Date;
  dueDate: Date;
  paymentStatus: PaymentStatus;
  createdAt: Date;
  updatedAt: Date;
}

const PaymentSchema: Schema<IPayment> = new Schema<IPayment>(
  {
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    investmentId: { type: mongoose.Schema.Types.ObjectId, ref: 'Investment', required: true },
    planId: { type: mongoose.Schema.Types.ObjectId, ref: 'Plan', required: true },
    investmentAmount: { type: Number, required: true },
    paymentAmount: { type: Number, required: true },
    investmentDate: { type: Date, required: true },
    dueDate: { type: Date, required: true },
    paymentStatus: {
      type: String,
      enum: ['ready', 'pending', 'completed'],
      default: 'pending',
    },
  },
  {
    timestamps: true,
  }
);

/**
 * Suggested indexes:
 * - userId: speed up queries for user payments
 * - investmentId: direct link to the relevant investment
 * - planId: if you want to see all payments for a plan
 * - paymentStatus: quickly find pending vs completed
 * - createdAt: chronological listing
 */
PaymentSchema.index({ userId: 1 });
PaymentSchema.index({ investmentId: 1 });
PaymentSchema.index({ planId: 1 });
PaymentSchema.index({ paymentStatus: 1 });
PaymentSchema.index({ createdAt: -1 });

export const Payment: Model<IPayment> =
  mongoose.models.Payment || model<IPayment>('Payment', PaymentSchema);
