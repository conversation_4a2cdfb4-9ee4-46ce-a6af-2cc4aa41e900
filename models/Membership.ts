
// models/Membership.tsx

import mongoose, { Schema, Document, model } from 'mongoose';

interface ISubscription extends Document {
  userName: string;  // Full name of the subscriber
  userEmail: string;  // Email of the subscriber
  amount: number;  // Amount the user is investing
  plan: mongoose.Schema.Types.ObjectId;  // Reference to the selected plan
  startDate: Date;
  endDate: Date;
}

const SubscriptionSchema: Schema = new Schema({
  userName: { type: String, required: true },  // Full Name of the subscriber
  userEmail: { type: String, required: true },  // Email address
  amount: { type: Number, required: true },  // Amount the user is investing
  plan: { type: Schema.Types.ObjectId, ref: 'Plan', required: true },  // Selected Plan
  startDate: { type: Date, default: Date.now },  // Subscription start date
  endDate: { type: Date, required: true },  // Subscription end date based on the plan duration
});

export const Subscription = mongoose.models.Subscription || model<ISubscription>('Subscription', SubscriptionSchema);
