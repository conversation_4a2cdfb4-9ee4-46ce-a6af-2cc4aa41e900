

// models/MonthlyPayment.ts

import mongoose, { Schema, Document, model, Model } from 'mongoose';
import { IInvestment } from './Investment';
import { IPlan } from './Plan';

export interface IMonthlyPayment extends Document {
  userId: mongoose.Types.ObjectId;
  investmentId: IInvestment; // Assume investmentId is always populated
  planId: IPlan; // Assume planId is always populated
  monthlyPaymentAmount: number; // Specific to monthly payments
  totalMonthsPaid: number; // Number of months paid so far
  startDate: Date; // Start of monthly payments
  nextDueDate: Date; // Next payment due date
  paymentStatus: 'ready' | 'pending' | 'completed';
  createdAt: Date;
  updatedAt: Date;
}

const MonthlyPaymentSchema: Schema<IMonthlyPayment> = new Schema<IMonthlyPayment>(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    investmentId: { type: Schema.Types.ObjectId, ref: 'Investment', required: true },
    planId: { type: Schema.Types.ObjectId, ref: 'Plan', required: true },
    monthlyPaymentAmount: { type: Number, required: true }, // Specific to monthly payments
    totalMonthsPaid: { type: Number, required: true, default: 0 }, // Tracks total months paid
    startDate: { type: Date, required: true }, // Start date of the monthly cycle
    nextDueDate: { type: Date, required: true }, // Next due date
    paymentStatus: {
      type: String,
      enum: ['ready', 'pending', 'completed'],
      default: 'pending',
    },
  },
  {
    timestamps: true, // Automatically manage createdAt and updatedAt
  }
);

export const MonthlyPayment: Model<IMonthlyPayment> =
  mongoose.models.MonthlyPayment || model<IMonthlyPayment>('MonthlyPayment', MonthlyPaymentSchema);
