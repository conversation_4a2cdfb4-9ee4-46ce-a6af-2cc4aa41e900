// // models/Investment.ts
// import mongoose, { Schema, Document, model, Model } from 'mongoose';
// import { ISubscription } from './Subscription';
// import { IPlan } from './Plan';

// export interface IInvestment extends Document {
//   _id: mongoose.Types.ObjectId;  // Explicitly type _id
//   subscriptionId: mongoose.Types.ObjectId | ISubscription;
//   userId: mongoose.Types.ObjectId;
//   planId: mongoose.Types.ObjectId | IPlan;
//   amount: number;
//   earningRate: number;
//   earningPeriod: number;
//   waitPeriod: number;
//   paymentStatus: 'pending' | 'approved' | 'declined' | 'pendingWithNotice' | 'declinedWithReason';
//   paymentMethod: 'creditCard' | 'debitCard' | 'bankTransfer' | 'bankDeposit' | 'mobileMoney' | 'reinvestment';
//   paymentDate: Date;
//   approvalDate?: Date;
//   startDate: Date;
//   endDate: Date;
//   depositSlipUrl?: mongoose.Types.ObjectId;
// }

// const InvestmentSchema = new Schema<IInvestment>({
//   subscriptionId: { type: Schema.Types.ObjectId, ref: 'Subscription', required: true },
//   userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
//   planId: { type: Schema.Types.ObjectId, ref: 'Plan', required: true },
//   amount: { type: Number, required: true },
//   earningRate: { type: Number, required: true, default: 35 },
//   earningPeriod: { type: Number, required: true },
//   waitPeriod: { type: Number, required: true },
//   paymentStatus: {
//     type: String,
//     enum: ['pending', 'approved', 'declined', 'pendingWithNotice', 'declinedWithReason'],
//     default: 'pending',
//   },
//   paymentMethod: {
//     type: String,
//     enum: ['creditCard', 'debitCard', 'bankTransfer', 'bankDeposit', 'mobileMoney', 'reinvestment'],
//     required: true,
//   },
//   paymentDate: { type: Date, required: true },
//   approvalDate: { type: Date },
//   startDate: { type: Date, required: true },
//   endDate: { type: Date, required: true },
//   depositSlipUrl: { type: Schema.Types.ObjectId, ref: 'uploads.files' },
// });

// export const Investment: Model<IInvestment> = mongoose.models.Investment || model<IInvestment>('Investment', InvestmentSchema);



// models/Investment.ts
import mongoose, { Schema, Document, model, Model } from 'mongoose';
import { ISubscription } from './Subscription';
import { IPlan } from './Plan';

/**
 * The IInvestment interface extends Mongoose's Document
 * to ensure strong typing for the fields in your Investment schema.
 */
export interface IInvestment extends Document {
  _id: mongoose.Types.ObjectId;         // The unique MongoDB document _id
  subscriptionId: mongoose.Types.ObjectId | ISubscription;
  userId: mongoose.Types.ObjectId;
  planId: mongoose.Types.ObjectId | IPlan;

  amount: number;                       // The investment amount
  earningRate: number;                  // e.g., 35% monthly
  earningPeriod: number;                // e.g., 12 months
  waitPeriod: number;                   // e.g., 120 days

  paymentStatus: 
    | 'pending' 
    | 'approved' 
    | 'declined' 
    | 'pendingWithNotice' 
    | 'declinedWithReason';

  paymentMethod:
    | 'creditCard'
    | 'debitCard'
    | 'bankTransfer'
    | 'bankDeposit'
    | 'mobileMoney'
    | 'reinvestment';

  paymentDate: Date;
  approvalDate?: Date;
  startDate: Date;
  endDate: Date;

  /**
   * The deposit slip file might be stored via GridFS,
   * so depositSlipUrl references an ObjectId in 'uploads.files' (or wherever your GridFS is stored)
   */
  depositSlipUrl?: mongoose.Types.ObjectId;

  // Mongoose automatically adds createdAt and updatedAt if we use timestamps
  createdAt: Date;
  updatedAt: Date;
}

/**
 * InvestmentSchema definition
 */
const InvestmentSchema = new Schema<IInvestment>(
  {
    subscriptionId: {
      type: Schema.Types.ObjectId,
      ref: 'Subscription',
      required: true,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    planId: {
      type: Schema.Types.ObjectId,
      ref: 'Plan',
      required: true,
    },

    amount: { type: Number, required: true },
    earningRate: { type: Number, default: 35, required: true },
    earningPeriod: { type: Number, required: true },
    waitPeriod: { type: Number, required: true },

    paymentStatus: {
      type: String,
      enum: [
        'pending',
        'approved',
        'declined',
        'pendingWithNotice',
        'declinedWithReason',
      ],
      default: 'pending',
    },
    paymentMethod: {
      type: String,
      enum: [
        'creditCard',
        'debitCard',
        'bankTransfer',
        'bankDeposit',
        'mobileMoney',
        'reinvestment',
      ],
      required: true,
    },

    paymentDate: { type: Date, required: true },
    approvalDate: { type: Date },

    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },

    // depositSlipUrl references a GridFS file (uploads.files collection)
    depositSlipUrl: {
      type: Schema.Types.ObjectId,
      ref: 'uploads.files',
    },
  },
  {
    timestamps: true, // adds createdAt and updatedAt fields
  }
);

/**
 * Indexes for faster queries.
 * Tweak or add more depending on usage patterns.
 */
InvestmentSchema.index({ userId: 1 });
InvestmentSchema.index({ subscriptionId: 1 });
InvestmentSchema.index({ planId: 1 });
InvestmentSchema.index({ paymentStatus: 1 });
InvestmentSchema.index({ createdAt: -1 });

// Optionally, if you frequently query by (userId + paymentStatus):
InvestmentSchema.index({ userId: 1, paymentStatus: 1 });

// Create and export the model.
// If the 'Investment' model already exists, reuse it; otherwise, create a new one.
export const Investment: Model<IInvestment> =
  mongoose.models.Investment ||
  model<IInvestment>('Investment', InvestmentSchema);
