// // models/User.ts
// import bcrypt from 'bcryptjs';
// import mongoose, { Schema, Document, model } from 'mongoose';

// export type UserRole = 'admin' | 'author' | 'editor' | 'member' | 'investor';

// export interface BankDetails {
//   accountName: string;
//   accountNumber: string;
//   bankName: string;
// }

// export interface IUser extends Document {
//   createdAt: Date;
//   updatedAt: Date;
//   email: string;
//   password: string;
//   name: string;
//   phone?: string;
//   membership: mongoose.Types.ObjectId | null;
//   role: UserRole;
//   refreshToken?: string;
//   refreshTokenExpiry?: Date;
//   referrerId?: mongoose.Types.ObjectId | IUser;
//   referralCode: string;
//   bankDetails?: BankDetails;
//   resetCode?: string;
//   resetCodeExpires?: Date;
//   tokenVersion: number;
//   rememberMe: boolean;
//   comparePassword(candidatePassword: string): Promise<boolean>;
// }

// const UserSchema: Schema<IUser> = new Schema(
//   {
//     email: { type: String, required: true, unique: true, index: true },
//     password: { type: String, required: true },
//     name: { type: String, required: true },
//     phone: { type: String, required: false },
//     membership: { type: Schema.Types.ObjectId, ref: 'Membership', default: null },
//     role: {
//       type: String,
//       required: true,
//       enum: ['admin', 'author', 'editor', 'member', 'investor'],
//       default: 'investor',
//     },
//     refreshToken: { type: String, default: null },
//     refreshTokenExpiry: { type: Date, default: null },
//     referrerId: { type: Schema.Types.ObjectId, ref: 'User' },
//     referralCode: { type: String, unique: true, required: true, index: true },
//     bankDetails: {
//       accountName: { type: String, required: false },
//       accountNumber: { type: String, required: false },
//       bankName: { type: String, required: false },
//     },
//     resetCode: { type: String, default: null },
//     resetCodeExpires: { type: Date, default: null },
//     tokenVersion: { type: Number, default: 0 },
//     rememberMe: { type: Boolean, default: false },
//   },
//   { timestamps: true }
// );

// // Middleware to hash passwords and generate referral codes
// UserSchema.pre<IUser>('validate', async function (next) {
//   // Hash the password if it is new or modified
//   if (this.isModified('password') || this.isNew) {
//     this.password = await bcrypt.hash(this.password, 12);
//   }

//   // Generate a unique referral code if it is not already set
//   if (!this.referralCode) {
//     let code: string;
//     let isUnique = false;

//     // Ensure referral code is unique
//     while (!isUnique) {
//       code = Math.random().toString(36).substring(2, 8).toUpperCase();
//       const existingUser = await mongoose.models.User.exists({ referralCode: code });
//       if (!existingUser) {
//         isUnique = true;
//         this.referralCode = code;
//       }
//     }
//   }

//   next();
// });

// // Method to compare passwords
// // UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
// //   return bcrypt.compare(candidatePassword, this.password);
// // };



// UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
//   console.log(`Comparing passwords for user: ${this.email}`);
//   console.log(`Hashed password in DB: ${this.password}`);
//   const isMatch = await bcrypt.compare(candidatePassword, this.password);
//   if (!isMatch) {
//     console.error(`Password comparison failed for user: ${this.email}`);
//   }
//   return isMatch;
// };


// // Ensure indexes for efficient queries
// UserSchema.index({ email: 1 }, { unique: true });
// UserSchema.index({ referralCode: 1 }, { unique: true });
// UserSchema.index({ referrerId: 1 });
// UserSchema.index({ role: 1 });
// UserSchema.index({ membership: 1 });
// UserSchema.index({ createdAt: -1 });
// UserSchema.index({ resetCode: 1 });
// UserSchema.index({ resetCodeExpires: 1 });

// export const User = mongoose.models.User || model<IUser>('User', UserSchema);



// models/User.ts
import bcrypt from 'bcryptjs';
import mongoose, { Schema, Document, model } from 'mongoose';
import crypto from 'crypto';

// Define User Roles
export type UserRole = 'admin' | 'author' | 'editor' | 'member' | 'investor';

// Define Bank Details Interface
export interface BankDetails {
  accountName: string;
  accountNumber: string;
  bankName: string;
}

// Define IUser Interface
export interface IUser extends Document {
  createdAt: Date;
  updatedAt: Date;
  email: string;
  password: string;
  name: string;
  phone?: string;
  membership: mongoose.Types.ObjectId | null;
  role: UserRole;
  refreshTokenHash?: string;
  refreshTokenExpiry?: Date;
  referrerId?: mongoose.Types.ObjectId | IUser;
  referralCode: string;
  bankDetails?: BankDetails;
  resetCode?: string;
  resetCodeExpires?: Date;
  tokenVersion: number;
  rememberMe: boolean;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

// Define User Schema
const UserSchema: Schema<IUser> = new Schema(
  {
    email: { type: String, required: true, unique: true, index: true },
    password: { type: String, required: true },
    name: { type: String, required: true },
    phone: { type: String, required: false },
    membership: { type: Schema.Types.ObjectId, ref: 'Membership', default: null },
    role: {
      type: String,
      required: true,
      enum: ['admin', 'author', 'editor', 'member', 'investor'],
      default: 'investor',
    },
    // Replace 'refreshToken' with 'refreshTokenHash' for security
    refreshTokenHash: { type: String, default: null, index: true },
    refreshTokenExpiry: { type: Date, default: null },
    referrerId: { type: Schema.Types.ObjectId, ref: 'User' },
    referralCode: { type: String, unique: true, required: true, index: true },
    bankDetails: {
      accountName: { type: String, required: false },
      accountNumber: { type: String, required: false },
      bankName: { type: String, required: false },
    },
    resetCode: { type: String, default: null },
    resetCodeExpires: { type: Date, default: null },
    tokenVersion: { type: Number, default: 0 },
    rememberMe: { type: Boolean, default: false },
  },
  { timestamps: true }
);

// Middleware to hash passwords and generate referral codes
UserSchema.pre<IUser>('validate', async function (next) {
  // Hash the password if it is new or modified
  if (this.isModified('password') || this.isNew) {
    this.password = await bcrypt.hash(this.password, 12);
  }

  // Generate a unique referral code if it is not already set
  if (!this.referralCode) {
    let code: string;
    let isUnique = false;
    let attempts = 0;
    const MAX_ATTEMPTS = 5; // Prevent infinite loop

    while (!isUnique && attempts < MAX_ATTEMPTS) {
      code = crypto.randomBytes(3).toString('hex').toUpperCase(); // Generates a 6-character hex code
      const existingUser = await mongoose.models.User.exists({ referralCode: code });
      if (!existingUser) {
        isUnique = true;
        this.referralCode = code;
      }
      attempts++;
    }

    if (!isUnique) {
      throw new Error('Failed to generate a unique referral code. Please try again.');
    }
  }

  next();
});

// Method to compare passwords
UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  const isMatch = await bcrypt.compare(candidatePassword, this.password);
  if (!isMatch) {
    console.error(`Password comparison failed for user: ${this.email}`);
  }
  return isMatch;
};

// Ensure indexes for efficient queries
UserSchema.index({ email: 1 }, { unique: true });
UserSchema.index({ referralCode: 1 }, { unique: true });
UserSchema.index({ referrerId: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ membership: 1 });
UserSchema.index({ createdAt: -1 });
UserSchema.index({ resetCode: 1 });
UserSchema.index({ resetCodeExpires: 1 });
UserSchema.index({ refreshTokenHash: 1 });

// Export User Model
export const User = mongoose.models.User || model<IUser>('User', UserSchema);
