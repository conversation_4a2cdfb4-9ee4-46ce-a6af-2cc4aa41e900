// models/Referrals.ts

import mongoose, { Schema, Document, model } from 'mongoose';
import { IUser } from './User';

export interface IReferral extends Document {
  referrerId: mongoose.Types.ObjectId | IUser; // Populated or ObjectId
  referredUserId?: mongoose.Types.ObjectId | IUser; // Populated or ObjectId
  referralId: string;
  createdAt: Date;
  earnings: {
    investmentAmount: number;
    percentage: number; // e.g., 10
    earningAmount: number;
  }[];
}

const ReferralSchema = new Schema<IReferral>({
  referrerId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  referredUserId: { type: Schema.Types.ObjectId, ref: 'User' },
  referralId: { type: String, required: true, unique: true },
  createdAt: { type: Date, default: Date.now },
  earnings: [
    {
      investmentAmount: { type: Number, required: true },
      percentage: { type: Number, default: 10 },
      earningAmount: { type: Number, required: true },
    },
  ],
});

// Add indexes for efficient querying
ReferralSchema.index({ referrerId: 1 });
ReferralSchema.index({ referredUserId: 1 });
ReferralSchema.index({ referralId: 1 }, { unique: true });
ReferralSchema.index({ createdAt: -1 });

export const Referral =
  mongoose.models.Referral || model<IReferral>('Referral', ReferralSchema);