/** @type {import('next').NextConfig} */

// Detect if we're in production or Vercel environment
const isProduction = process.env.NODE_ENV === 'production';
const isVercel = process.env.VERCEL === '1';
const isDeployment = isProduction || isVercel;

console.log('🔧 Next.js Build Configuration:');
console.log(`   Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`   Vercel: ${isVercel ? 'Yes' : 'No'}`);
console.log(`   TypeScript: ${isDeployment ? 'Relaxed (Production)' : 'Strict (Development)'}`);
console.log(`   Config: ${isDeployment ? 'tsconfig.prod.json' : 'tsconfig.json'}`);

const nextConfig = {
  typescript: {
    // Use different TypeScript config for production builds
    tsconfigPath: isDeployment ? './tsconfig.prod.json' : './tsconfig.json',

    // Ignore TypeScript errors during production build (Vercel deployment)
    ignoreBuildErrors: isDeployment,
  },

  eslint: {
    // Ignore ESLint errors during production build (Vercel deployment)
    ignoreDuringBuilds: isDeployment,
  },
};

export default nextConfig;
