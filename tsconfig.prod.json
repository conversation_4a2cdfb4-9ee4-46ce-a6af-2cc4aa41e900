{"extends": "./tsconfig.json", "compilerOptions": {"strict": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnreachableCode": true, "allowUnusedLabels": true, "skipLibCheck": true}}